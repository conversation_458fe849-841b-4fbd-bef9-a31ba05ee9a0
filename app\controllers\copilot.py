import json
import logging
import os
from datetime import datetime
import httpx
from fastapi import Depends, HTTPException, Request
from fastapi import APIRouter
import hashlib
import hmac
import base64
from urllib.parse import quote
import time

from app.utils.blob import save_file_to_blob, generate_blob_sas_url
from app.services.copilot_service import CopilotService
from app.models.copilot_summary import CopilotSummary
from app.services.account_service import AccountService
from app.core.database import get_db
from app.utils.word import Word
from app.core.global_var import GlobalVar
from app.utils.crypt import Crypt

word = Word()
router = APIRouter()

# 智能病历助理-摘要生成
@router.post("/chat/all_messages")
async def chat_stream(request: Request, db = Depends(get_db)):
    body = await request.json()
    last_message = body["messages"]
    channel_no = body.get("channel_no")
    clinic_no = body.get("clinic_no")
    copilot_type = body.get("copilot_type", "")
    logging.info(f"Received JSON: {body}")
    if not clinic_no:
        return {
            "code": 10100,
            "message": "门诊号不能为空",
            "data": {}
        }

    device_id = request.headers.get("DEVICE-ID")
    if not device_id:
        device_id = ""

    language = GlobalVar.get_var("language")

    if copilot_type == "copilot_ikang":
        channel_no = "copilot_ikang"

    category = "copilot"
    user_id = "copilot"
    if language == 'en-US':
        category = "copilot_en"
        user_id = "copilot_en"
    if copilot_type == "copilot_ikang":
        category = "copilot_ikang"
        user_id = "copilot_ikang"

    required_api_key = 'ts-mftR3Z6KMgsi2PmQrc6MA4EFaiQ6sguvG4ZqtMo63SpYfeCtEnQ8'
    llm_api_addr = 'https://llm.higrace.life:8080/api/v1/chat/add_message'
    headers = {
        "X-Forwarded-By": "ts_llm_service",
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": required_api_key
    }
    body = {
        "user": {
            "id": user_id,
            "device": "D2E8091F-5973-44E0-B47A-117713F760A0"
        },
        "agent": {
            "name": "grace",
            "role": "nurse"
        },
        "event": {
            "user_contents": [
                {
                    "text": last_message
                }
            ],
            "type": "text",
            "category": category
        },
        "scene": {
            "id": "e2f58fca-62fc-4754-b400-2bc3e90898e9",
            "source": "ios"
        }
    }
    timeout = httpx.Timeout(
        connect=10.0,  # 连接超时（秒）
        read=60.0,  # 读取超时（秒）
        write=10.0,  # 写入超时（秒）
        pool=None  # 连接池超时（秒），可以设置为None以使用默认值
    )
    async with httpx.AsyncClient(timeout=timeout) as client:
        upstream_response = await client.post(llm_api_addr, headers=headers, json=body)
        logging.info(f"upstream response: {upstream_response}")
        if upstream_response.status_code == 200:
            body = json.loads(upstream_response.text)
            event = body["data"]["event"]
            answer = event["agent_contents"][0]["text"]

    # 将响应内容生成word文档
    event_id = event["id"] # 事件ID
    channel_list = ["huilongguan_hospital", "copilot_ikang"]
    if channel_no and channel_no in channel_list:
        channel_no = channel_no #渠道编号
    else:
        channel_no = "other"

    now = datetime.now()
    ymd = now.strftime("%Y-%m-%d")
    target_file = f"/{channel_no}/{ymd}/{event_id}_{channel_no}_{ymd}.docx"
    local_file = word.text_to_word(answer, target_file)

    target_file = 'copilot_summary' + target_file
    meta_data = {"file_type": "docx"}
    remote_file_url = await save_file_to_blob(local_file, target_file, meta_data)
    summary_file = {"blob_name": target_file}

    async with db as session:
        copilot_service = CopilotService(session)
        data = CopilotSummary(
            account_id=0,
            channel_no=channel_no,
            device_id=device_id,
            clinic_no=clinic_no,
            voice_text=last_message,
            summary=answer,
            summary_file=summary_file
        )
        copilot_summary_info = await copilot_service.create_copilot_summary(data)

    crypt_key = os.getenv("CRYPT_KEY")
    copilot_id = Crypt.encrypt(str(copilot_summary_info.id), crypt_key)
    response_data = {
        "code": 0,
        "message": {
            "answer": answer,
            "copilot_id": copilot_id,
        }
    }

    return response_data


# 智能病历存储的word文件，通过接口获取文件列表
@router.post("/api/v1/get-summary-list")
async def get_filelist(request: Request, db = Depends(get_db)):
    body = await request.json()
    page = body.get("page", 1)
    if page < 1:
        page = 1
    logging.info(f"Received JSON: {body}")

    token = request.headers.get("CA-TOKEN")
    if not token:
        return {"code": 108003, "message": "登录状态失效，请重新登录"}
    account_service = AccountService()
    account_info = account_service.check_token(token)
    if not account_info:
        return {"code": 108003, "message": "登录状态失效，请重新登录"}

    channel_no = 'other'
    if account_info["account_id"] == 1:
        channel_no = 'huilongguan_hospital'
    elif account_info["account_id"] == 2:
        channel_no = 'copilot_ikang'
    
    limit = 50
    start = (page - 1) * limit
    async with db as session:
        copilot_service = CopilotService(session)
        summary_list = await copilot_service.fetch_all_by_channel_no(channel_no, start, limit)

    file_list = []
    for summary in summary_list:
        file_url = summary.summary_file
        file_url = generate_blob_sas_url(file_url["blob_name"])
        data = {
            "file_url": file_url,
            "clinic_no": summary.clinic_no,
            "date_time": summary.created
        }
        file_list.append(data)

    response_data = {
        "code": 0,
        "message": "获取成功",
        "filelist": file_list
    }

    return response_data


@router.get("/api/v1/get-speech-token")
@router.post("/api/v1/get-speech-token")
async def get_speech_token():
    speech_key = os.getenv('SPEECH_KEY')
    speech_region = os.getenv('SPEECH_REGION')

    if speech_key is None or speech_region is None:
        raise HTTPException(status_code=400, detail="You forgot to add your speech key or region to the .env file.")

    headers = {
        'Ocp-Apim-Subscription-Key': speech_key,
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    try:
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                f"https://{speech_region}.api.cognitive.microsoft.com/sts/v1.0/issueToken", headers=headers)
            token_response.raise_for_status()  # 检查响应状态码是否为 200

            return {"token": token_response.text, "region": speech_region}

    except httpx.HTTPStatusError:
        raise HTTPException(status_code=401, detail="There was an error authorizing your speech key.")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@router.post("/api/v1/get_xf_rtasr_url")
async def get_xf_rtasr_url():
    appid = os.getenv('XF_RTASR_APPID')
    api_key = os.getenv('XF_RTASR_API_KEY')

    if appid is None:
        raise HTTPException(status_code=400, detail="You forgot to add key to the .env file.")

    base_url = "wss://rtasr.xfyun.cn/v1/ws"
    ts = str(int(time.time()))
    pd = "medical"
    roleType = "2"
    tt = (appid + ts).encode('utf-8')
    md5 = hashlib.md5()
    md5.update(tt)
    base_string = md5.hexdigest()
    base_string = bytes(base_string, encoding='utf-8')

    apiKey = api_key.encode('utf-8')
    signa = hmac.new(apiKey, base_string, hashlib.sha1).digest()
    signa = base64.b64encode(signa)
    signa = str(signa, 'utf-8')

    url = base_url + "?appid=" + appid + "&ts=" + ts + "&pd=" + pd + "&roleType=" + roleType + "&signa=" + quote(signa)

    return {"url": url}