from fastapi import APIRouter, HTTPException, Request
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel, Field
import traceback
import json

from app.core.database import get_db
from app.models.user import User
from app.core.decorators import require_user_token
from app.schemas.base import APIResponse
from app.services.user_service import UserService
from app.core.logger import main_logger


router = APIRouter()

@router.get("/{user_id}")
async def get_user(user_id: str, db: AsyncSession = Depends(get_db)):
    async with db as session:
        result = await session.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()

    return {
        "code": 0,
        "message": _("成功"),
        "data": user
    }

#刷脸图片质量检测
class DetectFaceRequest(BaseModel):
    image: str = Field(..., description="Base64 encoded image data")
@router.post("/detect_face")
async def detect_face(request: Request, detect_face_request: DetectFaceRequest):
    image_base64 = detect_face_request.image
    if not image_base64:
        return APIResponse(code=10071, message=_("刷脸失败"))
    
    user_service = UserService(request)
    image_base64_list = {
        "image1": image_base64,
    }

    try:
        best_image_index, best_result = await user_service.facepp_best_detect(image_base64_list)
    except Exception as e:
        error_stack = traceback.format_exc()
        error_info = {"what": "dectct_face error", "msg": f"{e}", "error_info": error_stack}
        main_logger.error(json.dumps(error_info))
        return APIResponse(code=10072, message=_("请将脸部正对摄像头"))
    
    data = {
        "face_token": best_result["faces"][0]["face_token"]
    }

    return APIResponse(code=0, message="success", data=data)


#用户刷脸登录接口
class FaceLoginRequest(BaseModel):
    image: str = Field(..., description="Base64 encoded image data")
@router.post("/face_login")
async def face_login(login_request: FaceLoginRequest, request: Request):

    main_logger.info("facepp_login start")

    account_id = 1

    image1_base64 = login_request.image
    if not image1_base64:
        return APIResponse(code=10080, message=_("刷脸失败"))
    
    if not account_id:
        return APIResponse(code=10080, message=_("没有账号信息"))
    account_info = {"account_id": account_id}
    request.state.account = account_info
    
    user_service = UserService(request)
    image_base64_list = {
        "image1": image1_base64,
    }

    token = None
    try:
        user_id, token = await user_service.facepp_login(image_base64_list)
    except Exception as e:
        error_stack = traceback.format_exc()
        error_info = {"what": "face_login error", "msg": f"{e}", "error_info": error_stack}
        main_logger.error(json.dumps(error_info))

    if not token:
        return APIResponse(code=10081, message=_("刷脸失败"))
    
    user_info = await user_service.get_user_by_user_id(user_id)

    birthday = user_info["birthday"]
    if not birthday:
        is_need_complete = True
        info = {}
    else:
        is_need_complete = False
        info = {
            "name": user_info["name"],
            "gender": user_info["gender"],
            "age": user_service.get_age_by_birthday(birthday),
            "grade": user_service.convert_grade_to_text(user_info["grade"]),
            "birthday": birthday,
        }

    user_extra_json = user_info["extra_json"]
    qs_result_id = user_extra_json.get("qs", 0)
    if qs_result_id:
        is_need_qs = False
    else:
        is_need_qs = True

    data_info = {
        "token": token,
        "is_need_complete": is_need_complete,
        "is_need_qs": is_need_qs,
        "info": info,
    }

    return APIResponse(
        code=0, message=_("登录成功"), data=data_info
    )

# 用户名密码登录
class PwLoginRequest(BaseModel):
    username: str
    password: str
@router.post("/pw_login")
async def pw_login(request: Request, login_request: PwLoginRequest):

    account_id = 1
    username = login_request.username
    password = login_request.password

    try:
        user_service = UserService()
        user_service.account_id = account_id
        result = await user_service.pw_login(username, password)
    except Exception as e:
        error_stack = traceback.format_exc()
        error_info = {"what": "pw_login error", "msg": f"{e}", "error_info": error_stack}
        main_logger.error(json.dumps(error_info))
        result = None

    if not result:
        return APIResponse(code=10087, message=_("登录失败"))

    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    birthday = data["info"]["birthday"]
    if not birthday:
        is_need_complete = True
        info = {}
    else:
        is_need_complete = False
        info = {
            "name": data["info"]["name"],
            "gender": data["info"]["gender"],
            "age": user_service.get_age_by_birthday(birthday),
            "grade": user_service.convert_grade_to_text(data["info"]["grade"]),
            "birthday": birthday,
        }

    user_extra_json = data['info']["extra_json"]
    qs_result_id = user_extra_json.get("qs", 0)
    if qs_result_id:
        is_need_qs = False
    else:
        is_need_qs = True

    data_info = {
        "token": data["token"],
        "is_need_complete": is_need_complete,
        "is_need_qs": is_need_qs,
        "info": info,
    }

    return APIResponse(
        code=0, message=_("登录成功"), data=data_info
    )

#用户完善信息接口
class UpdateProfilerRequest(BaseModel):
    name: str = Field(..., description="name")
    gender: int
    birthday: str
    grade: int
@router.post("/update_profiler")
@require_user_token()
async def update_profiler(request: Request, update_request: UpdateProfilerRequest):

    current_user_info = request.state.user
    current_account_info = request.state.account
    name = update_request.name
    gender = update_request.gender
    birthday = update_request.birthday
    grade = update_request.grade

    user_service = UserService(request)

    if len(name) > 20 or len(name) <= 0:
        return APIResponse(code=10085, message=_("姓名不符合规范，请重新输入"))
    if gender not in [0, 1, 2]:
        return APIResponse(code=10086, message=_("更新用户信息失败"))
    try:
        age = user_service.get_age_by_birthday(birthday)
        if age < 0 or age > 150:
            return APIResponse(code=10087, message=_("更新用户信息失败"))
    except:
        return APIResponse(code=10088, message=_("更新用户信息失败"))

    update_user_info = {
        "name": name,
        "gender": gender,
        "birthday": birthday,
        "grade": grade
    }

    result = await user_service.update_user_info(current_user_info["user_id"], update_user_info)

    if not result:
        return APIResponse(code=10083, message=_("更新用户信息失败"))

    user_info = await user_service.get_user_by_user_id(current_user_info["user_id"])
    info = {
        "name": user_info["name"],
        "gender": user_info["gender"],
        "age": user_service.get_age_by_birthday(user_info["birthday"]),
        "grade": user_service.convert_grade_to_text(user_info["grade"]),
        "birthday": user_info["birthday"],
    }
    response_info = {
        "info": info,
    }

    return APIResponse(
        code=0, message=_("更新用户信息成功"), data=response_info
    )