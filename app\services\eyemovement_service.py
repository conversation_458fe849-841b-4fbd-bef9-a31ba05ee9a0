from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from datetime import datetime, timedelta
import logging
from sqlalchemy.exc import SQLAlchemyError
import httpx
import os

from app.models.screening_info import ScreeningInfo
from app.services.screening_info_service import ScreeningInfoService
from app.services.screening_detail_service import ScreeningDetailService
from app.services.eyemovement.detail_generator import generate_detail, calc_score
from app.services.eyemovement.result_generator import generate_result
from app.core.database import get_db

logger = logging.getLogger(__name__)

class EyemovementService:
    def __init__(self, db_session: AsyncSession = None):
        self.db_session = get_db

    async def list_eyemovement_report_by_device_id(self, device_id_list, start, limit):
        try:
            async with self.db_session() as session:
                conditions = [
                    ScreeningInfo.device_id.in_(device_id_list),
                ]
                query = select(ScreeningInfo).where(and_(*conditions)).offset(start).limit(limit)
                order = desc(ScreeningInfo.created_at)
                query = query.order_by(order)
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return []
    
    async def get_screening_info_list_by_user_ids(self, user_ids, start_time = None, end_time = None, start = 0, limit = 20):
        try:
            async with self.db_session() as session:
                conditions = [
                    ScreeningInfo.user_id.in_(user_ids),
                ]
                if start_time:
                    conditions.append(ScreeningInfo.created_at >= start_time)
                if end_time:
                    conditions.append(ScreeningInfo.created_at < end_time)
                query = select(ScreeningInfo).where(and_(*conditions)).offset(start).limit(limit)
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in get_screening_info_list_by_user_ids: {str(e)}")
            raise

        return []


    async def new_screening_info(self, screening_info):
        screening_info_service = ScreeningInfoService()
        return await screening_info_service.new_screening_info(screening_info)
    
    async def get_screening_info(self, id):
        screening_info_service = ScreeningInfoService()
        return await screening_info_service.get_screening_info_by_id(id)
    
    async def new_screening_detail(self, screening_detail):
        screening_detail_service = ScreeningDetailService()
        return await screening_detail_service.new_screening_detail(screening_detail)

    async def get_screening_detail_by_screening_info_id(self, screening_info_id):
        creening_detail_service = ScreeningDetailService()
        return await creening_detail_service.get_screening_detail_by_screening_info_id(screening_info_id)

    def generate_detail(self, details):
        return generate_detail(details)
    
    def generate_result(self,details):
        return generate_result(details)
    def calc_score(self, details):
        return calc_score(details)
    
    async def update_screening_report(self, screening_info_id, report):
        update_data = {
            "report": report,
        }
        screening_info_service = ScreeningInfoService()
        await screening_info_service.update_screening_info_by_id(screening_info_id, update_data)
