
# 语言能力评估项目1处理
class HandlerLanguage1:
    def __init__(self) -> None:
        pass

    # 通过收集评测的数据，计算需要的报告等数据
    def cal_result_info(self, collect_data):
        collect_data_sample = {
            "start_time": "",
            "run_data": [ # 每轮数据
                {
                    "cost_time": "",
                },
            ],
            "total_score": 5,
            "cost_time": "",
        }

    # 通过语音分析
    def analyze(self, resource_info):
        pass