import gettext
import os
from fastapi import Request
from functools import lru_cache

DOMAIN = 'myapp'
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOCALE_DIR = os.path.join(BASE_DIR, 'locale')

@lru_cache(maxsize=128)
def get_translation(language: str) -> gettext.NullTranslations:
    try:
        translation = gettext.translation(DOMAIN, localedir=LOCALE_DIR, languages=[language], fallback=True)
        translation.install()
        return translation
    except FileNotFoundError:
        return gettext.NullTranslations()

DEFAULT_LANGUAGE = 'zh-CN'

def set_language(language: str) -> None:
    translation = get_translation(language)
    import builtins
    builtins._ = translation.gettext

def get_language_from_request(request: Request, default: str = DEFAULT_LANGUAGE) -> str:
    language = request.query_params.get('lang', '')
    if not language:
        language = request.headers.get('Accept-Language', default)
    if language and '_' in language:
        language = language.replace('_', '-')
    if language not in ["zh-CN", "en-US", "en", "zh"]:
        language = default
    if language == 'en':
        language = "en-US"
    elif language == 'zh':
        language = "zh-CN"
    return language