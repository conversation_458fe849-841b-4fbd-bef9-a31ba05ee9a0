from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from datetime import datetime

from app.models.base import Base, TimestampMixin, ModelUpdateMixin

class TrainingItemPlan(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'training_item_plans'

    train_item_plan_id = Column(Integer, primary_key=True)
    category = Column(String(100), default="")
    train_plan_result_id = Column(Integer, default=0)
    train_item_id = Column(Integer, default=0)
    user_id = Column(Integer, default=0)
    account_id = Column(Integer, default=0)
    config_json = Column(JSON, default=dict)
    start_time = Column(DateTime, default=None)
    end_time = Column(DateTime, default=None)
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<TrainingItemPlan(train_item_plan_id={self.train_item_plan_id}, category={self.category}, user_id={self.user_id})>"
