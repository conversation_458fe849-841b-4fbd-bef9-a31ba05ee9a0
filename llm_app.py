# Standard library imports
import base64
import json
import logging
import os
import time
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict
import traceback


# Third-party imports
import dotenv
import httpx
import uvicorn
from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Request, Response, UploadFile, File
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.future import select
from sqlalchemy.orm import sessionmaker
from sse_starlette.sse import EventSourceResponse
from starlette.concurrency import run_in_threadpool
from datetime import datetime, timedelta
from pydub import AudioSegment


# Local application imports
from app.models.appconfig import AppConfig
from app.models.message import Message
from app.models.screening import Screening
from app.rppg.hrv_processor import HrvProcessor
from app.emotion.emotion_processor import EmotionProcessor
from app.schemas.emotion import EmotionRequest, EmotionResponse
from app.schemas.speech import AudioAnalysisRequest
from app.utils.oss import Oss
from app.utils.speech import Speech
from app.utils.word import Word
from app.utils.file import File as FileHandler
from app.utils.blob import save_file_to_blob, generate_blob_sas_url
from app.services.copilot_service import CopilotService
from app.services.speech_service import SpeechService
from app.models.copilot_summary import CopilotSummary
from app.services.account_service import AccountService
from app.core.database import get_db
from app.controllers.user import router as user_router
from app.controllers.h5wechat import router as h5wechat_router
from app.controllers.copilot import router as copilot_router
from app.controllers.account import router as account_router
from app.controllers.tsc102 import router as tsc102_router
from app.controllers.evaluation import router as evaluation_router
from app.controllers.upload import router as upload_router
from app.controllers.training import router as training_router
from app.controllers.quick_screening import router as quick_screening_router
from app.controllers.improve_learning import router as improve_learning_router
from app.controllers.api_external import router as api_external_router
from app.core.i18n import set_language, get_language_from_request
from app.core.global_var import GlobalVar

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d [%(levelname).1s] [%(threadName)s] ( %(name)s:%(lineno)d) - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

dotenv.load_dotenv()

# 配置 CORS
origins = [
    "*",
    "http://localhost",
    "http://localhost:3000",
    "http://localhost:5173",
    "http://**************:5173",
    "http://llm.airdoc.local:3000",
    "https://grace.ybbywb.com"
]

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.middleware("http")
async def set_language_middleware(request: Request, call_next):
    language = get_language_from_request(request)
    GlobalVar.set_var("language", language)
    set_language(language)
    response = await call_next(request)
    return response

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors(), "body": exc.body}
    )

app.include_router(user_router, prefix="/api/v1/user", tags=["users"])
app.include_router(account_router, prefix="", tags=["accounts"])
app.include_router(h5wechat_router, prefix="", tags=["h5wechat"])
app.include_router(copilot_router, prefix="", tags=["copilot"])
app.include_router(tsc102_router, prefix="", tags=["tsc102"])
app.include_router(evaluation_router, prefix="/api/v1/evaluation", tags=["learning_ability"])
app.include_router(training_router, prefix="/api/v1/training", tags=["learning_ability"])
app.include_router(upload_router, prefix="/api/v1/upload", tags=["upload"])
app.include_router(quick_screening_router, prefix="/api/v1/quick_screening", tags=["learning_ability"])
app.include_router(improve_learning_router, prefix="/api/v1/improve_learning", tags=["learning_ability"])
app.include_router(api_external_router, prefix="/api/v1/api_e", tags=["api_external"])


if __name__ == "__main__":
    app_port = int(os.getenv("APP_PORT"))
    uvicorn.run(app, host="0.0.0.0", port=app_port)
