from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from datetime import datetime

from app.models.base import Base, TimestampMixin

class ScreeningInfo(Base):
    __tablename__ = 'screening_info'

    id = Column(String(255), primary_key=True)
    profile_id = Column(String(255), default='')
    user_id = Column(Integer, default=0)
    category = Column(String(255), default='')
    device_id = Column(String(255), default='')
    report = Column(Text, default='')
    status = Column(Integer, default=1)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<ScreeningInfo(id={self.id}, device_id={self.device_id}, status={self.status})>"
