from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.quick_screening_answer import QuickScreeningAnswer

class QuickScreeningAnswerService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_quick_screening_answer(self, new_quick_screening_answer):
        try:
            async with self.db_session() as session:
                session.add(new_quick_screening_answer)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_quick_screening_answer: {str(e)}")
            raise

        return new_quick_screening_answer

    async def get_answer_list_by_qs_result_id(self, qs_result_id):
        try:
            async with self.db_session() as session:
                conditions = [
                    QuickScreeningAnswer.qs_result_id == qs_result_id,
                ]
                stmt = select(QuickScreeningAnswer).where(and_(*conditions))
                result = await session.execute(stmt)
                answer_list = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_answer_list_by_qs_result_id: {str(e)}")
            raise

        return answer_list
