from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from typing import Any, Dict, Optional
from app.models.base import Base, TimestampMixin, ModelUpdateMixin


class AccountDevice(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'account_devices'

    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, default=0)
    device_id = Column(String(100), default="")
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<AccountDevice(account_id={self.account_id}, id={self.id}, device_id={self.device_id})>"
