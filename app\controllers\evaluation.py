from fastapi import APIRouter, HTTPException, Request
from fastapi import Depends
from pydantic import BaseModel, Field
import traceback
import json
from typing import Any, Dict

from app.core.decorators import require_user_token
from app.schemas.base import APIResponse
from app.services.user_service import UserService
from app.core.logger import main_logger
from app.services.evaluation_service import EvaluationService
from app.core.global_var import GlobalVar


router = APIRouter()

@router.post("/list")
@require_user_token()
async def list(request: Request):

    current_user_info = request.state.user
    current_account_info = request.state.account

    evaluation_service = EvaluationService()
    list = await evaluation_service.get_category_list(current_user_info["user_id"])
    return APIResponse(
        code=0, message=_("获取成功"), data=list
    )

class RestartRequest(BaseModel):
    category: str = Field(..., description="category key")
@router.post("/restart_evaluation")
@require_user_token()
async def list(request: Request, restart_request: RestartRequest):
    current_user_info = request.state.user
    current_account_info = request.state.account
    
    category = restart_request.category

    try:
        evaluation_service = EvaluationService()
        result = await evaluation_service.restart_evaluation(category, current_user_info["user_id"], current_account_info["account_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10083, message=_("重新评估处理失败"))

    return APIResponse(
        code=0, message=_("重新评估处理成功"), data={}
    )


# 获取分类下的评估项目，当前只有一个项目，直接返回第一个项目的数据
class ItemListRequest(BaseModel):
    category: str = Field(..., description="category key")
@router.post("/get_item_list")
@require_user_token()
async def get_item_list(request: Request, item_list_request: ItemListRequest):

    current_user_info = request.state.user
    current_account_info = request.state.account
    
    category = item_list_request.category

    try:
        evaluation_service = EvaluationService()
        result = await evaluation_service.get_item_list_by_category(category, current_user_info["user_id"], current_account_info["account_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10083, message=_("获取信息失败"))

    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    return APIResponse(
        code=0, message=_("获取成功"), data=data
    )

# 开始评估项目
class StartItemRequest(BaseModel):
    item_result_id: int = Field(..., description="evaluation item ID")
@router.post("/start_item")
@require_user_token()
async def start_item(request: Request, start_item_request: StartItemRequest):

    current_user_info = request.state.user
    current_account_info = request.state.account

    item_result_id = start_item_request.item_result_id

    try:
        evaluation_service = EvaluationService()
        result = await evaluation_service.start_item(item_result_id, current_user_info["user_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10085, message=_("开始评估项目失败"))
    
    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)


    return APIResponse(
        code=0, message=_("开始成功"), data=data
    )

# 结束评估项目
class EndItemRequest(BaseModel):
    item_result_id: int = Field(..., description="evaluation item ID")
    item_collect_data: Dict[str, Any]
@router.post("/end_item")
@require_user_token()
async def end_item(request: Request, end_item_request: EndItemRequest):

    current_user_info = request.state.user
    current_account_info = request.state.account

    item_result_id = end_item_request.item_result_id
    item_collect_data = end_item_request.item_collect_data

    try:
        evaluation_service = EvaluationService()
        evaluation_finish_info = await evaluation_service.end_item(item_result_id, current_user_info["user_id"], item_collect_data)
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        evaluation_finish_info = None

    if not evaluation_finish_info:
        return APIResponse(code=10087, message=_("评估结束失败"))

    code = evaluation_finish_info.get("code", 0)
    msg = evaluation_finish_info.get("msg", "")
    data = evaluation_finish_info.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    return APIResponse(
        code=0, message=_("结束成功"), data=data
    )

class ReportRequest(BaseModel):
    result_id: int = Field(..., description="evaluation ID")
@router.post("/get_report")
@require_user_token()
async def get_report(request: Request, report_request: ReportRequest):

    current_user_info = request.state.user
    current_account_info = request.state.account

    result_id = report_request.result_id

    try:
        evaluation_service = EvaluationService()
        report_info = await evaluation_service.get_report_info(result_id, current_user_info["user_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        report_info = None

    if not report_info:
        return APIResponse(code=10083, message=_("获取报告失败"))

    code = report_info.get("code", 0)
    msg = report_info.get("msg", "")
    data = report_info.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)


    return APIResponse(
        code=0, message=_("获取报告成功"), data=data
    )