from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from app.models.base import Base, TimestampMixin, ModelUpdateMixin


class QuickScreeningQuestion(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'quick_screening_questions'

    question_id = Column(Integer, primary_key=True)
    title = Column(String(100), default="")
    desc = Column(String(2000), default="")
    content_json = Column(JSON, default=dict)
    key = Column(String(100), default="")
    type = Column(String(100), default="")
    category = Column(String(100), default="")
    part = Column(String(100), default="")
    option_json = Column(JSON, default=dict)
    ref_answer_json = Column(JSON, default=dict)
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<QuickScreeningQuestion(question_id={self.question_id}, title={self.title}, key={self.key}, category={self.category}, part={self.part})>"
