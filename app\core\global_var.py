import contextvars

# 全局变量，主要用于上下文数据
class GlobalVar:
    _instance = None
    _global_var = contextvars.ContextVar("global_var")
    
    @classmethod
    def get_var(cls, key):

        try:
            global_info = cls._global_var.get()
        except LookupError:
            cls._global_var.set({})
            global_info = cls._global_var.get()

        return global_info.get(key, None)
    
    @classmethod
    def set_var(cls, key, value):
        try:
            global_info = cls._global_var.get()
        except LookupError:
            cls._global_var.set({})
            global_info = cls._global_var.get()

        global_info[key] = value
        cls._global_var.set(global_info)