import numpy as np


class PosProcessor():
    def __init__(self, winsize=45):
        self.n = 0
        self.winsize = winsize
        self.rmean, self.gmean, self.bmean = 0, 0, 0
        self.hs = []
        self.name = None
        self.rs = []
        self.gs = []
        self.bs = []
        self.vs = []

    @staticmethod
    def moving_average_update(xold, xs, winsize):
        if len(xs) == 0:
            return np.nan
        # print(f"moving_average_update, winsize: {winsize}")
        return np.nanmean(xs[-winsize:])

    def process(self, rgb):
        v = self.calculate(rgb)
        self.vs.append(v)
        return v

    def calculate(self, rgb):
        self.n += 1
        self.rs.append(rgb[0])
        self.gs.append(rgb[1])
        self.bs.append(rgb[2])

        # spatial averaging
        self.rmean = self.moving_average_update(self.rmean, self.rs, self.winsize)
        self.gmean = self.moving_average_update(self.gmean, self.gs, self.winsize)
        self.bmean = self.moving_average_update(self.bmean, self.bs, self.winsize)

        # print(f"f:{len(self.rs):2d}, r={self.rmean:6.2f}, g={self.gmean:6.2f}, b={self.bmean:6.2f}, roi={roi_pixels}")

        if self.n >= self.winsize:
            # temporal normalization
            rn = np.divide(self.rs[-self.winsize:], self.rmean or 1.)
            gn = np.divide(self.gs[-self.winsize:], self.gmean or 1.)
            bn = np.divide(self.bs[-self.winsize:], self.bmean or 1.)

            # projection
            s1 = gn - bn
            s2 = -2 * rn + gn + bn

            # tuning
            h = s1 + np.nanstd(s1) / np.nanstd(s2) * s2
            self.hs.append(0.)
            self.hs[-self.winsize:] = self.hs[-self.winsize:] + (h-np.nanmean(h))
            # print(f"calculated update: {self.hs}")
            # print(f"calculated return: {self.hs[-self.winsize]:6.5f}")
            return self.hs[-self.winsize]
        self.hs.append(0)
        # print(f"calculated return: 0")
        return 0

    def __str__(self):
        if self.name is None:
            return "PosProcessor(winsize={})".format(self.winsize)
        return self.name
