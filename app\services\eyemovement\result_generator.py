import json

from app.services.eyemovement.common import is_valid_pos, calc_distance

BIAS = 20


def generate_result(details):
    results = []
    for detail in details:
        if detail.category == 'EM':
            if detail.name == 'test1':
                result1 = generate_em_test1_result(json.loads(detail.detail))
                results.append({'name': detail.name, 'result': result1})
            elif detail.name == 'test2':
                result2 = generate_em_test2_result(json.loads(detail.detail))
                results.append({'name': detail.name, 'result': result2})
            elif detail.name == 'test3':
                result3 = generate_em_test3_result(json.loads(detail.detail))
                results.append({'name': detail.name, 'result': result3})
            elif detail.name == 'test4':
                result4 = generate_em_test4_result(json.loads(detail.detail))
                results.append({'name': detail.name, 'result': result4})
            elif detail.name == 'test5':
                result5 = generate_em_test5_result(json.loads(detail.detail))
                results.append({'name': detail.name, 'result': result5})
            elif detail.name == 'test6':
                result6 = generate_em_test6_result(json.loads(detail.detail))
                results.append({'name': detail.name, 'result': result6})
            elif detail.name == 'test7':
                result7 = generate_em_test7_result(json.loads(detail.detail))
                results.append({'name': detail.name, 'result': result7})
            elif detail.name == 'test8':
                result8 = generate_em_test8_result(json.loads(detail.detail))
                results.append({'name': detail.name, 'result': result8})
    return results


def generate_em_test1_result(meta):
    gazes = []
    for i in range(0, len(meta['stages'])):
        stage = meta['stages'][i]
        gazes.append({
            'data': []
        })
        for j in range(0, len(stage['data'])):
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            gazes[i]['data'].append({
                'x': stage['data'][j]['ax'],
                'y': stage['data'][j]['ay'],
                't': 1
            })

    return {
        'config': meta['config'],
        'gazes': gazes
    }


def generate_em_test2_result(meta):
    gazes = []
    for i in range(0, len(meta['stages'])):
        stage = meta['stages'][i]
        gazes.append({
            'data': []
        })
        for j in range(0, len(stage['data'])):
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            gazes[i]['data'].append({
                'x': stage['data'][j]['ax'],
                'y': stage['data'][j]['ay'],
                't': 1
            })

    return {
        'config': meta['config'],
        'gazes': gazes
    }


def generate_em_test3_result(meta):
    gazes = []
    for i in range(0, len(meta['stages'])):
        stage = meta['stages'][i]
        gazes.append({
            'data': []
        })
        for j in range(0, len(stage['data'])):
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            gazes[i]['data'].append({
                'x': stage['data'][j]['ax'],
                'y': stage['data'][j]['ay'],
                't': 1
            })

    return {
        'config': meta['config'],
        'gazes': gazes
    }


def generate_em_test4_result(meta):
    gazes = []
    for i in range(0, len(meta['stages'])):
        stage = meta['stages'][i]
        gazes.append({
            'data': []
        })
        for j in range(0, len(stage['data'])):
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            gazes[i]['data'].append({
                'x': stage['data'][j]['ax'],
                'y': stage['data'][j]['ay'],
                't': 1
            })

    return {
        'config': meta['config'],
        'gazes': gazes
    }


def generate_em_test5_result(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']

    gazes = []
    for i in range(0, len(meta['stages'])):
        stage = meta['stages'][i]
        gazes.append({
            'data': []
        })
        is_gaze, gaze_pos = False, 0
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']):
                gaze_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            if calc_distance(
                    stage['data'][j]['ax'],
                    stage['data'][j]['ay'],
                    stage['data'][gaze_pos]['ax'],
                    stage['data'][gaze_pos]['ay']
            ) < size + BIAS:
                if not is_gaze and j - gaze_pos > fps / 5:
                    is_gaze = True
            else:
                if is_gaze:
                    x, y = stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']
                    gazes[i]['data'].append({'x': x, 'y': y, 't': j - gaze_pos - 1})
                is_gaze = False
                gaze_pos = j
        if is_gaze:
            j = len(stage['data'])
            x, y = stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']
            gazes[i]['data'].append({'x': x, 'y': y, 't': j - gaze_pos - 1})

    return {
        'config': meta['config'],
        'gazes': gazes
    }


def generate_em_test6_result(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']

    gazes = []
    for i in range(0, len(meta['stages'])):
        stage = meta['stages'][i]
        gazes.append({
            'data': []
        })
        is_gaze, gaze_pos = False, 0
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']):
                gaze_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            if calc_distance(
                    stage['data'][j]['ax'],
                    stage['data'][j]['ay'],
                    stage['data'][gaze_pos]['ax'],
                    stage['data'][gaze_pos]['ay']
            ) < size + BIAS:
                if not is_gaze and j - gaze_pos > fps / 5:
                    is_gaze = True
            else:
                if is_gaze:
                    x, y = stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']
                    gazes[i]['data'].append({'x': x, 'y': y, 't': j - gaze_pos - 1})
                is_gaze = False
                gaze_pos = j
        if is_gaze:
            j = len(stage['data'])
            x, y = stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']
            gazes[i]['data'].append({'x': x, 'y': y, 't': j - gaze_pos - 1})

    return {
        'config': meta['config'],
        'gazes': gazes
    }


def generate_em_test7_result(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']

    gazes = []
    for i in range(0, len(meta['stages'])):
        stage = meta['stages'][i]
        gazes.append({
            'env': stage['env'],
            'data': []
        })
        is_gaze, gaze_pos = False, 0
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']):
                gaze_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            if calc_distance(
                    stage['data'][j]['ax'],
                    stage['data'][j]['ay'],
                    stage['data'][gaze_pos]['ax'],
                    stage['data'][gaze_pos]['ay']
            ) < size + BIAS:
                if not is_gaze and j - gaze_pos > fps / 5:
                    is_gaze = True
            else:
                if is_gaze:
                    x, y = stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']
                    gazes[i]['data'].append({'x': x, 'y': y, 't': j - gaze_pos - 1})
                is_gaze = False
                gaze_pos = j
        if is_gaze:
            j = len(stage['data'])
            x, y = stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']
            gazes[i]['data'].append({'x': x, 'y': y, 't': j - gaze_pos - 1})

    return {
        'config': meta['config'],
        'gazes': gazes
    }


def generate_em_test8_result(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']

    gazes = []
    for i in range(0, 2):
        stage = meta['stages'][i + 1]
        gazes.append({
            'env': stage['env'],
            'data': []
        })
        is_gaze, gaze_pos = False, 0
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']):
                gaze_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            if calc_distance(
                    stage['data'][j]['ax'],
                    stage['data'][j]['ay'],
                    stage['data'][gaze_pos]['ax'],
                    stage['data'][gaze_pos]['ay']
            ) < size + BIAS:
                if not is_gaze and j - gaze_pos > fps / 5:
                    is_gaze = True
            else:
                if is_gaze:
                    x, y = stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']
                    gazes[i]['data'].append({'x': x, 'y': y, 't': j - gaze_pos - 1})
                is_gaze = False
                gaze_pos = j
        if is_gaze:
            j = len(stage['data'])
            x, y = stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']
            gazes[i]['data'].append({'x': x, 'y': y, 't': j - gaze_pos - 1})

    return {
        'config': meta['config'],
        'gazes': gazes
    }
