
# 执行能力评估项目1处理
class HandlerExecution1:
    def __init__(self) -> None:
        pass

    # 通过收集评测的数据，计算需要的报告等数据
    def cal_result_info(self, collect_data):
        collect_data_sample = {
            "start_time": "",
            "run_data": [ # 每轮数据
                {
                    "cost_time": "",
                    "compelete_time": ["", "", ""], # 每次切换完成时间
                    "complete_time": 1, # 成功完成次数
                    "uncomplete_time": 2, # 未完成次数
                    "wrong_category_time": 2, # 分类选择错误次数
                    "switch_cost_time": ["", "", ""], # 每次切换反应时间，从切换开始到开始拖拽松开时间
                },
            ],
            "collect_type": 0, # 操作方式：0点击拖拽
            "total_score": 5,
            "cost_time": "",
        }