from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta
import numpy as np
import random
import uuid
import json

from app.services.quick_screening_question_service import QuickScreeningQuestionService
from app.services.quick_screening_answer_service import QuickScreeningAnswerService
from app.services.improve_learning_result_service import ImproveLearningResultService
from app.services.user_service import UserService
from app.models.improve_learning_result import ImproveLearningResult
from app.models.quick_screening_answer import QuickScreeningAnswer
from app.core.database import get_db
from app.core.redis import AsyncRedisClient
from app.core.logger import main_logger
from app.core.global_var import GlobalVar

class ImproveLearningService:

    def __init__(self) -> None:
        self.db_session = get_db
        self.part_list = ["visual", "listening", "exam"]
        self.part_name_list = {"visual": "视觉信息处理能力", "listening": "听讲能力", "exam": "考试能力"}

    # 获取题目列表
    async def get_list(self, part):

        question_service = QuickScreeningQuestionService()

        question_list = await question_service.get_list_by_part(part)

        question_list, question_list_key = await self.gen_part_question_list(part, question_list)

        question_info_list = []
        for value in question_list:
            content_json = value.content_json
            if content_json:
                for key, content in enumerate(content_json):
                    if content.get("type") == "text":
                        content["content"] = _(content["content"])
                        content_json[key] = content
            option_list = value.option_json["option_list"]
            option_type = value.option_json["type"]
            new_option_list = {}
            if option_type == "single_choice":
                for opt, opt_name in option_list.items():
                    new_option_list[opt] = _(opt_name)
                value.option_json["option_list"] = new_option_list
            elif option_type == "single_media_choice":
                pass
            info = {
                "question_id": value.question_id,
                "title": value.title,
                "desc": value.desc,
                "content_info": content_json,
                "category": value.category,
                "key": value.key,
                "type": value.type,
                "status": value.status,
                "option_info": value.option_json,
            }
            question_info_list.append(info)

        return question_info_list, question_list_key

    # 处理提交的回答
    # collect_answer = {
    #     "emotion_survey": [
    #         {"question_id": 123, "type": "choice", "answer": ["opt1"]},
    #         {"question_id": 124, "type": "choice", "answer": ["opt1"]},
    #     ],
    # }
    async def answer_question(self, collect_answer, user_id):
        
        part = collect_answer.get("part", None)
        if not part:
            return {"code": 10206, "msg": "分类数据不存在"}

        answer_info = collect_answer.get("answer_info", None)
        if not answer_info:
            return {"code": 10206, "msg": "回答数据不存在"}
        
        question_list_key = collect_answer.get("question_list_key", "")
        if not question_list_key:
            return {"code": 10206, "msg": "question_list_key 不存在"}

        answer_json = {}
        part_answer_json = await self.handle_answer_result(answer_info, part, question_list_key)
        answer_json[part] = part_answer_json

        user_service = UserService()
        result_service = ImproveLearningResultService()

        user_info = await user_service.get_user_by_user_id(user_id)
        user_extra_json = user_info["extra_json"]
        il_result_id = user_extra_json.get("il_result_id", 0)
        if not il_result_id:
            new_result = ImproveLearningResult(
                user_id=user_id,
                result_json=answer_json,
                report_json={},
                extra_json={},
            )
            new_result_info = await result_service.new_improve_learning_result(new_result)
            il_result_id = new_result_info.il_result_id

            update_user_data = {
                "extra_json": {
                    "il_result_id": il_result_id
                }
            }
            await user_service.update_user_info_json(user_id, update_user_data)

        else:

            update_info = {
                "result_json": {part: 0}
            }
            await result_service.update_result_by_id(il_result_id, update_info)

            update_info = {
                "result_json": {part: part_answer_json}
            }
            await result_service.update_result_by_id(il_result_id, update_info)

        print("il_result_id: " + str(il_result_id))

        await self.handle_answer(part_answer_json, il_result_id, user_id)

        update_user_data = {
            "extra_json": {
                "il_status_info": {part: 3}
            }
        }
        await user_service.update_user_info_json(user_id, update_user_data)


        is_finish = await self.gen_report_data(il_result_id)

        return {"code": 0, "msg": "", "data": {"il_result_id": il_result_id, "is_finish": is_finish}}
        
    async def get_report(self, il_result_id, user_id):

        result_service = ImproveLearningResultService()
        user_service = UserService()

        result_info = await result_service.get_improve_learning_result_by_id(il_result_id)
        if not result_info:
            return {"code": 10206, "msg": _("获取报告失败")}
        report_json_info = result_info.report_json
        if not report_json_info:
            return {"code": 10207, "msg": _("获取报告失败")}
        if result_info.user_id != user_id:
            return {"code": 10208, "msg": _("获取报告失败")}

        user_info = report_json_info["user_info"]
        user_age = user_info["age"]
        user_name = user_info["name"]
        user_gender = _("男") if user_info["gender"] == 1 else _("女")
        user_grade = user_service.convert_grade_to_text(user_info["grade"])

        result_json_info = result_info.result_json
        train_info_list = self.gen_training_plan(report_json_info, result_json_info)

        analyze_detail_info = {
            "visual": [
                "您在视觉信息处理方面还有待提升，可能在信息捕捉和处理上存在一定不足。建议制定详细的训练计划，循序渐进地加强相关能力，以便更好地应对各类视觉任务。",
                "您在视觉信息处理方面的表现处于中等水平。虽然基本能应对大部分视觉任务，但在某些环节上仍有提升的空间。建议通过针对性训练进一步强化能力，争取在关键环节上取得突破。",
                "您在视觉信息处理方面表现非常出色，能够迅速、准确地获取和处理视觉信息。整体反应敏捷，思路清晰，显示出较高的综合能力。请继续保持并不断挑战更高水平！",
            ],
            "listening": [
                "您的听讲能力目前存在不足，可能在捕捉和理解信息时显得不够全面。建议加强听力和注意力方面的训练，逐步提升对信息的敏感性和整体理解能力。",
                "您的听讲能力表现处于平均水平，能够理解大部分听到的信息，但在细节和深度上可能还存在一定欠缺。建议进一步加强练习，提升专注度和敏锐度，以达到更优表现。",
                "您的听讲能力表现极佳，能够迅速捕捉并理解听到的信息，展现出高效的理解与反应能力。您的专注和信息处理能力均处于领先水平，请继续保持这一优势！",
            ],
            "exam": [
                "您的考试能力目前表现较弱，可能在应对考试情境时出现不稳定现象。建议您制定个性化提升计划，逐步改善应试策略和反应能力，以便在考试中发挥出更稳定的水平。",
                "您的考试能力整体表现中等，基本能满足考试要求，但在稳定性和应变能力方面还有进一步提升的空间。建议通过系统训练，逐步增强各项应试技能，争取取得更好成绩。",
                "您在考试能力上表现非常优异，能够在各个环节稳定发挥，展现出良好的应试状态。整体策略明确、思维敏捷，是您应对各类考试的重要优势。请继续保持并挑战更高目标！",
            ]
        }
        
        analyze_detail_info_list = {}
        for part in self.part_list:
            score = report_json_info[part]["score"]
            if score >= 70:
                analyze_info = analyze_detail_info[part][2]
            elif score < 40:
                analyze_info = analyze_detail_info[part][0]
            else:
                analyze_info = analyze_detail_info[part][1]
            analyze_detail_info_list[part] = {
                "score": round(score),
                "name": self.part_name_list[part],
                "analyze_info": analyze_info,
            }
        
        return_report_info = {
            "version": report_json_info["version"],
            "user_age": user_age,
            "user_name": user_name,
            "user_gender": user_gender,
            "user_grade": user_grade,
            "visual": analyze_detail_info_list["visual"]["score"],
            "listening": analyze_detail_info_list["listening"]["score"],
            "exam": analyze_detail_info_list["exam"]["score"],
            "analyze_detail_info_list": analyze_detail_info_list,
            "part_train_info_list": train_info_list,
        }

        return {"code": 0, "msg": "", "data": return_report_info}

    def gen_training_plan(self, report_json_info, result_json_info):
        part_score_list = {
            "visual": report_json_info["visual"]["score"],
            "listening": report_json_info["listening"]["score"],
            "exam": report_json_info["exam"]["score"],
        }

        train_detail_info_list = {
            "visual_discrimination" : {
                "name": "视觉辨别",
                "train_name": "快眼识图",
                "train_desc": "系统展示细微差异的图像，要求在限定时间内迅速找出异常图案，锻炼视觉识别与区分能力。",
                "train_plan": "每周4次，每次10分钟，持续6周"
            },
            "visual_memory": {
                "name": "视觉记忆",
                "train_name": "图像记忆挑战",
                "train_desc": "短暂展示一组图像，随后隐藏后要求按正确顺序回忆排列，提升图像存储与回忆能力。",
                "train_plan": "每周3次，每次15分钟，持续4周"
            },
            "spatial_relationships": {
                "name": "空间关系",
                "train_name": "立体拼图探险",
                "train_desc": "面对三维物体旋转和重构的拼图任务，训练对空间结构和物体间关系的快速理解。",
                "train_plan": "每周5次，每次10分钟，持续5周"
            },
            "form_constancy": {
                "name": "形状恒常性",
                "train_name": "形状探秘",
                "train_desc": "展示同一物体在不同角度、大小和光照下的图像，要求判断是否为同一物体，强化形状恒常性识别能力。",
                "train_plan": "每周3次，每次20分钟，持续4周"
            },
            "sequential_memory": {
                "name": "顺序记忆",
                "train_name": "序列记忆大挑战",
                "train_desc": "系统依次展示一串数字、符号或图案，要求按原顺序回忆排列，锻炼顺序记忆能力。",
                "train_plan": "每周4次，每次12分钟，持续6周"
            },
            "visual_figure_ground": {
                "name": "视觉图形-背景区分",
                "train_name": "前景背景识别",
                "train_desc": "提供包含复杂背景的图像，要求迅速从中提取出主要目标，提升对关键信息的捕捉能力。",
                "train_plan": "每周3次，每次15分钟，持续4周"
            },
            "visual_closure": {
                "name": "视觉闭合",
                "train_name": "图形闭合填空",
                "train_desc": "根据图像中已显示的部分推测并完成缺失部分，培养由局部推断整体的能力。",
                "train_plan": "每周4次，每次15分钟，持续5周"
            },
            "auditory_comprehension": {
                "name": "听觉理解",
                "train_name": "声音探秘",
                "train_desc": "提供不同背景噪音条件下录制的音频，训练在干扰环境中捕捉和理解关键信息的能力。播放后系统自动出题检测理解效果。",
                "train_plan": "每周4次，每次20分钟，持续5周",
            },
            "attention": {
                "name": "专注力",
                "train_name": "专注训练器",
                "train_desc": "通过计时任务和反应测试，在设置干扰条件下锻炼保持专注的能力，提升工作和考试时的注意力。",
                "train_plan": "每周5次，每次10分钟，持续4周",
            },
            "average_memory": {
                "name": "记忆力",
                "train_name": "记忆力挑战",
                "train_desc": "包含数字串、单词和图形记忆任务，通过多样化练习全面提升短期与长期记忆能力。",
                "train_plan": "每周4次，每次15分钟，持续5周",
            },
            "executive_function": {
                "name": "执行功能",
                "train_name": "任务管理挑战",
                "train_desc": "系统设置多步骤任务和情境问题，要求规划并依次完成任务，锻炼计划与执行能力。",
                "train_plan": "每周3次，每次20分钟，持续4周",
            },
            "languages": {
                "name": "语音能力",
                "train_name": "口语流畅训练",
                "train_desc": "通过朗读、复述和互动语音练习，改善发音、语速和表达流畅度。",
                "train_plan": "每周4次，每次10分钟，持续6周",
            },
            "logical_thinking": {
                "name": "逻辑思维",
                "train_name": "逻辑大作战",
                "train_desc": "提供逻辑谜题、数学推理题和策略游戏，通过解题练习提升分析、推理和问题解决能力。",
                "train_plan": "每周5次，每次15分钟，持续4周",
            },
            "spatial_relationships_exam": {
                "name": "空间认知",
                "train_name": "空间导航大师",
                "train_desc": "通过虚拟导航和三维拼图任务，训练对立体空间结构的理解与定位能力。",
                "train_plan": "每周4次，每次10分钟，持续5周",
            },
            "learning_strategies": {
                "name": "学习策略",
                "train_name": "学习计划大师",
                "train_desc": "引导掌握制定高效学习计划、记笔记技巧及记忆工具的使用，提升整体学习方法和自我管理能力。",
                "train_plan": "每周3次，每次20分钟，持续4周",
            },
            "social_cognition": {
                "name": "社会认知",
                "train_name": "社交情境模拟",
                "train_desc": "通过虚拟角色扮演和情境模拟练习，提升对社交信号的捕捉与解读能力，帮助适应复杂社交场景。",
                "train_plan": "每周3次，每次15分钟，持续5周",
            },
        }

        part_train_info_list = []
        for part in self.part_list:
            score_list = result_json_info[part]
            sorted_score_list = sorted(score_list.items(), key=lambda item: item[1]['score'])
            train_info_list = []
            for question_id, info in sorted_score_list:
                level = 1
                if info["score"] <= 0:
                    level = 3
                if part == "listening" and info["score"] <= 60: #临时判断
                    level = 3
                if level != 3:
                    continue
                train_info = {
                    "category": info["category"],
                    "detail_info": train_detail_info_list.get(info["category"], {}),
                    "level": level,
                    "score": info["score"],
                }
                train_info_list.append(train_info)
            if not train_info_list:
                continue
            part_train_info = {
                "train_info_list": train_info_list,
                "part": part,
                "name": self.part_name_list[part],
            }
            part_train_info_list.append(part_train_info)

        return part_train_info_list

    async def gen_report_data(self, il_result_id):
        
        result_service = ImproveLearningResultService()
        user_service = UserService()
        il_result_info = await result_service.get_improve_learning_result_by_id(il_result_id)

        report_json = {}

        result_json = il_result_info.result_json
        
        visual_result = result_json.get("visual")
        listening_result = result_json.get("listening")
        exam_result = result_json.get("exam")

        if not visual_result:
            return False
        if not listening_result:
            return False
        if not exam_result:
            return False

        visual_report_json = self.gen_visual_report_data(visual_result)
        report_json["visual"] = visual_report_json
        listening_report_json = self.gen_listening_report_data(listening_result)
        report_json["listening"] = listening_report_json
        exam_report_json = self.gen_exam_report_data(exam_result)
        report_json["exam"] = exam_report_json

        user_info = await user_service.get_user_by_user_id(il_result_info.user_id)
        age = user_service.get_age_by_birthday(user_info["birthday"])

        report_json["version"] = 1
        report_json["report_time"] = int(datetime.utcnow().timestamp() * 1000) #报告生成时间
        report_json["report_lang"] = GlobalVar.get_var("language")
        report_json["user_info"] = {"name": user_info["name"], "age": age, "gender": user_info["gender"], "grade": user_info["grade"]}

        update_data = {
            "report_json": report_json
        }
        await result_service.update_result_by_id(il_result_id, update_data)

        update_user_data = {
            "extra_json": {
                "il_status": 3
            }
        }
        await user_service.update_user_info_json(il_result_info.user_id, update_user_data)


        return True

    def gen_visual_report_data(self, visual_result):
        if not visual_result:
            return {}
        
        total_score = 0.0
        for question_id, answer in visual_result.items():
            total_score += answer["score"]

        visual_report_json = {
            "score": total_score,
        }
        return visual_report_json
    
    def gen_listening_report_data(self, listening_result):
        if not listening_result:
            return {}
        
        total_score = 0.0
        for question_id, answer in listening_result.items():
            total_score += answer["score"]

        listening_report_json = {
            "score": total_score,
        }
        return listening_report_json

    
    def gen_exam_report_data(self, exam_result):
        if not exam_result:
            return {}
        
        total_score = 0.0
        for question_id, answer in exam_result.items():
            total_score += answer["score"]

        exam_report_json = {
            "score": total_score,
        }
        return exam_report_json

    async def handle_answer(self, part_answer_json, il_result_id, user_id):
        
        answer_service = QuickScreeningAnswerService()
        for key, value in part_answer_json.items():
            new_answer = QuickScreeningAnswer(
                question_id=key,
                user_id=user_id,
                type="improve_learning",
                qs_result_id=il_result_id,
                answer_json=value,
                extra_json={}
            )
            new_answer_info = await answer_service.new_quick_screening_answer(new_answer)
    
    async def handle_answer_result(self, answer_info, part, question_list_key):

        answer_list_dict = {}
        for value in answer_info:
            answer_list_dict[value["question_id"]] = value
        
        question_service = QuickScreeningQuestionService()

        answer_json = {}
        
        redis_client = AsyncRedisClient()
        question_paper_list = await redis_client.command("get", question_list_key)
        if not question_paper_list:
            return answer_json

        question_paper_list = json.loads(question_paper_list)
        question_id_list = []
        question_paper_info_list = {}
        for question_paper_info in question_paper_list:
            question_id_list.append(question_paper_info["question_id"])
            question_paper_info_list[question_paper_info["question_id"]] = question_paper_info
        
        question_list = await question_service.get_list_by_question_ids(question_id_list)
        for value in question_list:
            answer_info = answer_list_dict.get(value.question_id, None)
            if not answer_info:
                answer_json[value.question_id] = {"answer": None, "score": 0.0, "category": value.category}
                continue

            answer = answer_info.get("answer", [])
            if not answer:
                answer_json[value.question_id] = {"answer": None, "score": 0.0, "category": value.category}
                continue
            answer_0 = answer[0]

            score = question_paper_info_list[value.question_id]["wrong"]
            ref_answer = value.ref_answer_json
            if ref_answer:
                if ref_answer.get("answer") == answer_0:
                    score = question_paper_info_list[value.question_id]["right"]
            answer_json[value.question_id] = {"answer": answer, "score": score, "category": value.category}

        return answer_json
    
    async def gen_part_question_list(self, part, question_list):
        get_rules = {}
        if part == "visual":
            get_rules = {
                "form_constancy": 1,
                "sequential_memory": 1,
                "spatial_relationships": 1,
                "visual_closure": 1,
                "visual_discrimination": 1,
                "visual_figure_ground": 1,
                "visual_memory": 1,
            }
            score_rules = {
                "right": 14.29,
                "wrong": 0.0,
            }
        elif part == "listening":
            get_rules = {
                "auditory_comprehension": 1,
            }
            score_rules = {
                "right": 100.0,
                "wrong": 60.0,
            }
        elif part == "exam":
            get_rules = {
                "attention": 1,
                "executive_function": 1,
                "languages": 1,
                "learning_strategies": 1,
                "logical_thinking": 1,
                "average_memory": 1,
                "social_cognition": 1,
                "spatial_relationships_exam": 1,
            }
            score_rules = {
                "right": 12.5,
                "wrong": 0.0,
            }
        
        tmp_question_list = {}
        for question_info in question_list:
            category = question_info.category
            tmp_question_list.setdefault(category, []).append(question_info)

        if get_rules:
            shuffled_keys = random.sample(get_rules.keys(), len(get_rules))
            shuffled_dict = {key: get_rules[key] for key in shuffled_keys}
            get_rules = shuffled_dict

        result_list = []
        for category, num in get_rules.items():
            category_question_list = tmp_question_list.get(category, [])
            if not category_question_list:
                continue
            random_question_list = random.sample(category_question_list, num)
            for question_info in random_question_list:
                result_list.append(question_info)

        if not result_list:
            raise Exception("No question_list for part:{}".format(part))

        question_paper_list = []
        for question_info in result_list:
            question_paper_list.append({
                "question_id": question_info.question_id,
                "right": score_rules["right"],
                "wrong": score_rules["wrong"]
            })
        redis_client = AsyncRedisClient()
        redis_key = "improve_learning_question_list_{}".format(uuid.uuid4())
        await redis_client.command("set", redis_key, json.dumps(question_paper_list))
        await redis_client.command("expire", redis_key, 3600)

        return result_list, redis_key