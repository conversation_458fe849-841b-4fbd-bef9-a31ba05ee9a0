from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.evaluation_result import EvaluationResult


class EvaluationResultService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_evaluation_result(self, new_evaluation_result):
        try:
            async with self.db_session() as session:
                session.add(new_evaluation_result)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_evaluation_result: {str(e)}")
            raise

        return new_evaluation_result
    

    async def get_result_by_id(self, result_id):
        try:
            async with self.db_session() as session:
                stmt = select(EvaluationResult).where(EvaluationResult.result_id == result_id)
                result = await session.execute(stmt)
                result_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_result_by_id: {str(e)}")
            raise

        return result_info
    
    async def get_result_list_by_ids(self, result_ids):
        try:
            async with self.db_session() as session:
                stmt = select(EvaluationResult).where(EvaluationResult.result_id.in_(result_ids))
                result = await session.execute(stmt)
                result_info = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_result_list_by_ids: {str(e)}")
            raise

        return result_info
    
    async def get_result_list_by_user_id(self, user_id, category, status, start = 0, limit = 10):
        try:
            async with self.db_session() as session:
                conditions = [
                    EvaluationResult.user_id == user_id,
                    EvaluationResult.category == category,
                    EvaluationResult.status.in_(status)
                ]
                query = select(EvaluationResult).where(and_(*conditions)).offset(start).limit(limit)
                order = desc(EvaluationResult.result_id)
                query = query.order_by(order)
                result = await session.execute(query)
                result_info = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_result_by_id: {str(e)}")
            raise

        return result_info
    
    async def update_result_by_id(self, result_id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(EvaluationResult).where(EvaluationResult.result_id == result_id)
                result = await session.execute(stmt)
                result_info = result.scalars().first()

                if not result_info:
                    return False

                await result_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_result_by_id: {str(e)}")
            raise

        return True