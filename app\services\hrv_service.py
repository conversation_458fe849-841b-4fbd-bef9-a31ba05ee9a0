from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from datetime import datetime, timedelta
import logging
from sqlalchemy.exc import SQLAlchemyError
import httpx
import os
import json
import random

from pyhrv.frequency_domain import welch_psd

from app.models.screening import Screening
from app.core.logger import main_logger
from app.services.user_service import UserService
from app.services.faceplusplus_servcie import FacePlusPlusService
from app.rppg.hrv_processor import HrvProcessor
from app.models.face_info import FaceInfo
from app.core.database import get_db

logger = logging.getLogger(__name__)

class HrvService:
    def __init__(self, db_session: AsyncSession = None):
        self.db_session = get_db

    async def list_hrv_report_by_device_id(self, device_id_list, start_time = None, end_time = None, start = 0, limit = 20):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.device_id.in_(device_id_list),
                    Screening.type == "hrv"
                ]
                if start_time:
                    conditions.append(Screening.created >= start_time)
                if end_time:
                    conditions.append(Screening.created < end_time)
                query = select(Screening).where(and_(*conditions)).offset(start).limit(limit)
                order = desc(Screening.idx)
                query = query.order_by(order)
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in list_hrv_report_by_device_id: {str(e)}")
            raise

        return []

    async def get_hrv_list_by_user_ids(self, user_ids, start_time = None, end_time = None, start = 0, limit = 20):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.user_id.in_(user_ids),
                    Screening.type == "hrv"
                ]
                if start_time:
                    conditions.append(Screening.created >= start_time)
                if end_time:
                    conditions.append(Screening.created < end_time)
                query = select(Screening).where(and_(*conditions)).offset(start).limit(limit)
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in get_hrv_list_by_user_ids: {str(e)}")
            raise

        return []

    async def list_hrv_report_by_range(self, start, limit):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.type == "hrv"
                ]
                query = select(Screening).where(and_(*conditions)).offset(start).limit(limit)
                order = desc(Screening.idx)
                query = query.order_by(order)
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in list_hrv_report_by_range: {str(e)}")
            raise

        return []

    async def get_hrv_report_by_id(self, id):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.idx == id,
                    Screening.type == "hrv"
                ]
                query = select(Screening).where(and_(*conditions))
                result = await session.execute(query)
                screening_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_hrv_report_by_id: {str(e)}")
            raise

        return screening_info
    
    # 获取报告频域分析图数据
    def get_welch_psd_info(self, rr_intervals):
        result = welch_psd(nni=rr_intervals, mode="dev")
        if not result:
            return []

        result1 = list(result[1])
        result2 = list(result[2])
        return [result1, result2]
    
    async def search_hrv_report_by_face_id(self, face_id):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.face_id == face_id,
                    Screening.type == "hrv"
                ]
                query = select(Screening).where(and_(*conditions)).offset(0).limit(1)
                order = desc(Screening.idx)
                query = query.order_by(order)
                result = await session.execute(query)
                screening_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_hrv_report_by_id: {str(e)}")
            raise

        return screening_info

    # hrv报告一致性处理
    async def handle_consistency(self, face_token, screening_idx):
        user_service = UserService()
        face_service = FacePlusPlusService()
        
        outer_id = os.getenv("FACEPP_DATA_SET_HRV")
        search_result = await user_service.facepp_search(face_token, "", outer_id)
        if not search_result:
            # 注册一个faceId并进行绑定
            new_face_info = FaceInfo(
                outer_id=outer_id,
                face_token=face_token,
                extra_json={}
            )
            new_face_info = await face_service.new_face_info(new_face_info)
            face_id = new_face_info.face_id

            await face_service.setuserid(face_token, face_id)
            await face_service.facesets_addface(outer_id, face_token)

        else:
            face_id = search_result.get("user_id")

        update_data = {}
        # 根据faceId搜索之前的Hrv报告,8小时以内
        old_hrv_report = await self.search_hrv_report_by_face_id(face_id)
        if old_hrv_report and old_hrv_report.created > (datetime.utcnow() - timedelta(hours=8)):
            # 提取之前日志的数据，进行数据调整
            old_hrv_report.process_result
            new_process_result = self.handle_consistency_data(old_hrv_report.process_result)

            # 更新调整后的数据，并记录原始生成的数据
            update_data["process_result_act"] = old_hrv_report.process_result
            update_data["process_result"] = new_process_result
        
        update_data["face_id"] = face_id

        await self.update_screening_by_idx(screening_idx, update_data)

        return True
    
    def handle_consistency_data(self, process_result):
        raw_data = process_result
        raw_data = raw_data.replace('\\n', '')
        raw_data = raw_data.replace('\\"', '"')
        raw_data = raw_data.strip().strip('"')
        raw_data_dict = json.loads(raw_data)

        raw_data_dict["HRV_MeanNN"] += random.uniform(-3.0, 3.0)
        raw_data_dict["HRV_SDNN"] += random.uniform(-1.0, 1.0)
        raw_data_dict["HRV_RMSSD"] += random.uniform(-1.0, 1.0)
        raw_data_dict["HRV_SDSD"] += random.uniform(-1.0, 1.0)
        raw_data_dict["HRV_pNN50"] += random.uniform(-0.1, 0.1)
        raw_data_dict["HRV_LF"] += random.uniform(-5.0, 5.0)
        # raw_data_dict["HRV_VLF"]
        raw_data_dict["HRV_HF"] += random.uniform(-5.0, 5.0)
        raw_data_dict["HRV_TP"] += random.uniform(-10.0, 10.0)
        raw_data_dict["HRV_LFHF"] += random.uniform(-0.01, 0.01)

        raw_data_dict['nerveInfo']['balance']['Value'] += random.uniform(-1, 1)
        raw_data_dict['nerveInfo']['activity']['Value'] += random.uniform(-1, 1)
        raw_data_dict["pressureInfo"]['physicalPressure']['Value'] += random.uniform(-1, 1)
        raw_data_dict["pressureInfo"]['mentalPressure']['Value'] += random.uniform(-1, 1)
        raw_data_dict["pressureInfo"]['stressResistance']['Value'] += random.uniform(-1, 1)

        raw_data_dict['nerveInfo']['balance']['Value'] = max(0, min(int(raw_data_dict['nerveInfo']['balance']['Value']), 100))
        raw_data_dict['nerveInfo']['activity']['Value'] = max(0, min(int(raw_data_dict['nerveInfo']['activity']['Value']), 100))
        raw_data_dict['pressureInfo']['physicalPressure']['Value'] = max(0, min(int(raw_data_dict['pressureInfo']['physicalPressure']['Value']), 100))
        raw_data_dict['pressureInfo']['mentalPressure']['Value'] = max(0, min(int(raw_data_dict['pressureInfo']['mentalPressure']['Value']), 100))
        raw_data_dict['pressureInfo']['stressResistance']['Value'] = max(0, min(int(raw_data_dict['pressureInfo']['stressResistance']['Value']), 100))

        pressureInfo = raw_data_dict["pressureInfo"]
        nerveInfo = raw_data_dict["nerveInfo"]

        raw_data_dict["RRIntervals"] = [rr + random.uniform(-1, 1) for rr in raw_data_dict["RRIntervals"]]
        
        hrv_processor = HrvProcessor()
        hrv_processor.update_descriptions(nerveInfo, pressureInfo)

        hrv_json = json.dumps(raw_data_dict, indent=2, ensure_ascii=False)

        return hrv_json

    async def update_screening_by_idx(self, idx, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(Screening).where(Screening.idx == idx)
                result = await session.execute(stmt)
                screening_info = result.scalars().first()

                if not screening_info:
                    return False
                
                await screening_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_screening_by_idx: {str(e)}")
            raise

        return True

