import numpy as np
import scipy.signal


class DigitalFilter:
    def __init__(self, f=30, cutoff=[0.8, 2.5], order=2, btype="bandpass"):
        ba = scipy.signal.butter(N=order, Wn=np.divide(cutoff, f / 2.), btype=btype)

        self._bs = ba[0]
        self._as = ba[1]
        self._xs = [0]*len(ba[0])
        self._ys = [0]*(len(ba[1])-1)

    def process(self, x):
        if np.isnan(x):  # ignore nans, and return as is
            return x

        self._xs.insert(0, x)
        self._xs.pop()
        y = (np.dot(self._bs, self._xs) / self._as[0]
             - np.dot(self._as[1:], self._ys))
        self._ys.insert(0, y)
        self._ys.pop()
        return y
