from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from app.models.base import Base, TimestampMixin


class CopilotSummary(Base, TimestampMixin):
    __tablename__ = 'copilot_summary'

    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, default="0")
    channel_no = Column(String(512), default="")
    clinic_no = Column(String(512), default="")
    device_id = Column(String(100), default="")
    voice_text = Column(Text, default="")
    summary = Column(Text, default="")
    summary_file = Column(JSON, default=dict)
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<CopilotSummary(id={self.id}, channel_no={self.channel_no})>"
