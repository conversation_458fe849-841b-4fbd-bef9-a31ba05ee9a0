from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.training_item_plan import TrainingItemPlan

class TrainingItemPlanService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_training_item_plan(self, new_training_item_plan):
        try:
            async with self.db_session() as session:
                session.add(new_training_item_plan)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_training_item_plan: {str(e)}")
            raise

        return new_training_item_plan

    async def get_item_plan_list_by_train_plan_result_id(self, train_plan_result_id):
        try:
            async with self.db_session() as session:
                stmt = select(TrainingItemPlan).where(TrainingItemPlan.train_plan_result_id == train_plan_result_id)
                result = await session.execute(stmt)
                item_plan_list = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_item_plan_list_by_train_plan_result_id: {str(e)}")
            raise

        return item_plan_list

    async def get_item_plan_by_id(self, train_item_plan_id):
        try:
            async with self.db_session() as session:
                stmt = select(TrainingItemPlan).where(TrainingItemPlan.train_item_plan_id == train_item_plan_id)
                result = await session.execute(stmt)
                item_plan_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_item_plan_by_id: {str(e)}")
            raise

        return item_plan_info
    
    async def update_item_plan_by_id(self, train_item_plan_id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(TrainingItemPlan).where(TrainingItemPlan.train_item_plan_id == train_item_plan_id)
                result = await session.execute(stmt)
                item_plan_info = result.scalars().first()

                if not item_plan_info:
                    return False

                await item_plan_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_item_plan_by_id: {str(e)}")
            raise

        return True