from pydantic import BaseModel, Field
from .base import APIResponse


class EmotionRequest(BaseModel):
    timestamp: int = Field(..., description="Unix timestamp in milliseconds")
    user_id: str = Field('unknown', description="Optional unique identifier for the user")
    session_id: str = Field(..., description="the unique identifier for the session")
    image: str = Field(..., description="Base64 encoded image data")


class EmotionResponse(APIResponse[dict]):
    pass
