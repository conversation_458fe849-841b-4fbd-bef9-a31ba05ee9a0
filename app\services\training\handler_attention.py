from app.utils.common import CommonUtils

# 注意力训练综合结果处理
class HandlerAttention:
    def __init__(self) -> None:
        pass

    # 通过各项目训练的数据，计算需要的报告数据
    def cal_result_info(self, item_result_info_list):
        return item_result_info_list
    
    def handle_report_data(self, plan_config_json, result_json_list, age):

        total_time = 0
        complete_item_num = 0
        total_score = 0
        item_score_list = []
        for result_json_info in result_json_list:
            complete_item_num += 1
            total_time += result_json_info["total_time"]
            total_score += result_json_info["score"]
            item_score_list.append(round(result_json_info["score"]))

        #均分
        score = total_score / complete_item_num

        #评级
        if score >= 90:
            training_level = 'A+'
        elif score >= 80 and score < 90:
            training_level = 'A'
        elif score >=70 and score < 80:
            training_level = 'B'
        elif score >= 60 and score < 70:
            training_level = 'C'
        else:
            training_level = 'D'

        report_info = {
            "total_time": total_time,
            "score": score,
            "training_level": training_level,
            "item_score_list": item_score_list,
        }

        return report_info
    
    def format_report_data(self, report_json):
        report_info = {
            "total_time": CommonUtils.format_time(round(report_json["total_time"] / 1000)),
            "score": round(report_json["score"]),
            "training_level": report_json["training_level"],
            "item_score_list": report_json["item_score_list"],
        }

        return report_info