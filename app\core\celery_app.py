from celery import Celery
from celery import Task
import os
import dotenv

dotenv.load_dotenv()

def make_celery():

    # 初始化 Celery
    celery_app = Celery(
        "celery_app",
        broker=os.getenv("CELERY_BROKER_URL"),
        # backend=os.getenv("CELERY_RESULT_BACKEND")
        include=[
            "app.tasks.upload_video_task"
        ]
    )

    # 配置 Celery（可选）
    # celery.conf.update(
    #     result_expires=3600,
    #     timezone='UTC',
    # )

    # 自动发现任务模块
    celery_app.autodiscover_tasks(['app.tasks'])

    # 绑定 FastAPI 应用
    # class ContextTask(celery.Task):
    #     def __call__(self, *args, **kwargs):
    #         with app.app_context():
    #             return self.run(*args, **kwargs)

    # celery.Task = ContextTask

    celery_app.conf.task_queues = {
        'video_upload': {
            'exchange': 'video_upload_exchange',
            'binding_key': 'video_upload'
        },
        # 'emails': {
        #     'exchange': 'emails_exchange',
        #     'binding_key': 'emails'
        # },
        # 'notifications': {
        #     'exchange': 'notifications_exchange',
        #     'binding_key': 'notifications'
        # }
    }

    celery_app.conf.task_video_upload_queue = 'video_upload'
    # celery_app.conf.task_default_exchange = 'default_exchange'
    # celery_app.conf.task_default_routing_key = 'default'

    return celery_app

celery_app = make_celery()