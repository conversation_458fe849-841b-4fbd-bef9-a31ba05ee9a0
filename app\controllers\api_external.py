from fastapi import APIRouter, HTTPException, Request
from fastapi import Depends
from pydantic import BaseModel, Field
import traceback
import json
from typing import Any, Dict
from datetime import datetime, timedelta
import os

from app.core.database import get_db
from app.core.decorators import check_account_key
from app.schemas.base import APIResponse
from app.core.logger import main_logger
from app.services.user_service import UserService
from app.services.account_device_service import AccountDeviceService
from app.services.hrv_service import HrvService
from app.services.emotion_service import EmotionService
from app.services.speech_service import SpeechService
from app.services.eyemovement_service import EyemovementService
from app.utils.crypt import Crypt


router = APIRouter()


# 获取编号对应的HRVh5报告地址
class GetHrvReportRequest(BaseModel):
    idnum: str
@router.post("/get_hrv_report")
@check_account_key()
async def get_hrv_report(request: Request, get_hrv_report_request: GetHrvReportRequest):

    current_account_info = request.state.account

    idnum = get_hrv_report_request.idnum
    if not idnum:
        return APIResponse(
            code=0, message=_("获取成功"), data={}
        )

    hit_screening_list = []

    try:

        account_device_servcie = AccountDeviceService()
        device_list = await account_device_servcie.get_list_by_account_id(current_account_info["account_id"])
        if not device_list:
            main_logger.info("get_hrv_report by idnum device_list is None, account_id: {}".format(current_account_info["account_id"]))
            return APIResponse(
                code=0, message=_("获取成功"), data={}
            )

        device_id_list = [device_info.device_id for device_info in device_list]

        user_service = UserService()
        user_list = await user_service.get_user_list_by_idnum(idnum, device_id_list, 0, 20)
        hit_user_ids = [user_info.user_id for user_info in user_list]
        print(hit_user_ids)

        start_time = datetime.utcnow() - timedelta(hours=24),
        end_time = datetime.utcnow()

        hrv_service = HrvService()
        hrv_screening_list = await hrv_service.get_hrv_list_by_user_ids(hit_user_ids, start_time, end_time, 0, 20)
        for hrv_screening in hrv_screening_list:
            hit_screening_list.append(hrv_screening)

        emotion_service = EmotionService()
        emotion_screening_list = await emotion_service.get_emotion_list_by_user_ids(hit_user_ids, start_time, end_time, 0, 10)
        for emotion_screening in emotion_screening_list:
            hit_screening_list.append(emotion_screening)

        speech_service = SpeechService()
        speech_screening_list = await speech_service.get_speech_list_by_user_ids(hit_user_ids, start_time, end_time, 0, 10)
        for speech_screening in speech_screening_list:
            hit_screening_list.append(speech_screening)

        eyemovement_service = EyemovementService()
        em_screening_list = await eyemovement_service.get_screening_info_list_by_user_ids(hit_user_ids, start_time, end_time, 0, 10)
        for em_screening in em_screening_list:
            em_screening.type = "eyemovement"
            hit_screening_list.append(em_screening)

    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)

    h5_url_list = []
    crypt_key = os.getenv("CRYPT_KEY")
    for screening_info in hit_screening_list:
        if screening_info.type == "emotion":
            en_session_id = Crypt.encrypt(str(screening_info.session_id), crypt_key)
            url = "https://llm.ybbywb.com/emotion_report?en_session_id={}".format(en_session_id)
        elif screening_info.type == "hrv":
            en_screening_id = Crypt.encrypt(str(screening_info.idx), crypt_key)
            url = "https://llm.ybbywb.com/hrv_report?en_screening_id={}".format(en_screening_id)
        elif screening_info.type == "speech":
            en_screening_id = Crypt.encrypt(str(screening_info.idx), crypt_key)
            url = "https://llm.ybbywb.com/speech_report?en_screening_id={}".format(en_screening_id)
        elif screening_info.type == "eyemovement":
            screening_id = screening_info.id
            url = "https://llm.ybbywb.com/eyemovement2/report.html?screeningId={}".format(screening_id)
        else:
            continue
        h5_url_list.append({"type": screening_info.type, "report_url": url})

    data = {
        "report_list": h5_url_list,
        "idnum": idnum,
    }

    return APIResponse(
        code=0, message=_("获取成功"), data=data
    )
