from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from typing import Any, Dict, Optional
from app.models.base import Base, TimestampMixin, ModelUpdateMixin


class UploadRecordVideo(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'upload_record_video'

    record_id = Column(Integer, primary_key=True)
    user_id = Column(Integer, default=0)
    type_id = Column(Integer, default=0)
    type = Column(String, default="")
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<UploadRecordVideo(record_id={self.record_id})>"
