import os
import jwt
from datetime import datetime, timedelta
from sqlalchemy import select, and_, or_, func, desc, update

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.appconfig import AppConfig
from app.services.account_service import AccountService

class AppConfigService:

    def __init__(self):
        self.db_session = get_db

    async def get_config_by_name(self, name):
        try:
            async with self.db_session() as session:
                conditions = [
                    AppConfig.name == name,
                ]
                stmt = select(AppConfig).where(and_(*conditions))
                result = await session.execute(stmt)
                result_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_config_by_name: {str(e)}")
            raise

        return result_info

    async def get_config_by_device_id(self, device_id):

        config = await self.get_config_by_name(device_id)
        if not config:
            config_info = {}
        else:
            config_info = config.value

        if not config_info:
            config_info = {}

        account_service = AccountService()
        account_info = await account_service.get_acount_info_by_device_id(device_id)
        if account_info:
            config_info = account_info["config"] | config_info

        return config_info