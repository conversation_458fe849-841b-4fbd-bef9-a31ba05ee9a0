import cv2
import mediapipe as mp
import torch.nn as nn
import numpy as np


class FaceDetector(nn.Module):
    def __init__(self, margin=15):
        super(FaceDetector, self).__init__()
        self.margin = margin  # Margin for the bounding box
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(static_image_mode=False,
                                        max_num_faces=1,
                                        refine_landmarks=True,
                                        min_detection_confidence=0.5,
                                        min_tracking_confidence=0.5)

        # Initialize MediaPipe drawing tools
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles

    def forward(self, frame):
        # frame = frame.copy()
        frame_copy = np.copy(frame)
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        # Process the image to detect face mesh
        results = self.face_mesh.process(frame_rgb)

        # If a face is detected, crop and draw the face mesh
        if results.multi_face_landmarks:
            for face_landmarks in results.multi_face_landmarks:
                h, w, c = frame.shape
                face_coords = self.get_face_bbox(face_landmarks, w, h)
                
                x_min, y_min, x_max, y_max = face_coords
                face_crop = frame_copy[y_min:y_max, x_min:x_max]

                # Draw the face mesh on the original frame
                self.mp_drawing.draw_landmarks(
                    image=frame,
                    landmark_list=face_landmarks,
                    connections=self.mp_face_mesh.FACEMESH_TESSELATION,
                    landmark_drawing_spec=None,
                    connection_drawing_spec=self.mp_drawing_styles
                        .get_default_face_mesh_tesselation_style())
                
                self.mp_drawing.draw_landmarks(
                    image=frame,
                    landmark_list=face_landmarks,
                    connections=self.mp_face_mesh.FACEMESH_CONTOURS,
                    landmark_drawing_spec=None,
                    connection_drawing_spec=self.mp_drawing_styles
                        .get_default_face_mesh_contours_style())
                
                return frame, face_crop  # Return the cropped face image

        return frame, frame_copy  # Return the original frame if no face is detected

    def get_face_bbox(self, face_landmarks, width, height):
        """
        Get the square bounding box around the face with a margin.
        face_landmarks: Facial landmarks
        width, height: Width and height of the image
        """
        x_coords = [landmark.x * width for landmark in face_landmarks.landmark]
        y_coords = [landmark.y * height for landmark in face_landmarks.landmark]
        
        # Get min and max coordinates for the bounding box
        x_min, x_max = int(min(x_coords)), int(max(x_coords))
        y_min, y_max = int(min(y_coords)), int(max(y_coords))

        # Calculate width and height of the box
        box_width = x_max - x_min
        box_height = y_max - y_min

        # Determine the size of the square box (take the larger of width and height)
        square_size = max(box_width, box_height)

        # Calculate center of the box
        x_center = (x_min + x_max) // 2
        y_center = (y_min + y_max) // 2

        # Recalculate the square bounding box with a margin
        half_size = (square_size // 2) + self.margin
        x_min_square = max(0, x_center - half_size)
        y_min_square = max(0, y_center - half_size)
        x_max_square = min(width, x_center + half_size)
        y_max_square = min(height, y_center + half_size)

        return x_min_square, y_min_square, x_max_square, y_max_square
