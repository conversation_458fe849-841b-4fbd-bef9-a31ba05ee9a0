import json
from fastapi import Depends, Request, Response
from fastapi import APIRouter

from app.services.account_service import AccountService

router = APIRouter()

@router.post("/api/v1/account/login")
async def account_login(request: Request):
    body = await request.json()
    account_name = body.get("account_name")
    password = body.get("password")
    if not account_name or not password:
        return {"code": 108001, "message": "登录失败"}
    account_service = AccountService()
    result = account_service.login(account_name, password)
    if not result:
        return {"code": 108002, "message": "登录失败"}
    
    response_data = {
        "code": 0,
        "message": "",
        "token": result
    }
    response = Response(content=json.dumps(response_data), media_type="application/json")

    return response
