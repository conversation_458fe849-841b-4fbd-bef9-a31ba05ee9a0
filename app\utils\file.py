import os

class File:

    # 获取指定目录下的所有文件和目录
    def list_directory(self, path):
        
        entries = os.listdir(path)
        files = []
        directories = []
        
        for entry in entries:
            full_path = os.path.join(path, entry)
            if os.path.isdir(full_path):
                directories.append(entry)
            elif os.path.isfile(full_path):
                files.append(entry)
        
        return files, directories