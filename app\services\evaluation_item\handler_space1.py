
# 空间认知评估项目1处理
class HandlerSpace1:
    def __init__(self) -> None:
        pass

    # 通过收集评测的数据，计算需要的报告等数据
    def cal_result_info(self, collect_data):
        collect_data_sample = {
            "start_time": "",
            "run_data": [ # 每轮数据
                {
                    "cost_time": "",
                    "select_right_path_num": 1, # 正确路径次数
                    "select_wrong_path_num": 2,
                    "is_complete": 1, # 是否成功找到路径完成
                    "barrier_num": 3, # 障碍数
                    "path_num": 10, # 分叉路径数
                    "select_cost_time": ["", "", ""]
                },
            ],
            "collect_type": 0, # 操作方式：0点击
            "total_score": 5,
            "cost_time": "",
        }