name: llm-app
channels:
  - defaults
dependencies:
  - bzip2=1.0.8=h6c40b1e_6
  - ca-certificates=2024.7.2=hecd8cb5_0
  - libffi=3.4.4=hecd8cb5_1
  - ncurses=6.4=hcec6c5f_0
  - openssl=3.0.14=h46256e1_0
  - pip=24.0=py310hecd8cb5_0
  - python=3.10.14=h5ee71fb_1
  - readline=8.2=hca72f7f_0
  - setuptools=69.5.1=py310hecd8cb5_0
  - sqlite=3.45.3=h6c40b1e_0
  - tk=8.6.14=h4d00af3_0
  - wheel=0.43.0=py310hecd8cb5_0
  - xz=5.4.6=h6c40b1e_1
  - zlib=1.2.13=h4b97444_1
  - pip:
      - absl-py==2.1.0
      - aiohappyeyeballs==2.4.3
      - aiohttp==3.10.10
      - aiosignal==1.3.1
      - aliyun-python-sdk-core==2.15.1
      - aliyun-python-sdk-kms==2.16.3
      - annotated-types==0.7.0
      - anyio==4.4.0
      - async-timeout==4.0.3
      - asyncmy==0.2.9
      - attrs==24.2.0
      - azure-cognitiveservices-speech==1.38.0
      - azure-core==1.31.0
      - azure-storage-blob==12.23.1
      - certifi==2024.7.4
      - cffi==1.16.0
      - charset-normalizer==3.3.2
      - click==8.1.7
      - contourpy==1.3.0
      - crcmod==1.7
      - cryptography==43.0.0
      - cycler==0.12.1
      - dnspython==2.6.1
      - email-validator==2.2.0
      - exceptiongroup==1.2.2
      - fastapi==0.111.1
      - fastapi-cli==0.0.4
      - filelock==3.16.1
      - flatbuffers==24.3.25
      - fonttools==4.54.1
      - frozenlist==1.4.1
      - fsspec==2024.9.0
      - greenlet==3.0.3
      - h11==0.14.0
      - httpcore==1.0.5
      - httptools==0.6.1
      - httpx==0.27.0
      - idna==3.7
      - isodate==0.7.2
      - jinja2==3.1.4
      - jmespath==0.10.0
      - joblib==1.4.2
      - kiwisolver==1.4.7
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - matplotlib==3.9.2
      - mdurl==0.1.2
      - mediapipe==*******
      - mpmath==1.3.0
      - multidict==6.1.0
      - networkx==3.4
      - neurokit2==0.2.10
      - numpy==1.26.0
      - opencv-contrib-python==*********
      - opencv-python==*********
      - oss2==2.18.6
      - packaging==24.1
      - pandas==2.2.3
      - pillow==10.4.0
      - plotly==5.24.1
      - propcache==0.2.0
      - protobuf==3.20.3
      - pycparser==2.22
      - pycryptodome==3.20.0
      - pydantic==2.8.2
      - pydantic-core==2.20.1
      - pygments==2.18.0
      - pyparsing==3.1.4
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.0.1
      - python-multipart==0.0.9
      - pytz==2024.2
      - pyyaml==6.0.1
      - requests==2.32.3
      - rich==13.7.1
      - scikit-learn==1.5.2
      - scipy==1.14.1
      - shellingham==1.5.4
      - six==1.16.0
      - sniffio==1.3.1
      - sqlalchemy==2.0.32
      - sse-starlette==2.1.2
      - starlette==0.37.2
      - sympy==1.13.3
      - tenacity==9.0.0
      - threadpoolctl==3.5.0
      - torch==2.2.2
      - torchvision==0.17.2
      - typer==0.12.3
      - typing-extensions==4.12.2
      - tzdata==2024.2
      - urllib3==2.2.2
      - uvicorn==0.30.3
      - uvloop==0.19.0
      - watchfiles==0.22.0
      - websockets==12.0
      - yarl==1.15.3
prefix: /Users/<USER>/miniconda3/envs/llm-app
