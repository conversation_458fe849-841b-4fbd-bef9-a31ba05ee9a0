# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-21 12:21+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: app//locale/other_translate.py:6
msgid "森林寻宝"
msgstr ""

#: app//locale/other_translate.py:7
msgid "用于评估持续注意力、选择注意力、分配注意力和反应时间"
msgstr ""

#: app//controllers/user.py:27
msgid "成功"
msgstr ""

#: app//controllers/user.py:46 app//controllers/user.py:70
msgid "刷脸失败"
msgstr ""

#: app//controllers/user.py:50
msgid "没有账号信息"
msgstr ""

#: app//controllers/user.py:72 app//controllers/user.py:127
msgid "登录成功"
msgstr ""

#: app//controllers/user.py:99
msgid "登录失败"
msgstr ""

#: app//controllers/user.py:157
msgid "更新用户信息失败"
msgstr ""

#: app//controllers/user.py:160
msgid "更新用户信息成功"
msgstr ""

#: app//controllers/evaluation.py:28 app//controllers/evaluation.py:87
#: app//controllers/training.py:29 app//controllers/training.py:62
msgid "获取成功"
msgstr ""

#: app//controllers/evaluation.py:50
msgid "重新评估处理失败"
msgstr ""

#: app//controllers/evaluation.py:53
msgid "重新评估处理成功"
msgstr ""

#: app//controllers/evaluation.py:78 app//controllers/training.py:53
msgid "获取信息失败"
msgstr ""

#: app//controllers/evaluation.py:111
msgid "开始评估项目失败"
msgstr ""

#: app//controllers/evaluation.py:121 app//controllers/training.py:95
msgid "开始成功"
msgstr ""

#: app//controllers/evaluation.py:147
msgid "评估结束失败"
msgstr ""

#: app//controllers/evaluation.py:156 app//controllers/training.py:130
#: app//controllers/training.py:162
msgid "结束成功"
msgstr ""

#: app//controllers/evaluation.py:179 app//controllers/training.py:153
msgid "获取报告失败"
msgstr ""

#: app//controllers/evaluation.py:189
msgid "获取报告成功"
msgstr ""

#: app//controllers/tsc102.py:201
msgid "上传成功"
msgstr ""

#: app//controllers/tsc102.py:230
msgid "分析语音情绪失败"
msgstr ""

#: app//controllers/tsc102.py:255
msgid "分析语音情绪成功"
msgstr ""

#: app//controllers/training.py:86
msgid "开始训练失败"
msgstr ""

#: app//controllers/training.py:121
msgid "训练结束失败"
msgstr ""

#: app//services/training_service.py:25
msgid "注意力训练"
msgstr ""

#: app//services/training_service.py:30
msgid "记忆力训练"
msgstr ""

#: app//services/training_service.py:35
msgid "执行功能训练"
msgstr ""

#: app//services/training_service.py:40
msgid "语言能力训练"
msgstr ""

#: app//services/training_service.py:45
msgid "逻辑思维训练"
msgstr ""

#: app//services/training_service.py:50
msgid "空间认知训练"
msgstr ""

#: app//services/training_service.py:55
msgid "学习策略训练"
msgstr ""

#: app//services/training_service.py:60
msgid "社会认知训练"
msgstr ""

#: app//services/training_service.py:119 app//services/training_service.py:123
#: app//services/training_service.py:137 app//services/training_service.py:207
msgid "当前没有训练计划，请先进行评估"
msgstr ""

#: app//services/training_service.py:133
msgid "当前训练不在训练周期内"
msgstr ""

#: app//services/training_service.py:190
msgid "没有此训练项目计划"
msgstr ""

#: app//services/training_service.py:193
msgid "当前用户没有权限进行此训练项目"
msgstr ""

#: app//services/training_service.py:202 app//services/training_service.py:339
msgid "本次训练项目不在当前训练计划中"
msgstr ""

#: app//services/training_service.py:210 app//services/training_service.py:347
msgid "此训练项目已删除"
msgstr ""

#: app//services/training_service.py:213 app//services/training_service.py:324
msgid "此训练项目已完成"
msgstr ""

#: app//services/training_service.py:234
msgid "该项目训练计划已全部完成"
msgstr ""

#: app//services/training_service.py:318
msgid "该项目训练不存在"
msgstr ""

#: app//services/training_service.py:321
msgid "当前用户没有权限进行此训练"
msgstr ""

#: app//services/training_service.py:330
msgid "该项目训练计划不存在"
msgstr ""

#: app//services/training_service.py:344
msgid "该训练计划不存在"
msgstr ""

#: app//services/training_service.py:350
msgid "此训练项目计划已完成"
msgstr ""

#: app//services/training_service.py:490
#: app//services/evaluation_service.py:473
msgid "{}报告"
msgstr ""

#: app//services/training_service.py:544
msgid "此训练不存在"
msgstr ""

#: app//services/training_service.py:546
msgid "此训练还未完成"
msgstr ""

#: app//services/training_service.py:548
#: app//services/evaluation_service.py:540
msgid "报告还未生成，请稍后"
msgstr ""

#: app//services/training_service.py:550
#: app//services/evaluation_service.py:542
msgid "当前用户无权限查看此报告"
msgstr ""

#: app//services/training_service.py:561
#: app//services/evaluation_service.py:553
msgid "男"
msgstr ""

#: app//services/training_service.py:561
#: app//services/evaluation_service.py:553
msgid "女"
msgstr ""

#: app//services/user_service.py:60 app//services/user_service.py:78
#: app//services/user_service.py:136
msgid "用户不存在"
msgstr ""

#: app//services/user_service.py:63
msgid "用户密码已修改需重新登录"
msgstr ""

#: app//services/user_service.py:83
msgid "密码不正确"
msgstr ""

#: app//services/user_service.py:231
msgid "图片质量无法识别人脸"
msgstr ""

#: app//services/user_service.py:234
msgid "请重新刷脸，尽量拍到清晰完整脸部"
msgstr ""

#: app//services/user_service.py:325
msgid "一"
msgstr ""

#: app//services/user_service.py:326
msgid "二"
msgstr ""

#: app//services/user_service.py:327
msgid "三"
msgstr ""

#: app//services/user_service.py:328
msgid "四"
msgstr ""

#: app//services/user_service.py:329
msgid "五"
msgstr ""

#: app//services/user_service.py:330
msgid "六"
msgstr ""

#: app//services/user_service.py:331
msgid "七"
msgstr ""

#: app//services/user_service.py:332
msgid "八"
msgstr ""

#: app//services/user_service.py:333
msgid "九"
msgstr ""

#: app//services/user_service.py:334
msgid "十"
msgstr ""

#: app//services/user_service.py:338 app//services/user_service.py:356
msgid "未知"
msgstr ""

#: app//services/user_service.py:340
msgid "幼儿园"
msgstr ""

#: app//services/user_service.py:344
msgid "{}年级"
msgstr ""

#: app//services/user_service.py:349
msgid "初{}"
msgstr ""

#: app//services/user_service.py:354
msgid "高{}"
msgstr ""

#: app//services/evaluation_service.py:23
msgid "注意力评估"
msgstr ""

#: app//services/evaluation_service.py:24
msgid "测试持续注意力和选择性注意力水平"
msgstr ""

#: app//services/evaluation_service.py:29
msgid "记忆力评估"
msgstr ""

#: app//services/evaluation_service.py:30
msgid "评估短期记忆和工作记忆能力"
msgstr ""

#: app//services/evaluation_service.py:35
msgid "执行功能评估"
msgstr ""

#: app//services/evaluation_service.py:36
msgid "测试计划、组织和执行能力"
msgstr ""

#: app//services/evaluation_service.py:41
msgid "语言能力评估"
msgstr ""

#: app//services/evaluation_service.py:42
msgid "评估语言理解和表达能力"
msgstr ""

#: app//services/evaluation_service.py:47
msgid "逻辑思维评估"
msgstr ""

#: app//services/evaluation_service.py:48
msgid "测试推理和问题解决能力"
msgstr ""

#: app//services/evaluation_service.py:53
msgid "空间认知评估"
msgstr ""

#: app//services/evaluation_service.py:54
msgid "评估空间感知和操作能力"
msgstr ""

#: app//services/evaluation_service.py:59
msgid "学习策略评估"
msgstr ""

#: app//services/evaluation_service.py:60
msgid "测试学习方法和策略运用"
msgstr ""

#: app//services/evaluation_service.py:65
msgid "社会认知评估"
msgstr ""

#: app//services/evaluation_service.py:66
msgid "评估社交理解和互动能力"
msgstr ""

#: app//services/evaluation_service.py:222
msgid "该评估下没有评估项目"
msgstr ""

#: app//services/evaluation_service.py:226
msgid "生成评估失败"
msgstr ""

#: app//services/evaluation_service.py:231
msgid "本次评估不存在"
msgstr ""

#: app//services/evaluation_service.py:235
msgid "本次评估项目不存在"
msgstr ""

#: app//services/evaluation_service.py:271
#: app//services/evaluation_service.py:308
msgid "此评估项目不存在"
msgstr ""

#: app//services/evaluation_service.py:281
#: app//services/evaluation_service.py:317
msgid "此评估项目不是正在进行的评估"
msgstr ""

#: app//services/evaluation_service.py:284
#: app//services/evaluation_service.py:320
msgid "此评估项目已删除"
msgstr ""

#: app//services/evaluation_service.py:287
#: app//services/evaluation_service.py:323
msgid "此评估项目已完成"
msgstr ""

#: app//services/evaluation_service.py:326
msgid "此评估项目还未开始"
msgstr ""

#: app//services/evaluation_service.py:538
msgid "此评估还未完成"
msgstr ""
