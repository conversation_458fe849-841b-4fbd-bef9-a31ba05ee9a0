from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.training_plan_result import TrainingPlanResult


class TrainingPlanResultService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_training_plan_result(self, new_training_plan_result):
        try:
            async with self.db_session() as session:
                session.add(new_training_plan_result)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_training_plan_result: {str(e)}")
            raise

        return new_training_plan_result
    

    async def get_plan_result_by_id(self, train_plan_result_id):
        try:
            async with self.db_session() as session:
                stmt = select(TrainingPlanResult).where(TrainingPlanResult.train_plan_result_id == train_plan_result_id)
                result = await session.execute(stmt)
                result_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_plan_result_by_id: {str(e)}")
            raise

        return result_info
    
    async def get_plan_result_list_by_ids(self, train_plan_result_ids):
        try:
            async with self.db_session() as session:
                stmt = select(TrainingPlanResult).where(TrainingPlanResult.train_plan_result_id.in_(train_plan_result_ids))
                result = await session.execute(stmt)
                result_info = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_plan_result_list_by_ids: {str(e)}")
            raise

        return result_info
    
    async def get_plan_result_list_by_user_id(self, user_id, category, status, start = 0, limit = 10):
        try:
            async with self.db_session() as session:
                conditions = [
                    TrainingPlanResult.user_id == user_id,
                    TrainingPlanResult.category == category,
                    TrainingPlanResult.status.in_(status)
                ]
                query = select(TrainingPlanResult).where(and_(*conditions)).offset(start).limit(limit)
                order = desc(TrainingPlanResult.train_plan_result_id)
                query = query.order_by(order)
                result = await session.execute(query)
                result_info = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_plan_result_list_by_user_id: {str(e)}")
            raise

        return result_info
    
    async def update_plan_result_by_id(self, train_plan_result_id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(TrainingPlanResult).where(TrainingPlanResult.train_plan_result_id == train_plan_result_id)
                result = await session.execute(stmt)
                result_info = result.scalars().first()

                if not result_info:
                    return False

                await result_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_plan_result_by_id: {str(e)}")
            raise

        return True