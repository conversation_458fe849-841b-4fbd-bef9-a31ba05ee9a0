from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from datetime import datetime

from app.models.base import Base, TimestampMixin, ModelUpdateMixin



class EvaluationItemResult(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'evaluation_item_results'

    item_result_id = Column(Integer, primary_key=True)
    category = Column(String(100), default="")
    result_id = Column(Integer, default=0)
    item_id = Column(Integer, default=0)
    user_id = Column(Integer, default=0)
    account_id = Column(Integer, default=0)
    collect_json = Column(JSON, default=dict)
    result_json = Column(JSON, default=dict)
    start_time = Column(DateTime, default=None)
    end_time = Column(DateTime, default=None)
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<EvaluationItemResult(item_result_id={self.item_result_id}, category={self.category}, result_id={self.result_id}, user_id={self.user_id})>"
