import time
import asyncio


async def async_sleep(idx):
    print(f"async_sleep_{idx} enter")
    await asyncio.sleep(3)
    print(f"async_sleep_{idx} return")


async def sync_sleep(idx):
    print(f"sync_sleep_{idx} enter")
    time.sleep(3)
    print(f"sync_sleep_{idx} return")


async def main():
    start_time = time.perf_counter()

    t1 = asyncio.create_task(async_sleep(1))
    t2 = asyncio.create_task(async_sleep(2))
    t3 = asyncio.create_task(async_sleep(3))

    await t1
    await t2
    await t3
    duration = time.perf_counter() - start_time
    print(f"call async_sleep cost: {duration} seconds")

    start_time = time.perf_counter()
    t1 = asyncio.create_task(sync_sleep(1))
    t2 = asyncio.create_task(sync_sleep(2))
    t3 = asyncio.create_task(sync_sleep(3))

    await t1
    await t2
    await t3
    duration = time.perf_counter() - start_time
    print(f"call sync_sleep cost: {duration} seconds")


if __name__ == '__main__':
    asyncio.run(main())
