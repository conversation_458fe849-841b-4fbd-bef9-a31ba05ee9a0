from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from datetime import datetime, timedelta
import logging
from sqlalchemy.exc import SQLAlchemyError
import httpx
import os

from app.models.screening import Screening
from app.core.logger import main_logger
from app.core.database import get_db

logger = logging.getLogger(__name__)

class SpeechService:
    def __init__(self, db_session: AsyncSession = None, request = None):
        self.db_session = get_db
        self.request = request
        self.device_id = ""

    async def list_speech_report_by_device_id(self, device_id_list, start, limit):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.device_id.in_(device_id_list),
                    Screening.type == "speech"
                ]
                query = select(Screening).where(and_(*conditions)).offset(start).limit(limit)
                order = desc(Screening.idx)
                query = query.order_by(order)
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return []

    async def get_speech_by_id(self, id) -> dict:
        try:
            async with self.db_session() as session:
                stmt = select(Screening).where(Screening.idx == id)
                result = await session.execute(stmt)
                screening_info = result.scalars().first()
                if screening_info:
                    screening_info = screening_info.__dict__
        except Exception as e:
            main_logger.error(f"Unexpected error in get_speech_by_id: {str(e)}")
            raise

        return screening_info
    
    async def get_speech_list_by_user_ids(self, user_ids, start_time = None, end_time = None, start = 0, limit = 20):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.user_id.in_(user_ids),
                    Screening.type == "speech"
                ]
                if start_time:
                    conditions.append(Screening.created >= start_time)
                if end_time:
                    conditions.append(Screening.created < end_time)
                query = select(Screening).where(and_(*conditions)).offset(start).limit(limit)
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in get_speech_list_by_user_ids: {str(e)}")
            raise

        return []

    async def save_result(self, speech_result, user_id):
        try:
            async with self.db_session() as session:
                screening = Screening(
                    type='speech',
                    user_id=user_id,
                    device_id=self.device_id,
                    session_id="",
                    origin_data=speech_result["orig_blob_name"],
                    process_result=speech_result,
                    created=datetime.utcnow(),
                    updated=datetime.utcnow()
                )
                session.add(screening)
                await session.commit()
        except ValueError as ve:
            logger.error(f"Value error in save_result: {str(ve)}")
            raise
        except SQLAlchemyError as e:
            logger.error(f"Database error in save_result: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return screening

    async def analyize(self, audio_url, language, noise):

        speech_lm_service_host = os.getenv("SPEECH_LM_SERVICE_HOST")
        timeout = httpx.Timeout(
            connect=10.0,  # 连接超时（秒）
            read=30.0,  # 读取超时（秒）
            write=10.0,  # 写入超时（秒）
            pool=None  # 连接池超时（秒），可以设置为None以使用默认值
        )
    
        async with httpx.AsyncClient(timeout=timeout) as client:
            speech_service_addr = speech_lm_service_host + '/analyze_audio'
            headers = {
                "X-Forwarded-By": "audio_lm_service",
                "Content-Type": "application/json; charset=utf-8",
            }
            body = {
                "audio_url": audio_url,
                "language": language,
                "noise": noise
            }
            result = await client.post(speech_service_addr, headers=headers, json=body)

        return result

    def format_emotions(self, emotions):

        emotion_percentages = {}
        positive = {
            "happy": [
                [_("显示良好的基础心理状态"), _("反映出对生活的满意度较高"), _("表明具有积极的生活态度")],
                [_("处于正常波动范围内")],
                [_("未检测到明显表现")]
            ],
            "surprised": [
                [_("体现对生活保持新鲜感和期待"), _("显示较强的情绪适应性和心理弹性"), _("反映出对新事物持开放态度")],
                [_("处于正常波动范围内")],
                [_("未检测到明显表现")]
            ],
        }
        negative = {
            "angry": [
                [_("强烈的愤怒反应可能由重大挫折引起，需要适当的方式来释放情绪"), _("个体可能会有显著的生理反应，如声音变得低沉或颤抖")],
                [_("处于正常波动范围内")],
                [_("未检测到明显表现")]
            ],
            "disgust": [
                [_("可能对个体的生活产生较大影响，需要关注"), _("如果厌恶情绪影响到日常活动，建议寻求适当的支持来帮助应对")],
                [_("处于正常波动范围内")],
                [_("未检测到明显表现")]
            ],
            "fear": [
                [_("可能与个体面临的重大压力或威胁有关"), _("可能伴随着生理反应，如心跳加快"), _("需要适当的放松技巧或支持来减轻恐惧感")],
                [_("处于正常波动范围内")],
                [_("未检测到明显表现")]
            ],
            "sad": [
                [_("通常与重大失落或创伤事件有关"), _("如果持续较长时间，可能需要寻求专业的心理支持")],
                [_("处于正常波动范围内")],
                [_("未检测到明显表现")]
            ],
        }
        neutral = {
            "neutral": [
                [_("表现出良好的情绪稳定性"), _("具备理性思考和判断能力"), _("显示情绪调节功能正常")],
                [_("处于正常波动范围内")],
                [_("未检测到明显表现")]
            ],
        }
        text = {
            'happy': _("幸福感"),
            'surprised': _("惊喜"),
            'neutral': _("平和"),
            'angry': _("愤怒"),
            'disgust': _("厌恶"),
            'fear': _("恐惧"),
            'sad': _("悲伤")
        }

        new_emotions = {}
        for key, value in positive.items():
            new_emotions[key] = emotions.get(key, 0.0)
        for key, value in neutral.items():
            new_emotions[key] =  emotions.get(key, 0.0)
        for key, value in negative.items():
            new_emotions[key] = emotions.get(key, 0.0)
        


        positive_emotion = {"percent": 0, "detail": []}
        neutral_emotion = {"percent": 0, "detail": []}
        negative_emotion = {"percent": 0, "detail": []}

        for key, value in new_emotions.items():

            emotion_percentages[key] = round(value * 100)
            if emotion_percentages[key] >= 20:
                percent_level = 0
            elif emotion_percentages[key] == 0:
                percent_level = 2
            else:
                percent_level = 1

            if key in positive.keys():
                positive_emotion["percent"] += emotion_percentages[key]
                positive_emotion["detail"].append(
                    {
                        "text": text[key],
                        "percent": emotion_percentages[key],
                        "tips": positive[key][percent_level]
                    }
                )
            if key in neutral.keys():
                neutral_emotion["percent"] += emotion_percentages[key]
                neutral_emotion["detail"].append(
                    {
                        "text": text[key],
                        "percent": emotion_percentages[key],
                        "tips": neutral[key][percent_level]
                    }
                )
            if key in negative.keys():
                negative_emotion["percent"] += emotion_percentages[key]
                negative_emotion["detail"].append(
                    {
                        "text": text[key],
                        "percent": emotion_percentages[key],
                        "tips": negative[key][percent_level]
                    }
                )

        # 活跃度权重
        arousal_weights = {
            'happy': 0.7,
            'surprised': 0.9,
            'neutral': 0.3,
            'angry': 0.8,
            'disgust': 0.5,
            'fear': 0.7,
            'sad': 0.2
        }
        
        # 效价权重
        valence_weights = {
            'happy': 0.8,
            'surprised': 0.6,
            'neutral': 0.0,
            'angry': -0.9,
            'disgust': -0.8,
            'fear': -0.7,
            'sad': -0.6
        }
        
        # 计算总活跃度
        # arousal = sum(
        #     percentage * arousal_weights[emotion]
        #     for emotion, percentage in emotion_percentages.items()
        # )
        arousal = 0.0
        positive_score = 0.0
        neutral_score = 0.0
        negative_score = 0.0
        for emotion, percentage in emotion_percentages.items():
            arousal += percentage * arousal_weights[emotion]
            score = percentage * arousal_weights[emotion]
            if emotion in positive.keys():
                positive_score += score
            if emotion in neutral.keys():
                neutral_score += score
            if emotion in negative.keys():
                negative_score += score

        positive_score_pecent = round((positive_score / arousal) * 100)
        neutral_score_pecent = round((neutral_score / arousal) * 100)
        negative_score_pecent = round((negative_score / arousal) * 100)

        arousal_text = ""
        if arousal > 60.0:
            arousal_text = _("总体呈现中等偏上水平")
        elif arousal > 40.0 and arousal <= 60.0:
            arousal_text = _("总体呈现中等水平")
        else:
            arousal_text = _("总体呈现中低等水平")
        
        first_text = ""
        second_text = ""
        if positive_emotion["percent"] > 60.0:
            first_text = _("积极情绪占主导（{}%）,显示良好的情绪活力").format(positive_emotion["percent"])
        elif positive_emotion["percent"] > 20.0 and positive_emotion["percent"] <= 60.0:
            second_text = _("积极情绪占({}%)，显示较好的情绪活力").format(positive_emotion["percent"])
        if neutral_emotion["percent"] > 60.0:
            first_text = _("中性情绪占主导（{}%），体现良好的情绪调节能力").format(neutral_emotion["percent"])
        elif neutral_emotion["percent"] > 20.0 and neutral_emotion["percent"] <= 60.0:
            second_text = _("中性情绪占({}%)，体现较好的情绪调节能力").format(neutral_emotion["percent"])
        if negative_emotion["percent"] > 60.0:
            first_text = _("消极情绪占主导（{}%），显示情绪调节能力需要提升").format(negative_emotion["percent"])
        elif negative_emotion["percent"] > 20.0 and negative_emotion["percent"] <= 60.0:
            second_text = _("消极情绪占({}%)，显示情绪调节能力需要提升").format(negative_emotion["percent"])
        
        arousal_result = {
            "arousal": arousal,
            "arousal_text": arousal_text,
            "first_text": first_text,
            "second_text": second_text,
        }

        # 计算总效价
        # valence = sum(
        #     percentage * valence_weights[emotion]
        #     for emotion, percentage in emotion_percentages.items()
        # )
        valence = 0.0
        positive_score = 0.0
        neutral_score = 0.0
        negative_score = 0.0
        for emotion, percentage in emotion_percentages.items():
            valence += percentage * valence_weights[emotion]
            score = percentage * valence_weights[emotion]
            if emotion in positive.keys():
                positive_score += score
            if emotion in neutral.keys():
                neutral_score += score
            if emotion in negative.keys():
                negative_score += score
        
        valence_text = ""
        first_text = ""
        second_text = ""
        total_status = {}
        if valence > 20.0:
            valence_text = _("整体呈现显著正向特征")
            first_text = _("正向情绪（幸福+惊喜）占比（{}%）").format(positive_emotion["percent"])
            second_text = _("负向情绪占比（{}%）").format(negative_emotion["percent"])
            total_status = {"status": 'positive', "text": _("偏向积极，具有较好的情绪基础")}
        elif valence > -20.0 and valence <= 20.0:
            valence_text = _("整体呈现中性特征")
            first_text = _("中性情绪占比（{}%）").format(neutral_emotion["percent"])
            # second_text = "正向情绪占比（{}%）".format(positive_emotion["percent"])
            total_status = {"status": 'neutral', "text": _("平和，具有较好的情绪调节能力")}
        else:
            valence_text = _("整体呈现负向特征")
            first_text = _("负向情绪占比（{}%）").format(negative_emotion["percent"])
            second_text = _("正向情绪占比（{}%）").format(positive_emotion["percent"])
            total_status = {"status": 'negative', "text": _("偏向消极，需要提高情绪调节能力")}

        valence_result = {
            "valence": valence,
            "valence_text": valence_text,
            "first_text": first_text,
            "second_text": second_text,
        }

        return total_status, new_emotions, positive_emotion, neutral_emotion, negative_emotion, arousal_result, valence_result