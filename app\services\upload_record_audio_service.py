from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta
import os
from pydub import AudioSegment

from app.models.upload_record_audio import UploadRecordAudio
from app.core.database import get_db
from app.core.logger import main_logger


class UploadRecordAudioService:
    def __init__(self, request = None):
        self.db_session = get_db
        self.request = request
        self.tmp_dir = "tmp/upload_records/audio"

    async def insert(self, user_id):

        new_record = UploadRecordAudio(
            user_id=user_id,
            status=1,
            extra_json={}
        )
        try:
            async with self.db_session() as session:
                session.add(new_record)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return new_record.__dict__


    async def get_by_record_id(self, record_id) -> dict:
        try:
            async with self.db_session() as session:
                stmt = select(UploadRecordAudio).where(UploadRecordAudio.record_id == record_id)
                result = await session.execute(stmt)
                record_info = result.scalars().first()
                if record_info:
                    record_info = record_info.__dict__
        except Exception as e:
            main_logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return record_info
    
    async def update_record_by_id(self, record_id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(UploadRecordAudio).where(UploadRecordAudio.record_id == record_id)
                result = await session.execute(stmt)
                result_info = result.scalars().first()

                if not result_info:
                    return False

                await result_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_record_by_id: {str(e)}")
            raise

        return True

    async def merge_audio_chunks(self, record_id: int):
        chunks_dir = f"{self.tmp_dir}/{record_id}"
        output_path_webm = f"{record_id}.webm"
        
        chunk_files = sorted([f for f in os.listdir(chunks_dir) if f.startswith('chunk_')])
        output_file_webm = os.path.join(chunks_dir, output_path_webm)

        with open(output_file_webm, 'wb') as outfile:
            for chunk_file in chunk_files:
                chunk_path = os.path.join(chunks_dir, chunk_file)
                with open(chunk_path, 'rb') as infile:
                    outfile.write(infile.read())
                os.remove(chunk_path)  # 删除已合并的分片

        output_path_mp3 = f"{record_id}.mp3"
        output_file_mp3 = os.path.join(chunks_dir, output_path_mp3)

        # 转换为MP3
        audio_segment = AudioSegment.from_file(output_file_webm)
        audio_segment.export(output_file_mp3, format="mp3")
        
        # 删除原始webm文件
        os.remove(output_file_webm)
        
        return output_file_mp3

    async def cleanup_chunks(self, record_id: int):
        chunks_dir = f"{self.tmp_dir}/{record_id}"
        try:
            for chunk_file in os.listdir(chunks_dir):
                os.remove(os.path.join(chunks_dir, chunk_file))
            os.rmdir(chunks_dir)
        except Exception as e:
            print(f"Error cleaning up chunks: {e}")
