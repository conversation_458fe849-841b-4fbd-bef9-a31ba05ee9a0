from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from app.models.base import Base, TimestampMixin


class EvaluationItem(Base, TimestampMixin):
    __tablename__ = 'evaluation_items'

    item_id = Column(Integer, primary_key=True)
    category = Column(String(100), default="")
    name = Column(String(100), default="")
    desc = Column(String(2000), default="")
    key = Column(String(100), default="")
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<EvaluationItem(item_id={self.item_id}, name={self.name}, key={self.key}, category={self.category})>"
