from fastapi import APIRouter, HTTPException, Request
from fastapi import Depends
from pydantic import BaseModel, Field
import traceback
import json
from typing import Any, Dict

from app.core.decorators import require_user_token
from app.schemas.base import APIResponse
from app.core.logger import main_logger
from app.core.global_var import GlobalVar
from app.services.user_service import UserService
from app.services.training_service import TrainingService


router = APIRouter()

# 训练分类列表
@router.post("/list_training")
@require_user_token()
async def list_training(request: Request):

    current_user_info = request.state.user
    current_account_info = request.state.account

    training_service = TrainingService()
    list = await training_service.get_category_list(current_user_info["user_id"])
    return APIResponse(
        code=0, message=_("获取成功"), data=list
    )

# 获取分类下的训练项目，以及计划完成情况
class ItemListRequest(BaseModel):
    category: str = Field(..., description="category key")
@router.post("/get_training_item_list")
@require_user_token()
async def get_training_item_list(request: Request, item_list_request: ItemListRequest):

    current_user_info = request.state.user
    current_account_info = request.state.account
    
    category = item_list_request.category

    try:
        training_service = TrainingService()
        result = await training_service.get_item_list_by_category(category, current_user_info["user_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10083, message=_("获取信息失败"))

    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    return APIResponse(
        code=0, message=_("获取成功"), data=data
    )

# 开始训练计划项目
class StartItemRequest(BaseModel):
    train_item_plan_id: int = Field(..., description="training item ID")
@router.post("/start_training_item")
@require_user_token()
async def start_training_item(request: Request, start_item_request: StartItemRequest):

    current_user_info = request.state.user
    current_account_info = request.state.account

    train_item_plan_id = start_item_request.train_item_plan_id

    try:
        training_service = TrainingService()
        result = await training_service.start_item(train_item_plan_id, current_user_info["user_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10085, message=_("开始训练失败"))
    
    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    return APIResponse(
        code=0, message=_("开始成功"), data=data
    )

# 结束训练项目
class EndItemRequest(BaseModel):
    train_item_result_id: int = Field(..., description="training item result ID")
    item_collect_data: Dict[str, Any]
@router.post("/end_training_item")
@require_user_token()
async def end_training_item(request: Request, end_item_request: EndItemRequest):

    current_user_info = request.state.user
    current_account_info = request.state.account

    train_item_result_id = end_item_request.train_item_result_id
    item_collect_data = end_item_request.item_collect_data

    try:
        training_service = TrainingService()
        result = await training_service.end_item(train_item_result_id, current_user_info["user_id"], item_collect_data)
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10087, message=_("训练结束失败"))

    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    return APIResponse(
        code=0, message=_("结束成功"), data=data
    )

class ReportRequest(BaseModel):
    train_plan_result_id: int = Field(..., description="training plan ID")
@router.post("/get_report")
@require_user_token()
async def get_report(request: Request, report_request: ReportRequest):

    current_user_info = request.state.user
    current_account_info = request.state.account

    train_plan_result_id = report_request.train_plan_result_id

    try:
        training_service = TrainingService()
        report_info = await training_service.get_report_info(train_plan_result_id, current_user_info["user_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        report_info = {}

    if not report_info:
        return APIResponse(code=10083, message=_("获取报告失败"))

    code = report_info.get("code", 0)
    msg = report_info.get("msg", "")
    data = report_info.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    return APIResponse(
        code=0, message=_("结束成功"), data=data
    )