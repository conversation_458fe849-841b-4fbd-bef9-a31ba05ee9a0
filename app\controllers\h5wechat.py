import base64
import json
import logging
import os
import uuid
from datetime import datetime
from typing import Dict
import dotenv

import httpx
from fastapi import BackgroundT<PERSON>s, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from sse_starlette.sse import EventSourceResponse
from fastapi import APIRouter, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.core.database import get_db
from app.models.message import Message
from app.utils.oss import Oss
from app.utils.speech import Speech
from app.utils.word import Word
from app.utils.file import File as FileHandler

dotenv.load_dotenv()

speech = Speech()
oss = Oss()

router = APIRouter()

tasks: Dict[str, Dict] = {}

@router.get("/api/v1/messages/{user_id}")
async def get_messages(user_id: str, db: AsyncSession = Depends(get_db)):
    async with db as session:
        result = await session.execute(select(Message).where(Message.user_id == user_id))
        messages = result.scalars().all()

        if not messages:
            raise HTTPException(status_code=404, detail="No messages found for this user")

        return messages


@router.post("/openapi/internal/chat/stream")
async def chat_stream(request: Request, db: AsyncSession = Depends(get_db)):

    body = await request.json()
    last_message = body["messages"][-1]["content"]
    user = body.get("user", {"id": "anonymous", "name": "anonymous"})
    scene = body.get("scene", {"id": "e2f58fca-62fc-4754-b400-2bc3e90898e9", "source": "ios"})
    logging.info(f"Received JSON: {body}")

    timeout = httpx.Timeout(
        connect=10.0,  # 连接超时（秒）
        read=30.0,  # 读取超时（秒）
        write=10.0,  # 写入超时（秒）
        pool=None  # 连接池超时（秒），可以设置为None以使用默认值
    )

    async with httpx.AsyncClient(timeout=timeout) as client:
        required_api_key = 'ts-mftR3Z6KMgsi2PmQrc6MA4EFaiQ6sguvG4ZqtMo63SpYfeCtEnQ8'
        llm_api_addr = 'https://llm.higrace.life:8080/api/v1/chat/add_message'
        # llm_api_addr = 'http://127.0.0.1:8001/api/v1/chat/add_message'
        headers = {
            "X-Forwarded-By": "ts_llm_service",
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": required_api_key
        }
        body = {
            "user": {
                "id": user["id"],
                "name": user["name"],
                "device": "D2E8091F-5973-44E0-B47A-117713F760A0"
            },
            "agent": {
                "name": "sophie",
                "role": "doctor"
            },
            "event": {
                "user_contents": [
                    {
                        "text": last_message
                    }
                ],
                "type": "text",
                "category": "cbt"
            },
            "scene": {
                "id": scene["id"],
                "source": scene["source"]
            }
        }
        upstream_response = await client.post(llm_api_addr, headers=headers, json=body)
        logging.info(f"upstream response: {upstream_response}")

    if upstream_response.status_code == 200:
        print(f"yield: {upstream_response.text}")

        body = json.loads(upstream_response.text)
        event = body["data"]["event"]
        answer = event["agent_contents"][0]["text"]
        async with db as session:
            user_message = Message(
                owner=1,
                user_id=user["id"],
                session_id=scene["id"],
                text_data=last_message,
                created=datetime.utcnow(),
                updated=datetime.utcnow()
            )

            session.add(user_message)

            agent_message = Message(
                owner=2,
                user_id=user["id"],
                session_id=scene["id"],
                text_data=answer,
                created=datetime.utcnow(),
                updated=datetime.utcnow()
            )

            session.add(agent_message)
            await session.commit()

    async def event_generator():
        loop_count = 0
        finished = False
        while True:
            if await request.is_disconnected() or finished:
                logging.info("disconnected")
                break
            # 模拟数据流，可以替换为实际的数据生成逻辑
            response_data = {
                "error_code": 0,
                "message": 'ok',
                "data": {
                    "content": answer,
                    "usage": "null",
                    "end": "true"
                }
            }
            yield {
                "event": "message",
                "id": "message_id",
                "retry": 10000,
                "data": json.dumps(response_data, ensure_ascii=False)
            }
            finished = True

    return EventSourceResponse(event_generator())


async def generate_audio_task(task_id: str, request_id: str, text: str):
    temp_dir = os.getenv("TEMP_DIR")
    voice_path = os.path.join(temp_dir, f"{uuid.uuid4()}.mp3")

    logging.debug("before text_to_speech")
    speech.text_to_speech(voice_path, text=text)
    logging.debug("after text_to_speech")

    voice_url = oss.upload("voices", voice_path)
    logging.info(f"voice url: {voice_url}")

    tasks[task_id] = {"requestId": request_id, "url": voice_url}


@router.post("/audio/long/generate")
async def generate_audio(request: Request, background_tasks: BackgroundTasks):
    body = await request.json()
    logging.info(f"Received JSON: {body}")

    text = body['text']
    task_id = str(uuid.uuid4())
    request_id = str(uuid.uuid4())
    tasks[task_id] = {"request_id": request_id, "url": None}
    background_tasks.add_task(generate_audio_task, task_id, request_id, body['text'])
    logging.info(f"{task_id}, {request_id}, {text}")
    return JSONResponse(content={"data": {"taskId": task_id, "requestId": request_id}})


@router.post("/audio/long/generate2")
async def generate_audio2(request: Request, background_tasks: BackgroundTasks):
    body = await request.json()
    logging.info(f"Received JSON: {body}")

    text = body['text']
    temp_dir = os.getenv("TEMP_DIR")
    voice_path = os.path.join(temp_dir, f"{uuid.uuid4()}.mp3")
    await speech.text_to_speech_async(voice_path, text=text)
    voice_url = await oss.upload_async("voices", voice_path)
    logging.info(f"voice url: {voice_url}")
    return JSONResponse(content={"data": voice_url})


@router.get("/audio/long/result")
async def get_audio(request: Request):
    params = request.query_params
    for key, value in params.items():
        logging.debug(f"{key}: {value}")

    task_id = params.get("taskId")
    request_id = params.get("requestId")

    task = tasks.get(task_id)
    if not task or task["requestId"] != request_id:
        logging.warning(f"task not found: {task_id}")
        raise HTTPException(status_code=404, detail="Task not found or invalid requestId")
    if not task["url"]:
        logging.warning(f"task is still running: {task_id}")
        raise HTTPException(status_code=202, detail="Task is still processing")

    url = task["url"]
    logging.info(f"{task_id=}, {request_id=}, {url=}")
    return JSONResponse(content={"data": url})


@router.post("/audio/speech/recognizer")
async def recognize_audio(request: Request):
    body = await request.json()
    # logging.info(f"Received JSON: {body}")

    audio_data = body["data"]
    audio_binary = base64.b64decode(audio_data)
    temp_dir = os.getenv("TEMP_DIR")
    audio_path = os.path.join(temp_dir, f"{uuid.uuid4()}.mp3")
    with open(audio_path, "wb") as audio_file:
        audio_file.write(audio_binary)

    question = speech.speech_to_text(audio_path)
    logging.info(f"{audio_path=}, {question=}")
    return JSONResponse(content={"data": question})


@router.get("/wechat/auth")
async def login_wechat(code: str):
    # 微信开放平台的配置
    # WECHAT_APP_ID = 'wx2bfb60e18f059602'
    WECHAT_APP_ID = 'wx9d8af6e1f786750f'
    WECHAT_APP_SECRET = '399ddef5bd1c0c3d515450019185003f'
    WECHAT_TOKEN_URL = 'https://api.weixin.qq.com/sns/oauth2/access_token'
    WECHAT_USERINFO_URL = 'https://api.weixin.qq.com/sns/userinfo'

    # 使用授权码获取访问令牌
    payload = {
        'appid': WECHAT_APP_ID,
        'secret': WECHAT_APP_SECRET,
        'code': code,
        'grant_type': 'authorization_code'
    }
    async with httpx.AsyncClient() as client:
        token_response = await client.get(WECHAT_TOKEN_URL, params=payload)

        token_data = token_response.json()
        if "access_token" not in token_data:
            raise HTTPException(status_code=400, detail="WeChat token request failed")
        logging.info(f"token_data: {token_data}")

        # 使用访问令牌获取用户信息
        userinfo_payload = {
            'access_token': token_data['access_token'],
            'openid': token_data['openid']
        }
        userinfo_response = await client.get(WECHAT_USERINFO_URL, params=userinfo_payload)

        user_info = userinfo_response.json()
        if "openid" not in user_info:
            raise HTTPException(status_code=400, detail="WeChat userinfo request failed")
        logging.info(f"user_info: {user_info}")

    # 根据用户信息创建或更新用户会话
    # 这里应该有你的逻辑来处理用户信息，例如创建用户会话
    # ...
    return {"user_info": user_info}
