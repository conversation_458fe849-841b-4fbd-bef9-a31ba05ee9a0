from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from datetime import datetime, timedelta
import logging
from sqlalchemy.exc import SQLAlchemyError
import httpx
import os

from app.models.screening import Screening
from app.core.database import get_db

logger = logging.getLogger(__name__)

class EmotionService:
    def __init__(self, db_session: AsyncSession = None):
        self.db_session = get_db

    async def list_emotion_report_by_device_id(self, device_id_list, start, limit):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.device_id.in_(device_id_list),
                    Screening.type == "emotion"
                ]
                query = select(Screening).where(and_(*conditions)).offset(start).limit(limit).group_by(Screening.session_id)
                order = desc(Screening.idx)
                query = query.order_by(order)
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return []

    async def get_emotion_list_by_session_id(self, session_id):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.session_id == session_id,
                    Screening.type == "emotion"
                ]
                query = select(Screening).where(and_(*conditions))
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in get_emotion_list_by_session_id: {str(e)}")
            raise

        return []
    
    async def get_emotion_list_by_user_ids(self, user_ids, start_time = None, end_time = None, start = 0, limit = 20):
        try:
            async with self.db_session() as session:
                conditions = [
                    Screening.user_id.in_(user_ids),
                    Screening.type == "emotion"
                ]
                if start_time:
                    conditions.append(Screening.created >= start_time)
                if end_time:
                    conditions.append(Screening.created < end_time)
                query = select(Screening).where(and_(*conditions)).offset(start).limit(limit).group_by(Screening.session_id)
                result = await session.execute(query)
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Unexpected error in get_emotion_list_by_user_id: {str(e)}")
            raise

        return []