from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.services.user_service import UserService
from app.services.evaluation_item_service import EvaluationItemService
from app.services.evaluation_item_result_service import EvaluationItemResultService
from app.services.evaluation_result_service import EvaluationResultService
from app.services.training_service import TrainingService
from app.core.database import get_db
from app.core.logger import main_logger
from app.models.evaluation_item_result import EvaluationItemResult
from app.models.evaluation_result import EvaluationResult
from app.utils.common import CommonUtils
from app.core.global_var import GlobalVar

class EvaluationService:

    def __init__(self) -> None:
        self.db_session = get_db

        self.category_list = {
            "attention": {
                "name": _("注意力评估"),
                "desc": _("测试持续注意力和选择性注意力水平"),
                "key": "attention",
                "disable": False
            },
            "memory": {
                "name": _("记忆力评估"),
                "desc": _("评估短期记忆和工作记忆能力"),
                "key": "memory",
                "disable": True
            },
            "execution": {
                "name": _("执行功能评估"),
                "desc": _("测试计划、组织和执行能力"),
                "key": "execution",
                "disable": True
            },
            "language": {
                "name": _("语言能力评估"),
                "desc": _("评估语言理解和表达能力"),
                "key": "language",
                "disable": True
            },
            "logical": {
                "name": _("逻辑思维评估"),
                "desc": _("测试推理和问题解决能力"),
                "key": "logical",
                "disable": True
            },
            "space": {
                "name": _("空间认知评估"),
                "desc": _("评估空间感知和操作能力"),
                "key": "space",
                "disable": True
            },
            "learning": {
                "name": _("学习策略评估"),
                "desc": _("测试学习方法和策略运用"),
                "key": "learning",
                "disable": True
            },
            "society": {
                "name": _("社会认知评估"),
                "desc": _("评估社交理解和互动能力"),
                "key": "society",
                "disable": True
            }
        }

        self.std_score = {
            "attention": {
                "average": [75, 75, 75],
                "low": [50, 50, 50],
                "high": [95, 95, 95],
                "basic": [60, 60, 60],
            },
            "memory": {
                "average": [34, 56, 56],
                "low": [34, 56, 56],
                "high": [34, 56, 56],
                "basic": [34, 56, 56],
            }
        }


    def get_same_age_score(self, age, category):
        average_score = 0
        low_score = 0
        high_score = 0
        basic_score = 0
        score_list = self.std_score[category]
        if age >=0 and age <= 8:
            average_score = score_list["average"][0]
            low_score = score_list["low"][0]
            high_score = score_list["high"][0]
            basic_score = score_list["basic"][0]
        elif age >= 9 and age <= 12:
            average_score = score_list["average"][1]
            low_score = score_list["low"][1]
            high_score = score_list["high"][1]
            basic_score = score_list["basic"][1]
        elif age >= 13:
            average_score = score_list["average"][2]
            low_score = score_list["low"][2]
            high_score = score_list["high"][2]
            basic_score = score_list["basic"][2]

        score = {
            "average": average_score,
            "low": low_score,
            "high": high_score,
            "basic": basic_score,
        }
        return score

    # 获取评估分类列表
    async def get_category_list(self, user_id):

        user_service = UserService()
        evaluation_result_service = EvaluationResultService()

        user_info = await user_service.get_user_by_user_id(user_id)
        user_extra_json = user_info["extra_json"]

        result_id_list = []
        for key, value in self.category_list.items():
            result_id = user_extra_json.get("ev_" + key, 0)
            if result_id:
                result_id_list.append(result_id)
        
        result_info_list_dict = {}
        if len(result_id_list) > 0:
            result_info_list = await evaluation_result_service.get_result_list_by_ids(result_id_list)
            for result_info in result_info_list:
                result_info_list_dict[result_info.result_id] = result_info
        
        info_list = []
        for key, value in self.category_list.items():
            result_id = user_extra_json.get("ev_" + key, 0)
            status = 1
            if result_id:
                result_info = result_info_list_dict[result_id]
                status = result_info.status
            info = {
                "name": value["name"],
                "desc": value["desc"],
                "category": value["key"],
                "disable": value["disable"],
                "status": status,
            }
            info_list.append(info)

        return info_list

    # 重新进行分类评估
    async def restart_evaluation(self, category, user_id, account_id):
        return await self.gen_evaluation(category, user_id, account_id)

    # 生成分类评估
    async def gen_evaluation(self, category, user_id, account_id):

        evaluation_item_service = EvaluationItemService()
        evaluation_item_result_service = EvaluationItemResultService()
        evaluation_result_service = EvaluationResultService()
        user_service = UserService()

        item_info_list = await evaluation_item_service.get_items_by_category(category)
        if not item_info_list:
            return False
        
        new_evaluation_result_info = EvaluationResult(
            category=category,
            user_id=user_id,
            account_id=account_id,
            result_json={},
            extra_json={}
        )
        new_evaluation_result = await evaluation_result_service.new_evaluation_result(new_evaluation_result_info)

        update_user_data = {
            "extra_json": {
                "ev_" + category: new_evaluation_result.result_id
            }
        }
        await user_service.update_user_info_json(user_id, update_user_data)

        for item_info in item_info_list:
            new_evaluation_item_result_info = EvaluationItemResult(
                category=category,
                result_id=new_evaluation_result.result_id,
                item_id=item_info.item_id,
                user_id=user_id,
                account_id=account_id,
                collect_json={},
                result_json={},
                extra_json={}
            )
            new_evaluation_item_result = await evaluation_item_result_service.new_evaluation_item_result(new_evaluation_item_result_info)

        return new_evaluation_result.result_id

    # 获取评估分类下的评估项目
    async def get_item_list_by_category(self, category, user_id, account_id):

        # 根据用户的信息，查询是否有正在评估的测试，如果没有，找到评估项目，当前只有一个直接展示。
        # 如果有正在评估，则返回正在评估数据。显示继续。
        # {"ev_attention": "result_id", "ev_memory": "result_id"}
        user_service = UserService()
        user_info = await user_service.get_user_by_user_id(user_id)
        user_extra_json = user_info["extra_json"]
        result_id = user_extra_json.get("ev_" + category, 0)

        evaluation_item_service = EvaluationItemService()
        evaluation_item_result_service = EvaluationItemResultService()
        evaluation_result_service = EvaluationResultService()
        if not result_id:

            item_info_list = await evaluation_item_service.get_items_by_category(category)
            if not item_info_list:
                return {"code": 10201, "msg": _("该评估下没有评估项目")}
            
            result_id = await self.gen_evaluation(category, user_id, account_id)
            if not result_id:
                return {"code": 10213, "msg": _("生成评估失败")}
            
        
        evaluation_result_info = await evaluation_result_service.get_result_by_id(result_id)
        if not evaluation_result_info:
            return {"code": 10202, "msg": _("本次评估不存在")}

        item_result_info_list = await evaluation_item_result_service.get_item_result_list_by_result_id(result_id)
        if not item_result_info_list:
            return {"code": 10203, "msg": _("本次评估项目不存在")}

        return_item_info_list = []
        for item_result_info in item_result_info_list:
            item_info = await evaluation_item_service.get_item_by_id(item_result_info.item_id)
            extra_json = item_info.extra_json
            bg_img = extra_json.get("bg_img", "https://llmagentstorage.blob.core.windows.net/static/ts/res/ev_attention1_bg.jpg")
            game_url = extra_json.get("game_url", "")
            return_info = {
                "category": item_info.category,
                "name": _(item_info.name),
                "desc": _(item_info.desc),
                "key": item_info.key,
                "bg_img": bg_img,
                "game_url": game_url,
                "item_id": item_info.item_id,
                "item_result_id": item_result_info.item_result_id,
                "status": item_result_info.status
            }
            return_item_info_list.append(return_info)

        return_evaluation_info = {
            "status": evaluation_result_info.status,
            "name": self.category_list[category]["name"],
            "desc": self.category_list[category]["desc"],
            "item_info_list": return_item_info_list,
        }

        return {"code": 0, "msg": "", "data": return_evaluation_info}


    # 开始项目测试
    async def start_item(self, item_result_id, user_id):

        evaluation_item_result_service = EvaluationItemResultService()

        item_result_info = await evaluation_item_result_service.get_item_result_by_id(item_result_id)
        if not item_result_info:
            return {"code": 10204, "msg": _("此评估项目不存在")}
        
        category = item_result_info.category

        # 增加用户状态信息，能够查到正在做的评估状态和对应id
        user_service = UserService()
        user_info = await user_service.get_user_by_user_id(user_id)
        user_extra_json = user_info["extra_json"]
        result_id = user_extra_json.get("ev_" + category, 0)
        if result_id != item_result_info.result_id:
            return {"code": 10205, "msg": _("此评估项目不是正在进行的评估")}

        if item_result_info.status == 0:
            return {"code": 10207, "msg": _("此评估项目已删除")}

        if item_result_info.status == 3:
            return {"code": 10206, "msg": _("此评估项目已完成")}

        # 开始分类评估处理
        await self.start_evaluation(result_id, user_id)

        update_data = {
            "start_time": datetime.utcnow(),
            "status": 2,
        }
        await evaluation_item_result_service.update_item_result_by_id(item_result_id, update_data)
        
        return {"code": 0, "msg": "", "data": {}}

    # 结束项目测试
    async def end_item(self, item_result_id, user_id, item_collect_data):

        evaluation_item_service = EvaluationItemService()
        evaluation_item_result_service = EvaluationItemResultService()

        item_result_info = await evaluation_item_result_service.get_item_result_by_id(item_result_id)
        if not item_result_info:
            return {"code": 10204, "msg": _("此评估项目不存在")}
        
        category = item_result_info.category

        user_service = UserService()
        user_info = await user_service.get_user_by_user_id(user_id)
        user_extra_json = user_info["extra_json"]
        result_id = user_extra_json.get("ev_" + category, 0)
        if result_id != item_result_info.result_id:
            return {"code": 10205, "msg": _("此评估项目不是正在进行的评估")}

        if item_result_info.status == 0:
            return {"code": 10207, "msg": _("此评估项目已删除")}

        if item_result_info.status == 3:
            return {"code": 10206, "msg": _("此评估项目已完成")}
        
        if item_result_info.status == 1:
            return {"code": 10208, "msg": _("此评估项目还未开始")}


        item_info = await evaluation_item_service.get_item_by_id(item_result_info.item_id)
        item_key = item_info.key
        # 根据key，调用不同的处理方式对数据进行处理
        result_data = evaluation_item_service.cal_result_info(item_key, item_collect_data)

        update_data = {
            "collect_json": item_collect_data,
            "result_json": result_data,
            "end_time": datetime.utcnow(),
            "status": 3,
        }
        await evaluation_item_result_service.update_item_result_by_id(item_result_id, update_data)

        # 判断所有项目是否都已经完成，进行整体分类数据计算和评估
        result_id = item_result_info.result_id
        is_evaluation_finish = await self.end_evaluation(result_id)

        return_info = {
            "is_evaluation_finish": is_evaluation_finish,
            "result_id": result_id,
            "category": category,
        }
        return {"code": 0, "msg": "", "data": return_info}

    # 开始分类评估
    async def start_evaluation(self, result_id, user_id):

        evaluation_result_service = EvaluationResultService()
        result_info = await evaluation_result_service.get_result_by_id(result_id)
        if not result_info:
            return False

        if result_info.status != 1:
            return False

        update_data = {
            "start_time": datetime.utcnow(),
            "status": 2,
        }
        await evaluation_result_service.update_result_by_id(result_id, update_data)
        
        return True

    # 结束分类评估
    async def end_evaluation(self, result_id):
        
        evaluation_item_result_service = EvaluationItemResultService()
        evaluation_result_service = EvaluationResultService()

        item_result_data_list = []
        item_result_info_list = await evaluation_item_result_service.get_item_result_list_by_result_id(result_id)

        if not item_result_info_list:
            return False

        for item_result_info in item_result_info_list:
            if item_result_info.status != 3:
                return False
            item_result_data_list.append(item_result_info.result_json)
        
        result_info = await evaluation_result_service.get_result_by_id(result_id)
        if not result_info:
            return False
        category = result_info.category

        # 根据category，调用不同的处理方式对数据进行处理
        result_data = self.cal_result_info(category, item_result_data_list)

        update_data = {
            "result_json": result_data,
            "end_time": datetime.utcnow(),
            "status": 3,
        }
        await evaluation_result_service.update_result_by_id(result_id, update_data)

        await self.gen_report_info(result_id)

        # 生成训练计划
        train_service = TrainingService()
        await train_service.gen_training_plan(result_id)

        return True
    
    def cal_result_info(self, category, item_result_info_list):
        try:
            # 获取对应的处理器实例
            handler = CommonUtils.get_handler("app.services.evaluation", category)
            # 调用处理器的方法
            result = handler.cal_result_info(item_result_info_list)
            print(result)
        except ValueError as e:
            print(f"Wrong: {e}")

        return result
    
    def cal_score(self, category, collect_info):
        
        try:
            # 获取对应的处理器实例
            handler = CommonUtils.get_handler("app.services.evaluation" ,category)
            # 调用处理器的方法
            result = handler.cal_score(collect_info)
            print(result)
        except ValueError as e:
            print(f"Wrong: {e}")

        return result

    def handle_report_data(self, key, result_json, age):
        try:
            # 获取对应的处理器实例
            handler = CommonUtils.get_handler("app.services.evaluation" ,key)
            # 调用处理器的方法
            result = handler.handle_report_data(result_json, age)
            print(result)
        except ValueError as e:
            print(f"Wrong: {e}")

        return result
    
    def format_report_data(self, key, report_json):
        try:
            # 获取对应的处理器实例
            handler = CommonUtils.get_handler("app.services.evaluation" ,key)
            # 调用处理器的方法
            result = handler.format_report_data(report_json)
            print(result)
        except ValueError as e:
            print(f"Wrong: {e}")

        return result
    
    # 获取评估报告数据
    async def gen_report_info(self, result_id):

        evaluation_result_service = EvaluationResultService()
        result_info = await evaluation_result_service.get_result_by_id(result_id)

        if result_info.status != 3:
            return False

        category = result_info.category
        category_info = self.category_list[category]
        
        report_name = _("{}报告").format(category_info["name"])

        evaluation_item_result_service = EvaluationItemResultService()
        item_result_info_list = await evaluation_item_result_service.get_item_result_list_by_result_id(result_id)
        total_item_num = len(item_result_info_list)
        complete_item_num = total_item_num

        user_service = UserService()
        user_id = result_info.user_id
        user_info = await user_service.get_user_by_user_id(user_id)
        if not user_info:
            return False
        
        age = user_service.get_age_by_birthday(user_info["birthday"])
        score_info = self.get_same_age_score(age, category)

        last_evaluation_result = None
        last_evaluation_result_list = await evaluation_result_service.get_result_list_by_user_id(user_id, category, [3], 1, 1)
        if last_evaluation_result_list:
            last_evaluation_result = last_evaluation_result_list[0]

        last_evaluation_result_info = {}
        if last_evaluation_result:
            report_json = last_evaluation_result.report_json
            if not report_json:
                report_json = {}
            last_score = report_json.get("score", 0)
            if last_score != 0:
                last_evaluation_result_info = {
                    "result_id": last_evaluation_result.result_id,
                    "score": last_score,
                }

        start_time = result_info.start_time
        end_time = result_info.end_time
        time_diff = end_time - start_time
        total_time = time_diff.seconds

        result_json = result_info.result_json
        report_info_dict = self.handle_report_data(category, result_json, age)

        return_report_info = {
            "report_name": report_name,
            "start_time": int(start_time.timestamp() * 1000),
            "end_time": int(end_time.timestamp() * 1000),
            "total_time": total_time,
            "total_item_num": total_item_num,
            "complete_item_num": complete_item_num,
            "user_info": {"name": user_info["name"], "age": age, "gender": user_info["gender"], "grade": user_info["grade"]},
            "std_score_info": score_info,
            "last_evaluation_result_info": last_evaluation_result_info,
            "version": 1,
            "report_time": int(datetime.utcnow().timestamp() * 1000), #报告生成时间
            "report_lang": GlobalVar.get_var("language"),
        }
        for key, value in report_info_dict.items():
            return_report_info[key] = value

        update_data = {
            "report_json": return_report_info,
        }
        await evaluation_result_service.update_result_by_id(result_id, update_data)

        return True
    
    async def get_report_info(self, result_id, user_id):
        evaluation_result_service = EvaluationResultService()
        result_info = await evaluation_result_service.get_result_by_id(result_id)

        if result_info.status != 3:
            return {"code": 10209, "msg": _("此评估还未完成")}
        if not result_info.report_json:
            return {"code": 10210, "msg": _("报告还未生成，请稍后")}
        if user_id != result_info.user_id:
            return {"code": 10211, "msg": _("当前用户无权限查看此报告")}

        report_json_info = result_info.report_json
        category = result_info.category

        user_service = UserService()

        total_time = CommonUtils.format_time(report_json_info["total_time"])
        user_info = report_json_info["user_info"]
        user_age = user_info["age"]
        user_name = user_info["name"]
        user_gender = _("男") if user_info["gender"] == 1 else _("女")
        user_grade = user_service.convert_grade_to_text(user_info["grade"])
        
        report_info_dict = self.format_report_data(category, report_json_info)

        score = report_json_info["score"]
        std_score_info = report_json_info["std_score_info"]
        same_age_percent = ((score - std_score_info["average"]) / 100 * 50 + 50) / 100
        compliance_rate = score / std_score_info["basic"]
        last_evaluation_result_info = report_json_info["last_evaluation_result_info"]
        last_score_percent = 0
        if last_evaluation_result_info:
            last_score = last_evaluation_result_info["score"]
            last_score_percent = (score - last_score) / last_score

        start_time = int(report_json_info["start_time"] / 1000)
        start_time_str = CommonUtils.timestamp_to_string(start_time, "%Y-%m-%d")
        end_time = int(report_json_info["end_time"] / 1000)
        end_time_str = CommonUtils.timestamp_to_string(end_time, "%Y-%m-%d")

        return_report_info = {
            "version": report_json_info["version"],
            "report_name": report_json_info["report_name"],
            "start_time": start_time_str,
            "end_time": end_time_str,
            "total_time": total_time,
            "total_item_num": report_json_info["total_item_num"],
            "complete_item_num": report_json_info["complete_item_num"],
            "user_age": user_age,
            "user_name": user_name,
            "user_gender": user_gender,
            "user_grade": user_grade,
            "same_age_percent": round(same_age_percent, 4),
            "last_score_percent": round(last_score_percent, 4),
            "compliance_rate": round(compliance_rate, 4),
            "score": round(score),
            "average_score": std_score_info["average"],
            "low_score": std_score_info["low"],
            "high_score": std_score_info["high"],
        }
        for key, value in report_info_dict.items():
            return_report_info[key] = value

        return {"code": 0, "msg": "", "data": return_report_info}
