from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from typing import Any, Dict, Optional
from app.models.base import Base, TimestampMixin, ModelUpdateMixin


class AccountKey(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'account_keys'

    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, default=0)
    app_id = Column(String(100), default="")
    app_key = Column(String(100), default="")
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<AccountKey(account_id={self.account_id}, id={self.id})>"
