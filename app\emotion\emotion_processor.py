import asyncio
import base64
import httpx
import inspect
import logging
import os
import time
import numpy as np
from datetime import datetime, timedelta
from functools import wraps
from io import BytesIO
from PIL import Image
from typing import Optional, List, Tuple
from torchvision import transforms
from app.emotion.face_detector import FaceDetector
from app.models.screening import Screening
from app.schemas.emotion import EmotionRequest
from concurrent.futures import ThreadPoolExecutor
from app.utils.blob import save_image_to_blob, generate_blob_sas_url
from fastapi import BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.emotion.fau_handler import FauHandler, image_eval

logger = logging.getLogger(__name__)
shared_thread_pool = ThreadPoolExecutor(max_workers=10)  # 调整线程池大小


async def upload_images(image_data: List[Tuple[str, bytes]]):
    try:
        start_time = time.time()

        # 并行上传两张图片
        upload_tasks = [
            asyncio.create_task(save_image_to_blob(image, blob_name))
            for blob_name, image in image_data
        ]
        blob_urls = await asyncio.gather(*upload_tasks)
        total_cost = (time.time() - start_time) * 1000

        blob_names = ', '.join([blob_name for blob_name, _ in image_data])
        logger.info(f"{len(image_data)} images uploaded successfully. "
                    f"Blob names: [{blob_names}], Cost: {total_cost:.2f}ms")
        return blob_urls
    except Exception as e:
        logger.error(f"Error uploading images, error={str(e)}")
        raise


def parse_result(pred, exp_probs, valence_probs, arousal_probs):
    logger.debug(f"Inference output: \n{pred}")

    AU_index = [
        '1', '2', '4', '5', '6', '7', '9', '10', '11', '12',
        '13', '14', '15', '16', '17', '18', '19', '20', '22', '23',
        '24', '25', '26', '27', '32', '38', '39', 'L1', 'R1', 'L2',
        'R2', 'L4', 'R4', 'L6', 'R6', 'L10', 'R10', 'L12', 'R12', 'L14', 'R14']

    AU_names = [
        'Inner brow raiser', 'Outer brow raiser', 'Brow lowerer', 'Upper lid raiser', 'Cheek raiser',
        'Lid tightener', 'Nose wrinkler', 'Upper lip raiser', 'Nasolabial deepener', 'Lip corner puller',
        'Sharp lip puller', 'Dimpler', 'Lip corner depressor', 'Lower lip depressor', 'Chin raiser',
        'Lip pucker', 'Tongue show', 'Lip stretcher', 'Lip funneler', 'Lip tightener', 'Lip pressor',
        'Lips part', 'Jaw drop', 'Mouth stretch', 'Lip bite', 'Nostril dilator', 'Nostril compressor',
        'Left Inner brow raiser', 'Right Inner brow raiser', 'Left Outer brow raiser',
        'Right Outer brow raiser', 'Left Brow lowerer', 'Right Brow lowerer', 'Left Cheek raiser',
        'Right Cheek raiser', 'Left Upper lip raiser', 'Right Upper lip raiser', 'Left Nasolabial deepener',
        'Right Nasolabial deepener', 'Left Dimpler', 'Right Dimpler']

    # expression, confidence = expression_cal(pred[0])
    exp_labels = ['neutral', 'happiness', 'sadness', 'surprise', 'fear', 'disgust', 'anger', 'neglect'] # contempt -> neglect
    expression_list = []
    expression = ""
    confidence = 0.0
    for i in range(len(exp_labels)):
        expression_list.append({"label": exp_labels[i], "probs": exp_probs[i]})
        if confidence < exp_probs[i]:
            confidence = exp_probs[i]
            expression = exp_labels[i]

    logger.debug(f"expression: {expression}, confidence: {confidence}")

    valence = "positive" if valence_probs > 0 else "negative"
    arousal = "high" if arousal_probs > 0 else "low"
    valence_value = round(valence_probs, 2)
    arousal_value = round(arousal_probs, 2)
    valence_arousal = {"valence": valence, "valence_value": valence_value, "arousal": arousal, "arousal_value": arousal_value}

    au_infos = {}
    for i, item in enumerate(range(41)):
        name = 'AU' + AU_index[item] + ' -- ' + AU_names[i]
        value = pred[0][i]
        au_infos[name] = value
        logger.debug(f"{name}: {value:.2f}")

    return {
        "expression": expression,
        "confidence":confidence,
        "au_infos": au_infos,
        "expression_list": expression_list,
        "valence_arousal": valence_arousal
    }


def timing_decorator(key):
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            start_time = time.perf_counter()

            if inspect.iscoroutinefunction(func):
                async def async_wrapper():
                    try:
                        result = await func(self, *args, **kwargs)
                    finally:
                        end_time = time.perf_counter()
                        elapsed_time = end_time - start_time
                        self.timing_list.append({"task": key, "cost": elapsed_time})
                    return result

                return async_wrapper()
            else:
                try:
                    result = func(self, *args, **kwargs)
                finally:
                    end_time = time.perf_counter()
                    elapsed_time = end_time - start_time
                    self.timing_list.append({"task": key, "cost": elapsed_time})
                return result

        return wrapper

    return decorator

class EmotionProcessor:
    def __init__(self):
        self.request: Optional[EmotionRequest] = None
        self.image_orig = None
        self.image_mesh = None
        self.image_crop = None
        self.face_crop_244 = None
        self.time_prefix = None
        self.orig_blob_name = None
        self.mesh_blob_name = None

        self.client = None
        self.thread_pool = shared_thread_pool
        self.timing_list = []
        self.fau_handler = FauHandler()

        self.device_id = ""

        # self.dataset_info = hybrid_prediction_infolist

    @timing_decorator("decode_image")
    def decode_image(self):
        image_data = base64.b64decode(self.request.image)
        self.image_orig = Image.open(BytesIO(image_data)).convert('RGB')

    @timing_decorator("detect_face")
    def detect_face(self):
        face_detector = FaceDetector()
        video_frame_np = np.array(self.image_orig, dtype=np.uint8)
        face_img_np, face_crop_np = face_detector(video_frame_np)
        self.image_mesh = Image.fromarray(np.uint8(face_img_np))
        self.image_crop = Image.fromarray(np.uint8(face_crop_np))

    @timing_decorator("crop_face")
    def crop_face(self):
        pass

    @timing_decorator("call_triton_api")
    async def call_triton_api(self, path, infer_request):
        if self.client is None:
            self.client = httpx.AsyncClient(timeout=30.0)

        triton_base_url = os.getenv("TRITON_BASE_URL")
        triton_url = f"{triton_base_url}{path}"
        infer_request = infer_request

        try:
            response = await self.client.post(
                triton_url,
                headers={"Content-Type": "application/json"},
                json=infer_request
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Error: {e.response.status_code} - {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"Error calling Triton API: {str(e)}")
            return None

    @timing_decorator("upload_blobs")
    def upload_blobs(self, background_tasks):
        self.orig_blob_name = f"{self.request.user_id}_{self.request.session_id}_{self.time_prefix}__orig.png"
        self.mesh_blob_name = f"{self.request.user_id}_{self.request.session_id}_{self.time_prefix}_mesh.png"

        if background_tasks:
            # upload images in background thread
            images = [(self.orig_blob_name, self.image_orig), (self.mesh_blob_name, self.image_mesh)]
            background_tasks.add_task(upload_images, images)

    async def save_result(self, db, parsed_result):
        if db is None:
            return

        try:
            async with db as session:
                screening = Screening(
                    type='emotion',
                    user_id=self.request.user_id,
                    device_id= self.device_id,
                    session_id=self.request.session_id,
                    origin_data=self.orig_blob_name,
                    process_result=parsed_result,
                    created=datetime.utcnow(),
                    updated=datetime.utcnow()
                )
                session.add(screening)
                await session.commit()
        except ValueError as ve:
            logger.error(f"Value error in save_result: {str(ve)}")
            raise
        except SQLAlchemyError as e:
            logger.error(f"Database error in save_result: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return screening

    def dump_images(self):
        # 保存原始解码后的图像到 origin.png
        self.image_orig.save(f"./data/{self.time_prefix}_origin.png", format="PNG")
        self.image_mesh.save(f"./data/{self.time_prefix}_facemesh.png", format="PNG")
        self.image_corp.save(f"./data/{self.time_prefix}_face_crop.png", format="PNG")
        # 只将图像transform到224
        transform2 = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224)])
        face_crop_244_img = transform2(self.image_crop)
        # 将face_crop_244保存为JPEG格式
        face_crop_244_img.save(f"./data/{self.time_prefix}_face_crop_244.png", format="PNG")

    def image_processing(self):
        self.decode_image()
        self.detect_face()
        self.crop_face()

    async def process(self, request: EmotionRequest, background_tasks: BackgroundTasks, db: AsyncSession):
        start_time = time.time()
        self.request = request
        self.time_prefix = datetime.now().strftime("%Y%m%d_%H%M%S")

        await asyncio.get_event_loop().run_in_executor(
            self.thread_pool, self.image_processing
        )

        img_transform = image_eval()
        self.face_crop_244 = img_transform(self.image_crop).unsqueeze(0).numpy().astype(np.float32)

        emotion_fau_infer_request = {
            "inputs": [
                {
                    "name": "input",
                    "shape": list(self.face_crop_244.shape),
                    "datatype": "FP32",
                    "data": self.face_crop_244.flatten().tolist()
                }
            ],
            "outputs": [
                {
                    "name": "output"
                }
            ]
        }
        result = await self.call_triton_api('/emotion/infer', emotion_fau_infer_request)
        pred = np.array(result['outputs'][0]['data']).reshape(result['outputs'][0]['shape'])
        # infostr_probs, infostr_aus = self.dataset_info(pred[0], 0.5)
        # exp = expression_cal(pred[0][0])

        self.crop_preprocess_data = self.fau_handler.preprocess_image(self.image_crop)
        print(list(self.crop_preprocess_data.shape))

        emotion_multi_infer_request = {
            "inputs": [
                {
                    "name": "input",
                    "shape": list(self.crop_preprocess_data.shape),
                    "datatype": "FP32",
                    "data": self.crop_preprocess_data.flatten().tolist()
                }
            ],
            "outputs": [
                {
                    "name": "expression"
                },
                {
                    "name": "valence"
                },
                {
                    "name": "arousal"
                }
            ]
        }
        
        multiple_result = await self.call_triton_api('/emotion_multi/infer', emotion_multi_infer_request)
        print(multiple_result)

        # multiple_result_au_output = np.array(multiple_result['outputs'][0]['data']).reshape(multiple_result['outputs'][0]['shape'])
        multiple_result_exp_output = np.array(multiple_result['outputs'][1]['data']).reshape(multiple_result['outputs'][1]['shape'])
        multiple_result_valence_probs_output = np.array(multiple_result['outputs'][2]['data']).reshape(multiple_result['outputs'][2]['shape'])
        multiple_result_arousal_probs_output = np.array(multiple_result['outputs'][0]['data']).reshape(multiple_result['outputs'][0]['shape'])
        exp_probs = self.fau_handler.softmax(multiple_result_exp_output[0])
        if exp_probs.ndim > 1:
            exp_probs = exp_probs[0]
        # va_probs = multiple_result_va_output[0]
        valence_probs = np.clip(multiple_result_valence_probs_output[0]*2, -1.0, 1.0)
        arousal_probs = np.clip(multiple_result_arousal_probs_output[0]-0.4, -1.0, 1.0)

        parsed_result = parse_result(pred, exp_probs, valence_probs, arousal_probs)
        # img = draw_text(list(infostr_aus), pred[0])

        self.upload_blobs(background_tasks)

        parsed_result['orig_blob_name'] = self.orig_blob_name
        parsed_result['mesh_blob_name'] = self.mesh_blob_name
        screening = await self.save_result(db, parsed_result)
        parsed_result['screening_idx'] = screening.idx
        screening_time = screening.created + timedelta(hours=8)
        parsed_result['screening_time'] = screening_time.strftime('%Y-%m-%d')

        start_time1 = time.time()
        parsed_result['orig_blob_url'] = generate_blob_sas_url(self.orig_blob_name)
        parsed_result['mesh_blob_url'] = generate_blob_sas_url(self.mesh_blob_name)
        total_cost1 = time.time() - start_time1

        for item in self.timing_list:
            logger.info(f"{item['cost'] * 1000:7.2f}ms - {item['task']}")
        logger.info(f"{total_cost1 * 1000:7.2f}ms - generate_blob_sas_url")

        total_cost = time.time() - start_time
        logger.info(f"{total_cost * 1000:7.2f}ms - TOTAL")
        return parsed_result
