
# 注意力评估综合结果处理
class HandlerAttention:
    def __init__(self) -> None:

        self.attention_param = {
            "reaction_reference": [500, 500, 500],  #参考基准反应时间
        }

    def get_attention_param(self, age, param):
        value = 500
        if age >=0 and age <= 8:
            value = self.attention_param[param][0]
        elif age >= 9 and age <= 12:
            value = self.attention_param[param][1]
        elif age >= 13:
            value = self.attention_param[param][2]
        return value

    # 通过各项目评测的数据，计算需要的报告数据
    def cal_result_info(self, item_result_info_list):
        return item_result_info_list
    
    # 计算报告分数
    def cal_score(self, param_info):
        reaction_speed_percent = param_info["reaction_speed_percent"]
        choose_attention = param_info["choose_attention"]
        allocate_attention = param_info["allocate_attention"]
        sustained_attention = param_info["sustained_attention"]
        total_score = reaction_speed_percent * 0.25 + choose_attention * 0.25 + allocate_attention * 0.25 + sustained_attention * 0.25
        return total_score
    
    def handle_report_data(self, result_json, age):

        result_json_info = result_json[0]

        reaction_speed = result_json_info["reaction_speed"]
        reaction_reference = self.get_attention_param(age, "reaction_reference")
        reaction_speed_percent = (reaction_reference - reaction_speed) / reaction_reference * 100

        score = 0
        param_info = {
            "reaction_speed_percent": reaction_speed_percent,
            "choose_attention": result_json_info["choose_attention"],
            "allocate_attention": result_json_info["allocate_attention"],
            "sustained_attention": result_json_info["sustained_attention"],
        }
        score = self.cal_score(param_info)

        return_info = {
            "reaction_speed_percent": reaction_speed_percent,
            "choose_attention": result_json_info["choose_attention"],
            "allocate_attention": result_json_info["allocate_attention"],
            "sustained_attention": result_json_info["sustained_attention"],
            "score": score,
        }

        return return_info
    
    def format_report_data(self, report_json_info):
        info = {
            "reaction_speed_percent": round(report_json_info["reaction_speed_percent"] / 100, 4),
            "choose_attention": round(report_json_info["choose_attention"] / 100, 4),
            "allocate_attention": round(report_json_info["allocate_attention"] / 100, 4),
            "sustained_attention": round(report_json_info["sustained_attention"] / 100, 4),
        }
        return info