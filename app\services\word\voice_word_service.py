from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.services.appconfig_service import AppConfigService


class VoiceWordService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def get_words(self, device_id):
        app_config_service = AppConfigService()
        config_info = await app_config_service.get_config_by_device_id(device_id)
        if not config_info:
            return {}
        if config_info.get("is_baichuan"):
            return self.baichuan_words()
        
        return {}

    def baichuan_words(self):
        return {
        }