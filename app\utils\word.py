import os
from docx import Document
import dotenv

dotenv.load_dotenv()

class Word:

    word_dir = os.getenv('WORD_DIR')

    def text_to_word(self, text, target_file):
        document = Document()
        document.add_paragraph(text)
        print(self.word_dir)
        print(target_file)
        target_file = self.word_dir + target_file
        directory = os.path.dirname(target_file)
        if not os.path.exists(directory):
            os.makedirs(directory)
        document.save(target_file)
        return target_file
