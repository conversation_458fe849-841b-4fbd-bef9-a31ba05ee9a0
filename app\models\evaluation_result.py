from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from datetime import datetime

from app.models.base import Base, TimestampMixin, ModelUpdateMixin

class EvaluationResult(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'evaluation_results'

    result_id = Column(Integer, primary_key=True)
    category = Column(String(100), default="")
    user_id = Column(Integer, default=0)
    account_id = Column(Integer, default=0)
    result_json = Column(JSON, default=dict)
    report_json = Column(JSON, default=dict)
    start_time = Column(DateTime, default=None)
    end_time = Column(DateTime, default=None)
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<EvaluationResult(result_id={self.result_id}, category={self.category}, user_id={self.user_id})>"
