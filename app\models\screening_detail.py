from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from datetime import datetime

from app.models.base import Base, TimestampMixin

class ScreeningDetail(Base):
    __tablename__ = 'screening_detail'

    id = Column(String(255), primary_key=True)
    screening_id = Column(String(255), default='')
    category = Column(String(255), default='')
    name = Column(String(255), default='')
    version = Column(String(255), default='')
    detail = Column(Text, default='')
    device_id = Column(String(255), default='')
    status = Column(Integer, default=1)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<ScreeningDetail(id={self.id}, device_id={self.device_id}, status={self.status})>"
