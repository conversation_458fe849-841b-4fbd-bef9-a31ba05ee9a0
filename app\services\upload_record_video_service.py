from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta
import logging
from sqlalchemy.exc import SQLAlchemyError
import httpx
import os
import jwt
import asyncio
import ffmpeg

from app.models.upload_record_video import UploadRecordVideo
from app.services.faceplusplus_servcie import FacePlusPlusService
from app.utils.common import CommonUtils
from app.core.database import get_db
from app.core.logger import main_logger
from app.core.celery_app import celery_app
from app.utils.blob import save_file_to_blob, generate_blob_sas_url


class UploadRecordVideoService:
    def __init__(self, request = None):
        self.db_session = get_db
        self.request = request
        self.tmp_dir = "tmp/upload_records/video"

    async def insert(self, user_id):

        new_record = UploadRecordVideo(
            user_id=user_id,
            status=1,
            extra_json={}
        )
        try:
            async with self.db_session() as session:
                session.add(new_record)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return new_record.__dict__

    async def get_info_by_type_id(self, type_id, type) -> dict:
        try:
            async with self.db_session() as session:
                conditions = [
                    UploadRecordVideo.type_id == type_id,
                    UploadRecordVideo.type == type,
                ]
                stmt = select(UploadRecordVideo).where(and_(*conditions))
                result = await session.execute(stmt)
                record_info = result.scalars().first()
                if record_info:
                    record_info = record_info.__dict__
        except Exception as e:
            main_logger.error(f"Unexpected error in get_info_by_type_id: {str(e)}")
            raise

        return record_info

    async def get_by_record_id(self, record_id) -> dict:
        try:
            async with self.db_session() as session:
                stmt = select(UploadRecordVideo).where(UploadRecordVideo.record_id == record_id)
                result = await session.execute(stmt)
                record_info = result.scalars().first()
                if record_info:
                    record_info = record_info.__dict__
        except Exception as e:
            main_logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return record_info
    
    async def update_record_by_id(self, record_id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(UploadRecordVideo).where(UploadRecordVideo.record_id == record_id)
                result = await session.execute(stmt)
                result_info = result.scalars().first()

                if not result_info:
                    return False

                await result_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_record_by_id: {str(e)}")
            raise

        return True

    async def merge_video_chunks(self, record_id: int):
        chunks_dir = f"{self.tmp_dir}/{record_id}"
        output_path_webm = f"{record_id}.webm"

        chunk_files = sorted([f for f in os.listdir(chunks_dir) if f.startswith('chunk_')])
        
        try:
            # # 使用 FFmpeg 合并视频分片
            # input_pattern = f"{chunks_dir}/chunk_%06d.webm"
            
            # stream = ffmpeg.input(input_pattern)
            # stream = ffmpeg.output(stream, output_path, c='copy')
            
            # print(input_pattern)

            # await asyncio.create_subprocess_exec(
            #     'ffmpeg',
            #     '-f', 'concat',
            #     '-safe', '0',
            #     '-i', input_pattern,
            #     '-c', 'copy',
            #     output_path
            # )

            output_file_webm = os.path.join(chunks_dir, output_path_webm)
            with open(output_file_webm, 'wb') as outfile:
                for chunk_file in chunk_files:
                    chunk_path = os.path.join(chunks_dir, chunk_file)
                    with open(chunk_path, 'rb') as infile:
                        outfile.write(infile.read())
                    os.remove(chunk_path)  # 删除已合并的分片

            os.chmod(output_file_webm, 0o777)
            return output_file_webm
            
        except Exception as e:
            print(f"Error merging video chunks: {e}")
            raise

    async def cleanup_chunks(self, record_id: int):
        chunks_dir = f"{self.tmp_dir}/{record_id}"
        try:
            for chunk_file in os.listdir(chunks_dir):
                os.remove(os.path.join(chunks_dir, chunk_file))
            os.rmdir(chunks_dir)
        except Exception as e:
            print(f"Error cleaning up chunks: {e}")

    async def video_upload_blob(self, record_id, output_file, target_file, content_type):

        #todo 当前是在一台服务器器上，后期在不同服务器上需要优化
        print("video_upload_blob task record_id: {}".format(record_id))
        remote_file_url = await save_file_to_blob(output_file, target_file, None, content_type)

        if not remote_file_url:
            print("video_upload_blob task failed record_id: {}".format(record_id))
            return
        
        update_data = {
            "extra_json": {"blob_url": target_file},
        }

        await self.update_record_by_id(record_id, update_data)

        os.unlink(output_file)
        # 清理临时文件
        await self.cleanup_chunks(record_id)
        print("video_upload_blob end record_id: {}".format(record_id))