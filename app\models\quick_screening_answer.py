from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from app.models.base import Base, TimestampMixin, ModelUpdateMixin


class QuickScreeningAnswer(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'quick_screening_answers'

    answer_id = Column(Integer, primary_key=True)
    question_id = Column(Integer, default=0)
    user_id = Column(Integer, default=0)
    qs_result_id = Column(Integer, default=0)
    type = Column(String, default="")
    answer_json = Column(JSON, default=dict)
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<QuickScreeningAnswer(qs_result_id={self.qs_result_id}, user_id={self.user_id})>"
