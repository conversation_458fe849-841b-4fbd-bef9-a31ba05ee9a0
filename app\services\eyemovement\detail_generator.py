
import json
from functools import reduce

from app.services.eyemovement.common import is_valid_pos, calc_distance

BIAS = 20


def generate_detail(details):
    parts = {
        'part1': {
            'target1': {
                'min': 0.0,
                'max': 1147.5,
                'val': -1.0
            },
            'target2': {
                'min': 0.0,
                'max': 877.5,
                'val': -1.0
            },
            'target3': {
                'min': 0.0,
                'max': 1443.25,
                'val': -1.0
            },
            'target4': {
                'min': 0.0,
                'max': 0.12,
                'val': -1.0
            },
            'target5': {
                'min': 25.0,
                'max': 41.0,
                'val': -1.0
            },
            'target6': {
                'min': 66.81,
                'max': 107.82,
                'val': -1.0
            },
            'target7': {
                'min': 58.62,
                'max': 86.77,
                'val': -1.0
            },
        },
        'part2': {
            'target1': {
                'min': 0.0,
                'max': 24.12,
                'val': -1.0
            },
            'target2': {
                'min': 0.0,
                'max': 2500,
                'val': -1.0
            },
            'target3': {
                'min': 0.0,
                'max': 60.5,
                'val': -1.0
            },
            'target4': {
                'min': 0.0,
                'max': 24.12,
                'val': -1.0
            },
            'target5': {
                'min': 0.0,
                'max': 2130,
                'val': -1.0
            },
            'target6': {
                'min': 0.0,
                'max': 77.6,
                'val': -1.0
            },
        },
        'part3': {
            'target1': {
                'min': 0.0,
                'max': 449.2,
                'val': -1.0
            },
            'target2': {
                'min': 0.0,
                'max': 0.58,
                'val': -1.0
            },
            'target3': {
                'min': 0.0,
                'max': 471.45,
                'val': -1.0
            },
            'target4': {
                'min': 0.0,
                'max': 0.51,
                'val': -1.0
            },
        },
        'part4': {
            'target1': {
                'min': 2.0,
                'max': 13.0,
                'val': -1.0
            },
            'target2': {
                'min': 2.0,
                'max': 12.0,
                'val': -1.0
            },
            'target3': {
                'min': 8.0,
                'max': 550.0,
                'val': -1.0
            },
            'target4': {
                'min': 283.7,
                'max': 1922.44,
                'val': -1.0
            },
            'target5': {
                'min': 3450.0,
                'max': 4940.0,
                'val': -1.0
            },
            'target6': {
                'min': 85.94,
                'max': 217.26,
                'val': -1.0
            },
            'target7': {
                'min': 0.21,
                'max': 0.5,
                'val': -1.0
            },
            'target8': {
                'min': 0.25,
                'max': 0.51,
                'val': -1.0
            },
            'target9': {
                'min': 4.22,
                'max': 14.44,
                'val': -1.0
            },
            'target10': {
                'min': 0.55,
                'max': 1.4,
                'val': -1.0
            },
        },
        'part5': {
            'target1': {
                'min': 28.0,
                'max': 10000.0,
                'val': -1.0
            },
            'target2': {
                'min': 0.0,
                'max': 0.0,
                'val': -1.0
            },
            'target3': {
                'min': 0.0,
                'max': 0.0,
                'val': -1.0
            },
            'target4': {
                'min': 7.0,
                'max': 14.0,
                'val': -1.0
            },
            'target5': {
                'min': 0.0,
                'max': 0.0,
                'val': -1.0
            },
        },
    }
    for detail in details:
        if detail.category == 'EM':
            if detail.name == 'test1':
                targets1 = generate_em_test1_detail(json.loads(detail.detail))
                parts['part1']['target1']['val'] = targets1['target1']
            elif detail.name == 'test2':
                targets2 = generate_em_test2_detail(json.loads(detail.detail))
                parts['part1']['target2']['val'] = targets2['target2']
            elif detail.name == 'test3':
                targets3 = generate_em_test3_detail(json.loads(detail.detail))
                # parts['part1']['target3']['val'] = targets3['target3']
                # parts['part1']['target4']['val'] = targets3['target4']
                # parts['part1']['target5']['val'] = targets3['target5']
                # parts['part1']['target6']['val'] = targets3['target6']
                # parts['part1']['target7']['val'] = targets3['target7']
            elif detail.name == 'test4':
                targets4 = generate_em_test4_detail(json.loads(detail.detail))
                parts['part1']['target3']['val'] = targets4['target3']
                parts['part1']['target4']['val'] = targets4['target4']
                parts['part1']['target5']['val'] = targets4['target5']
                parts['part1']['target6']['val'] = targets4['target6']
                parts['part1']['target7']['val'] = targets4['target7']
            elif detail.name == 'test5':
                targets5 = generate_em_test5_detail(json.loads(detail.detail))
                parts['part2']['target1']['val'] = targets5['target1']
                parts['part2']['target2']['val'] = targets5['target2']
                parts['part2']['target3']['val'] = targets5['target3']
                parts['part2']['target4']['val'] = targets5['target4']
                parts['part2']['target5']['val'] = targets5['target5']
                parts['part2']['target6']['val'] = targets5['target6']
            elif detail.name == 'test6':
                targets6 = generate_em_test6_detail(json.loads(detail.detail))
                parts['part3']['target1']['val'] = targets6['target1']
                parts['part3']['target2']['val'] = targets6['target2']
                parts['part3']['target3']['val'] = targets6['target3']
                parts['part3']['target4']['val'] = targets6['target4']
            elif detail.name == 'test7':
                targets7 = generate_em_test7_detail(json.loads(detail.detail))
                parts['part4']['target1']['val'] = targets7['target1']
                parts['part4']['target2']['val'] = targets7['target2']
                parts['part4']['target3']['val'] = targets7['target3']
                parts['part4']['target4']['val'] = targets7['target4']
                parts['part4']['target5']['val'] = targets7['target5']
                parts['part4']['target6']['val'] = targets7['target6']
                parts['part4']['target7']['val'] = targets7['target7']
                parts['part4']['target8']['val'] = targets7['target8']
                parts['part4']['target9']['val'] = targets7['target9']
                parts['part4']['target10']['val'] = targets7['target10']
            elif detail.name == 'test8':
                targets8 = generate_em_test8_detail(json.loads(detail.detail))
                parts['part5']['target1']['val'] = targets8['target1']
                parts['part5']['target2']['val'] = targets8['target2']
                parts['part5']['target3']['val'] = targets8['target3']
                parts['part5']['target4']['val'] = targets8['target4']
                parts['part5']['target5']['val'] = targets8['target5']
    # parts['part1']['target3']['val'] = (targets3['target3'] + targets4['target3']) / 2
    # parts['part1']['target4']['val'] = (targets3['target4'] + targets4['target4']) / 2
    # parts['part1']['target5']['val'] = (targets3['target5'] + targets4['target5']) / 2
    # parts['part1']['target6']['val'] = (targets3['target6'] + targets4['target6']) / 2
    # parts['part1']['target7']['val'] = (targets3['target7'] + targets4['target7']) / 2
    return parts


def generate_em_test1_detail(meta):
    fps = meta['config']['fps']

    tick = 0
    for stage in meta['stages']:
        for item in stage['data']:
            if not is_valid_pos(item['ax'], item['ay']):
                continue
            if calc_distance(item['ax'], item['ay'], item['tx'], item['ty']) > 100 + BIAS:
                tick += 1

    return {
        'target1': tick * (1000 / fps)
    }


def generate_em_test2_detail(meta):
    fps = meta['config']['fps']

    tick = 0
    for stage in meta['stages']:
        for item in stage['data']:
            if not is_valid_pos(item['ax'], item['ay']):
                continue
            if calc_distance(item['ax'], item['ay'], item['tx'], item['ty']) > 100 + BIAS:
                tick += 1

    return {
        'target2': tick * (1000 / fps)
    }


def generate_em_test3_detail(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']

    tick = 0
    for stage in meta['stages']:
        for item in stage['data']:
            if not is_valid_pos(item['ax'], item['ay']):
                continue
            if calc_distance(item['ax'], item['ay'], item['tx'], item['ty']) > 100 + BIAS:
                tick += 1

    rate = 0
    for stage in meta['stages']:
        j = int(fps / 10) - 1
        while j < len(stage['data']) and not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
            j += 1
        rate += calc_distance(
            stage['data'][j]['ax'],
            stage['data'][j]['ay'],
            stage['data'][0]['ax'],
            stage['data'][0]['ay']
        ) / 100

    gaze = 0
    for stage in meta['stages']:
        is_gaze, gaze_pos = False, 0
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']):
                gaze_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            dist = calc_distance(
                stage['data'][j]['ax'],
                stage['data'][j]['ay'],
                stage['data'][gaze_pos]['ax'],
                stage['data'][gaze_pos]['ay']
            )
            if dist < size + BIAS:
                if not is_gaze and j - gaze_pos > fps / 5:
                    is_gaze = True
            else:
                if is_gaze:
                    gaze += 1
                is_gaze = False
                gaze_pos = j
        if is_gaze:
            gaze += 1

    delta, count = 0, 0
    for stage in meta['stages']:
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][j - 1]['ax'], stage['data'][j - 1]['ay']):
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            act_dist = calc_distance(
                stage['data'][j]['ax'],
                stage['data'][j]['ay'],
                stage['data'][j - 1]['ax'],
                stage['data'][j - 1]['ay']
            )
            tar_dist = calc_distance(
                stage['data'][j]['tx'],
                stage['data'][j]['ty'],
                stage['data'][j - 1]['tx'],
                stage['data'][j - 1]['ty']
            )
            delta += abs(act_dist - tar_dist)
            count += 1

    return {
        'target3': tick * (1000 / fps),
        'target4': rate,
        'target5': gaze,
        'target6': delta / count * (1000 / fps) if count > 0 else 0,
        'target7': delta / (count / fps) if count > 0 else 0,
    }


def generate_em_test4_detail(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']

    tick = 0
    for stage in meta['stages']:
        for item in stage['data']:
            if not is_valid_pos(item['ax'], item['ay']):
                continue
            if calc_distance(item['ax'], item['ay'], item['tx'], item['ty']) > 100 + BIAS:
                tick += 1

    rate = 0
    for stage in meta['stages']:
        j = int(fps / 10) - 1
        while j < len(stage['data']) and not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
            j += 1
        rate += calc_distance(
            stage['data'][j]['ax'],
            stage['data'][j]['ay'],
            stage['data'][0]['ax'],
            stage['data'][0]['ay']
        ) / 100

    gaze = 0
    for stage in meta['stages']:
        is_gaze, gaze_pos = False, 0
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']):
                gaze_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            dist = calc_distance(
                stage['data'][j]['ax'],
                stage['data'][j]['ay'],
                stage['data'][gaze_pos]['ax'],
                stage['data'][gaze_pos]['ay']
            )
            if dist < size + BIAS:
                if not is_gaze and j - gaze_pos > fps / 5:
                    is_gaze = True
            else:
                if is_gaze:
                    gaze += 1
                is_gaze = False
                gaze_pos = j
        if is_gaze:
            gaze += 1

    delta, count = 0, 0
    for stage in meta['stages']:
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][j - 1]['ax'], stage['data'][j - 1]['ay']):
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            act_dist = calc_distance(
                stage['data'][j]['ax'],
                stage['data'][j]['ay'],
                stage['data'][j - 1]['ax'],
                stage['data'][j - 1]['ay']
            )
            tar_dist = calc_distance(
                stage['data'][j]['tx'],
                stage['data'][j]['ty'],
                stage['data'][j - 1]['tx'],
                stage['data'][j - 1]['ty']
            )
            delta += abs(act_dist - tar_dist)
            count += 1

    return {
        'target3': tick * (1000 / fps),
        'target4': rate,
        'target5': gaze,
        'target6': delta / count * (1000 / fps) if count > 0 else 0,
        'target7': delta / (count / fps) if count > 0 else 0,
    }


def generate_em_test5_detail(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']

    gaze_count, gaze_times, scan_count, scan_dists = [0, 0], [0, 0], [0, 0], [0, 0]
    for stage in meta['stages']:
        is_gaze, gaze_pos = False, 0
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']):
                gaze_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            dist = calc_distance(
                stage['data'][j]['ax'],
                stage['data'][j]['ay'],
                stage['data'][gaze_pos]['ax'],
                stage['data'][gaze_pos]['ay']
            )
            if dist < size + BIAS:
                if not is_gaze and j - gaze_pos > fps / 5:
                    is_gaze = True
            else:
                flag = 0 if stage['data'][gaze_pos]['ex']['rx'] == 0 and stage['data'][gaze_pos]['ex']['ry'] == 0 else 1
                if is_gaze:
                    gaze_count[flag] += 1
                    gaze_times[flag] += j - gaze_pos - 1
                else:
                    scan_count[flag] += 1
                    scan_dists[flag] += dist
                is_gaze = False
                gaze_pos = j
        if is_gaze:
            flag = 0 if stage['data'][gaze_pos]['ex']['rx'] == 0 and stage['data'][gaze_pos]['ex']['ry'] == 0 else 1
            j = len(stage['data'])
            gaze_count[flag] += 1
            gaze_times[flag] += j - gaze_pos - 1

    return {
        'target1': gaze_count[0],
        'target2': gaze_times[0] / gaze_count[0] * (1000 / fps) if gaze_count[0] > 0 else 0,
        'target3': scan_dists[0] / scan_count[0] - BIAS if scan_count[0] > 0 else 0,
        'target4': gaze_count[1],
        'target5': gaze_times[1] / gaze_count[1] * (1000 / fps) if gaze_count[1] > 0 else 0,
        'target6': scan_dists[1] / scan_count[1] - BIAS if scan_count[1] > 0 else 0,
    }


def generate_em_test6_detail(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']
    width = meta['config']['width']

    started, start_pos, count, res_times, res_speed = False, 0, 0, [0, 0], [0, 0]
    for stage in meta['stages']:
        for j in range(1, len(stage['data'])):
            if stage['data'][j]['tx'] == width / 2:
                started = False
                start_pos = 0
                continue
            if start_pos == 0:
                start_pos = j
            if started:
                continue
            if not is_valid_pos(stage['data'][start_pos]['ax'], stage['data'][start_pos]['ay']):
                start_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            dist = calc_distance(
                stage['data'][j]['ax'],
                stage['data'][j]['ay'],
                stage['data'][start_pos]['ax'],
                stage['data'][start_pos]['ay']
            )
            if dist > size + BIAS:
                started = True
                count += 1
                res_times[stage['data'][start_pos]['ex']['type']] += j - start_pos
                res_speed[stage['data'][start_pos]['ex']['type']] += dist

    return {
        'target1': res_times[1] / count * (1000 / fps) if count > 0 else 0,
        'target2': res_speed[1] / count / (1000 / fps) if count > 0 else 0,
        'target3': res_times[0] / count * (1000 / fps) if count > 0 else 0,
        'target4': res_speed[0] / count / (1000 / fps) if count > 0 else 0,
    }


def generate_em_test7_detail(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']

    gaze_count, gaze_times, scan_count, scan_dists = 0, 0, 0, 0
    for stage in meta['stages']:
        is_gaze, gaze_pos = False, 0
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']):
                gaze_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            dist = calc_distance(
                stage['data'][j]['ax'],
                stage['data'][j]['ay'],
                stage['data'][gaze_pos]['ax'],
                stage['data'][gaze_pos]['ay']
            )
            if dist < size + BIAS:
                if not is_gaze and j - gaze_pos > fps / 5:
                    is_gaze = True
            else:
                if is_gaze:
                    gaze_count += 1
                    gaze_times += j - gaze_pos - 1
                else:
                    scan_count += 1
                    scan_dists += dist
                is_gaze = False
                gaze_pos = j
        if is_gaze:
            j = len(stage['data'])
            gaze_count += 1
            gaze_times += j - gaze_pos - 1

    return {
        'target1': gaze_count / len(meta['stages']),
        'target2': scan_count / len(meta['stages']),
        'target3': gaze_times / gaze_count * (1000 / fps) if gaze_count > 0 else 0,
        'target4': scan_dists / len(meta['stages']) - BIAS,
        'target5': gaze_times / len(meta['stages']) * (1000 / fps),
        'target6': scan_dists / scan_count - BIAS if scan_count > 0 else 0,
        'target7': 0,
        'target8': 0,
        'target9': 0,
        'target10': 0,
    }


def generate_em_test8_detail(meta):
    fps = meta['config']['fps']
    size = meta['config']['pointSize']
    width = meta['config']['width']
    height = meta['config']['height']

    gaze_count, gaze_times, gaze_areas = [0, 0], [0, 0], [0, 0]
    for i in range(1, 3):
        stage = meta['stages'][i]
        is_gaze, gaze_pos, gaze_grid = False, 0, {
            '0,0': 0, '0,1': 0, '0,2': 0,
            '1,0': 0, '1,1': 0, '1,2': 0,
            '2,0': 0, '2,1': 0, '2,2': 0,
        }
        for j in range(1, len(stage['data'])):
            if not is_valid_pos(stage['data'][gaze_pos]['ax'], stage['data'][gaze_pos]['ay']):
                gaze_pos = j
                continue
            if not is_valid_pos(stage['data'][j]['ax'], stage['data'][j]['ay']):
                continue
            if calc_distance(
                    stage['data'][j]['ax'],
                    stage['data'][j]['ay'],
                    stage['data'][gaze_pos]['ax'],
                    stage['data'][gaze_pos]['ay']
            ) < size + BIAS:
                if not is_gaze and j - gaze_pos > fps / 5:
                    is_gaze = True
                    x = int(stage['data'][j]['ax'] // (width / 3))
                    y = int(stage['data'][j]['ay'] // (height / 3))
                    gaze_grid[f'{x},{y}'] = 1
            else:
                if is_gaze:
                    gaze_count[i - 1] += 1
                    gaze_times[i - 1] += j - gaze_pos - 1
                is_gaze = False
                gaze_pos = j
        if is_gaze:
            j = len(stage['data'])
            gaze_count[i - 1] += 1
            gaze_times[i - 1] += j - gaze_pos - 1
        gaze_areas[i - 1] = reduce(lambda a, b: a + b, gaze_grid.values())
        gaze_areas[i - 1] -= gaze_grid[f'0,2']
        gaze_areas[i - 1] -= gaze_grid[f'2,0']

    val1 = gaze_count[0] + gaze_count[1]
    val2 = gaze_times[0] + gaze_times[1]

    return {
        'target1': val1,
        'target2': gaze_areas[0],
        'target3': gaze_areas[1],
        'target4': gaze_areas[0] + gaze_areas[1],
        'target5': val2 / val1 / (1000 / fps) if val1 > 0 else 0,
    }


def calc_score(parts):
    return (0.0312 * parts['part4']['target4']['val'] + 5.614 * parts['part1']['target7']['val'] + 0.000203 * parts['part2']['target5']['val'] - 9.637) / 10
