import azure.cognitiveservices.speech as speech_sdk
import asyncio
import logging


class Speech:
    SPEECH_KEY = "f10c50acceea4adc95536bdd747017f8"
    SPEECH_REGION = "eastus"
    SSML = '''
<speak version="1.0" 
       xmlns="http://www.w3.org/2001/10/synthesis" 
       xmlns:mstts="http://www.w3.org/2001/mstts" 
       xmlns:emo="http://www.w3.org/2009/10/emotionml" 
       xml:lang="zh-CN">
    <voice name="{{voice}}">
        <mstts:express-as style="{{style}}" styledegree="2">
            {{text}}
        </mstts:express-as>
    </voice>
</speak>
'''

    def speech_to_text(self, file_path: str, **kwargs) -> str:
        speech_config = speech_sdk.SpeechConfig(subscription=self.SPEECH_KEY, region=self.SPEECH_REGION)
        speech_config.speech_recognition_language = kwargs.get("language", "zh-CN")
        audio_config = speech_sdk.AudioConfig(filename=file_path)
        speech_recognizer = speech_sdk.SpeechRecognizer(speech_config=speech_config, audio_config=audio_config)
        result = speech_recognizer.recognize_once_async().get()
        return result.text

    def text_to_speech(self, file_path: str, **kwargs):
        text = self.SSML \
            .replace("{{voice}}", kwargs.get("voice", "zh-CN-XiaoxiaoMultilingualNeural")) \
            .replace("{{style}}", kwargs.get("style", "friendly")) \
            .replace("{{text}}", kwargs.get("text", ""))
        speech_config = speech_sdk.SpeechConfig(subscription=self.SPEECH_KEY, region=self.SPEECH_REGION)
        speech_config.set_speech_synthesis_output_format(
            speech_sdk.SpeechSynthesisOutputFormat.Audio24Khz48KBitRateMonoMp3
        )
        audio_config = speech_sdk.audio.AudioOutputConfig(filename=file_path)
        speech_synthesizer = speech_sdk.SpeechSynthesizer(speech_config=speech_config, audio_config=audio_config)
        speech_synthesizer.speak_ssml_async(text).get()

    async def text_to_speech_async(self, file_path: str, **kwargs):
        text = self.SSML \
            .replace("{{voice}}", kwargs.get("voice", "zh-CN-XiaoxiaoMultilingualNeural")) \
            .replace("{{style}}", kwargs.get("style", "friendly")) \
            .replace("{{text}}", kwargs.get("text", ""))
        speech_config = speech_sdk.SpeechConfig(subscription=self.SPEECH_KEY, region=self.SPEECH_REGION)
        speech_config.set_speech_synthesis_output_format(
            speech_sdk.SpeechSynthesisOutputFormat.Audio24Khz48KBitRateMonoMp3
        )
        audio_config = speech_sdk.audio.AudioOutputConfig(filename=file_path)
        speech_synthesizer = speech_sdk.SpeechSynthesizer(speech_config=speech_config, audio_config=audio_config)
        result_future = speech_synthesizer.speak_ssml_async(text)
        result = await asyncio.to_thread(result_future.get)

        if result.reason == speech_sdk.ResultReason.SynthesizingAudioCompleted:
            logging.info("Speech synthesized successfully.")
            return True
        elif result.reason == speech_sdk.ResultReason.Canceled:
            cancellation_details = result.cancellation_details
            logging.warning(f"Speech synthesis canceled: {cancellation_details.reason}, {cancellation_details.error_details}")
            return False
