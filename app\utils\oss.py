import os
import oss2
import asyncio


class Oss:
    ACCESS_KEY_ID = "LTAI5tH7d78REGEpZ4Cdr11k"
    ACCESS_KEY_SECRET = "******************************"
    ENDPOINT = "https://oss-cn-beijing.aliyuncs.com"
    BUCKET = "pingpang-prod"
    HOST = "https://static.higrace.life"

    def upload(self, path: str, file_path: str) -> str:
        auth = oss2.Auth(self.ACCESS_KEY_ID, self.ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, self.ENDPOINT, self.BUCKET)
        oss_file_path = os.path.join(path, os.path.basename(file_path))
        bucket.put_object_from_file(oss_file_path, file_path)
        return f"{self.HOST}/{oss_file_path}"

    async def upload_async(self, path: str, file_path: str) -> str:
        auth = oss2.Auth(self.ACCESS_KEY_ID, self.ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, self.ENDPOINT, self.BUCKET)
        oss_file_path = os.path.join(path, os.path.basename(file_path))
        await asyncio.to_thread(bucket.put_object_from_file, oss_file_path, file_path)
        return f"{self.HOST}/{oss_file_path}"