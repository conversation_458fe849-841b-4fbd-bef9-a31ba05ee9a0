import asyncio
import dotenv
from app.services.user_service import UserService

dotenv.load_dotenv()

class UserTools:
    
    def __init__(self) -> None:
        pass

    async def create_user(self, username, password):
        user_service = UserService()
        await user_service.register(username, password)

async def main():
    tools = UserTools()
    username = "jeff"
    password = "tsc102"
    await tools.create_user(username, password)

if __name__ == "__main__":
    asyncio.run(main())