# Standard library imports
import base64
import logging
import time
from io import BytesIO
from uuid import uuid4

# Third-party imports
from fastapi import BackgroundTasks
from PIL import Image

# Local application imports
from app.emotion.emotion_processor import EmotionProcessor
from app.schemas.emotion import EmotionRequest


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d [%(levelname).1s] [%(threadName)s] ( %(name)s:%(lineno)d) - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def run_test():
    image_path = '/Users/<USER>/workspace/llm-app/data/20241012_125756_origin.png'

    with Image.open(image_path) as image:
        buffered = BytesIO()
        image.save(buffered, format="JPEG")
        base64_image = base64.b64encode(buffered.getvalue()).decode("utf-8")

        processor = EmotionProcessor()
        background_tasks = BackgroundTasks()
        request = EmotionRequest(
            timestamp=int(time.time() * 1000),
            image=base64_image,
            user_id='jasonxia',
            session_id=str(uuid4()))
        result = await processor.process(request, background_tasks, None)
        logging.info(f"Process result: {result}")

        # 执行后台任务
        await background_tasks()
        logging.info("Background tasks completed")

if __name__ == '__main__':
    import asyncio
    asyncio.run(run_test())
