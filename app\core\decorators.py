from functools import wraps
from typing import Optional, List
from fastapi import HTTPException, Request
from app.services.user_service import UserService
from app.core.global_var import GlobalVar
from app.services.account_service import AccountService

def require_user_token(is_force = True):
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):

            request.state.user = None
            request.state.account = None

            # 从自定义header中获取token
            token = request.headers.get('TS-Auth-Token')
            if not token and is_force:
                raise HTTPException(
                    status_code=401,
                    detail=str({"code": "10011", "message": _("登录状态失效，请重新登录")})
                )
            if not token:
                return await func(request, *args, **kwargs)
            
            user_service = UserService(request)

            try:
                # 验证token并获取用户信息
                user_info, account_info = await user_service.verify_token(token)
                
                # 将用户信息添加到request.state中
                request.state.user = user_info
                request.state.account = account_info
                GlobalVar.set_var("current_user_info", user_info)
                GlobalVar.set_var("current_account_info", account_info)
                
                return await func(request, *args, **kwargs)
            except Exception as e:
                print(e)
                if is_force:
                    raise HTTPException(
                        status_code=401,
                        detail=str({"code": "10011", "message": _("登录状态失效，请重新登录")})
                    )
                else:
                    return await func(request, *args, **kwargs)
        return wrapper
    return decorator

def check_account_key():
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):

            app_key = request.headers.get('TS-APP-KEY')
            if not app_key:
                raise HTTPException(
                    status_code=401,
                    detail=str({"code": "10011", "message": "APPKEY状态失效，请检查有效性"})
                )
            
            account_service = AccountService()

            try:
                account_info = await account_service.check_app_key(app_key)
                
                # 将用户信息添加到request.state中
                request.state.account = account_info
                GlobalVar.set_var("current_account_info", account_info)
                
                return await func(request, *args, **kwargs)
            except Exception as e:
                raise HTTPException(
                    status_code=401,
                    detail=str({"code": "10011", "message": "APPKEY状态失效，请检查有效性"})
                )
        return wrapper
    return decorator