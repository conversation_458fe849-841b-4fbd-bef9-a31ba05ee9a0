from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from typing import Any, Dict, Optional
from app.models.base import Base, TimestampMixin, ModelUpdateMixin


class User(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'users'

    user_id = Column(Integer, primary_key=True)
    username = Column(String(100), default="")
    password = Column(String(32), default="")
    salt = Column(String(6), default="")
    name = Column(String(50), default="")
    gender = Column(Integer, default=0)
    birthday = Column(String(20), default="")
    grade = Column(Integer, default=-1)
    idnum = Column(String(100), default="")
    device_id = Column(String(100), default="")
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<User(user_id={self.user_id}, name={self.username}, password={self.password})>"
