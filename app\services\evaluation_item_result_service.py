from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.evaluation_item_result import EvaluationItemResult

class EvaluationItemResultService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_evaluation_item_result(self, new_evaluation_item_result):
        try:
            async with self.db_session() as session:
                session.add(new_evaluation_item_result)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_evaluation_item_result: {str(e)}")
            raise

        return new_evaluation_item_result

    async def get_item_result_list_by_result_id(self, result_id):
        try:
            async with self.db_session() as session:
                stmt = select(EvaluationItemResult).where(EvaluationItemResult.result_id == result_id)
                result = await session.execute(stmt)
                item_result_list = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_item_result_list_by_result_id: {str(e)}")
            raise

        return item_result_list

    async def get_item_result_by_id(self, item_result_id):
        try:
            async with self.db_session() as session:
                stmt = select(EvaluationItemResult).where(EvaluationItemResult.item_result_id == item_result_id)
                result = await session.execute(stmt)
                item_result_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_item_result_by_id: {str(e)}")
            raise

        return item_result_info
    
    async def update_item_result_by_id(self, item_result_id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(EvaluationItemResult).where(EvaluationItemResult.item_result_id == item_result_id)
                result = await session.execute(stmt)
                item_result_info = result.scalars().first()

                if not item_result_info:
                    return False

                await item_result_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in get_item_result_by_id: {str(e)}")
            raise

        return True