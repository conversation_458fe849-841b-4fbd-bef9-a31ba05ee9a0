from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy import select, insert
from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from datetime import datetime
import asyncio

Base = declarative_base()

DEBUG_DATABASE_URL='mysql+asyncmy://ts_user:IblIHh^PM36V@127.0.0.1/llmapp'
RELEASE_DATABASE_URL='mysql+asyncmy://root:YbbYwb%402024@localhost:13306/llmapp'

# 定义两个数据库的引擎
ts_engine = create_async_engine('mysql+asyncmy://ts_user:IblIHh^PM36V@**************/ts', echo=True)
llmapp_engine = create_async_engine(RELEASE_DATABASE_URL, echo=True)

# 创建异步session
ts_SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=ts_engine, class_=AsyncSession)
llmapp_SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=llmapp_engine, class_=AsyncSession)

# 你的Event和Message类应该已经定义了
class Event(Base):
    __tablename__ = 'event'

    idx = Column(Integer, primary_key=True)
    id = Column(String(36))
    type = Column(String(36))
    owner = Column(Integer)

    # category
    category = Column(String(100), default="default")
    prompt_id = Column(Integer, default=0)

    # scene info
    scene_id = Column(String(36))

    # user info
    user_id = Column(String(36))
    device_id = Column(String(36))

    # agent info
    agent_name = Column(String(36))
    agent_role = Column(String(36))

    # content
    text_data = Column(Text)
    image_data = Column(Text)
    audio_data = Column(Text)
    video_data = Column(Text)
    image_url = Column(String(1000), default="")
    audio_url = Column(String(1000), default="")
    video_url = Column(String(1000), default="")

    extra_json = Column(JSON)
    status = Column(Integer, default=1)
    prompt_level = Column(Integer, default=1)

    created = Column(DateTime, default=datetime.utcnow)
    updated = Column(DateTime, default=datetime.utcnow)


class Message(Base):
    __tablename__ = 'messages'

    idx = Column(Integer, primary_key=True)
    owner = Column(Integer, default=1)
    user_id = Column(String(512), default="")
    session_id = Column(String(512), default="")
    text_data = Column(Text, default="")
    extra_json = Column(JSON)
    created = Column(DateTime, default=datetime.utcnow)
    updated = Column(DateTime, default=datetime.utcnow)


async def migrate_event_to_message():
    async with ts_SessionLocal() as ts_session, llmapp_SessionLocal() as llmapp_session:
        # 查询 ts 数据库中的 event 表

        user_ids = [
            'oAfMX6XaZ5o4HPZoBpN0g8cOsrrI',
            'oAfMX6fAR0wsUmDcvnDjx_hs0GyI',
            'oAfMX6QwjkfDZlHDQgxBSqxf3ur8',
            'oAfMX6TLLyQltom2xpnMNz7Izwes',
            'oAfMX6fhi2zTu8F9MipVFWc_-YbU',
            'oAfMX6Xyj0CgAZ-o7pNmTnBRaj2E',
            'oAfMX6UNutrayjznAn5zxooMukq4',
            'oAfMX6YWCpQkiS5NZxPoJO_cKW4E',
            'oAfMX6ZhhuPknQ1QZ8HzGoqjYobk',
            'oRcZo6nsfn52Dh2OhAv8042ObG08',
            'oAfMX6VEx3zaEJevy1A-ZL65zI-Y',
            'oAfMX6W38nZY7LtbUvCt5FWUuab4',
        ]

        query = select(Event).where(Event.user_id.in_(user_ids))
        results = await ts_session.execute(query)
        events = results.scalars().all()

        # 遍历查询结果，将其插入到 llmapp 数据库的 messages 表
        for event in events:
            print(f"Processing event {event.user_id=}, {event.owner=}, {event.scene_id=}, {event.text_data=}")
            new_message = {
                "owner": event.owner,
                "user_id": event.user_id,
                "session_id": event.scene_id,  # 可以使用 event.scene_id 作为 session_id
                "text_data": event.text_data,
                "extra_json": event.extra_json
            }
            insert_stmt = insert(Message).values(new_message)
            await llmapp_session.execute(insert_stmt)

        # 提交更改
        await llmapp_session.commit()

if __name__ == "__main__":
    print("this script is danger, uncomment the code to migrate data")
    # asyncio.run(migrate_event_to_message())