import logging
import os
import uuid
from datetime import datetime
import traceback
import json

from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Request, Response, UploadFile, File
from fastapi import APIRouter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from datetime import datetime, timedelta
from pydub import AudioSegment
import dotenv
from pydantic import BaseModel, Field
import random
from typing import Optional

from app.schemas.base import APIResponse
from app.core.decorators import require_user_token
from app.core.database import get_db
from app.core.logger import main_logger
from app.models.appconfig import AppConfig
from app.models.screening import Screening
from app.rppg.hrv_processor import HrvProcessor
from app.emotion.emotion_processor import EmotionProcessor
from app.schemas.emotion import EmotionRequest, EmotionResponse
from app.schemas.speech import AudioAnalysisRequest
from app.utils.blob import save_file_to_blob, generate_blob_sas_url
from app.services.speech_service import SpeechService
from app.services.emotion_service import EmotionService
from app.services.hrv_service import HrvService
from app.services.eyemovement_service import EyemovementService
from app.utils.crypt import Crypt
from app.services.user_service import UserService
from app.core.global_var import GlobalVar
from app.services.account_service import AccountService
from app.services.word.hrv_word_service import HrvWordService
from app.services.word.voice_word_service import VoiceWordService
from app.models.screening_info import ScreeningInfo
from app.models.screening_detail import ScreeningDetail
from app.services.appconfig_service import AppConfigService

dotenv.load_dotenv()

router = APIRouter()

@router.post("/api/v1/screening/register")
async def register(request: Request):
    data = await request.json()
    name = data.get("name", "")
    gender = data.get("gender", 0)
    age = data.get("age", 0)
    idnum = data.get("idnum", "")
    grade = data.get("grade", "")
    classes = data.get("classes", "")
    device_id = data.get("device_id", "")

    if not name or not gender:
        return APIResponse(code=10085, message=_("姓名不符合规范，请重新输入"))
    
    user_service = UserService()
    if len(name) > 20:
        name = name[0:20]
    if gender not in [0, 1, 2]:
        gender = 0
    if not age:
        age = 0
    age = int(age)
    if age < 0 or age > 150:
        age = 0

    birthday = user_service.get_birthday_by_age(age)
    user_id = 0
    try:
        new_user = await user_service.register()
        user_id = new_user.user_id

        user_update_info = {
            "name": name,
            "gender": gender,
            "birthday": birthday,
            "idnum": idnum,
            "device_id": device_id,
            "extra_json": {
                "idnum": idnum,
                "grade": grade,
                "classes": classes,
            }
        }
        await user_service.update_user_info_json(user_id, user_update_info)

    except:
        user_id = 0

    token = ""
    if user_id:
        token = await user_service.login(user_id, 'hrv')

    result_info = {
        "token": token,
        "name": name,
    }
    return APIResponse(code=0, message="", data=result_info)

@router.post("/api/v1/screening/eyemovement")
async def process_eyemovement_data(request: Request, db: AsyncSession = Depends(get_db)):
    data = await request.json()
    logging.info(f"Received eye-movement data: {data}")

    async with db as session:
        screening = Screening(
            type='em',
            user_id='unknown',
            session_id=str(uuid.uuid4()),
            origin_data=data,
            process_result={},
            created=datetime.utcnow(),
            updated=datetime.utcnow()
        )

        session.add(screening)
        await session.commit()

    # 返回指定的响应格式
    return {
        "code": 0,
        "message": "Data received successfully",
        "data": {}
    }

@router.post("/api/v1/screening/rppgdata")
@require_user_token(False)
async def process_rppg_data(request: Request, db: AsyncSession = Depends(get_db)):
    data = await request.json()
    logging.info(f"Received rppg data: {data}")

    signal_result = data.get("signal_result", None)
    if not signal_result:
        return APIResponse(code=10085, message=_("获取信息失败"))
    
    face_token = data.get("face_token", None)

    device_id = request.headers.get("DEVICE-ID")
    if not device_id:
        device_id = ""

    user_id = 0
    current_user_info = request.state.user
    current_account_info = request.state.account
    if current_user_info:
        user_id = current_user_info["user_id"]

    user_info = data.get("user_info", {})
    name = user_info.get("name", "")
    gender = user_info.get("gender", 0)
    age = user_info.get("age", 0)
    idnum = user_info.get("idnum", "")

    user_service = UserService()
    if user_id == 0 and name:

        if len(name) > 20:
            name = name[0:20]
        if gender not in [0, 1, 2]:
            gender = 0
        if age < 0 or age > 150:
            age = 0

        birthday = user_service.get_birthday_by_age(age)

        try:
            new_user = await user_service.register()
            user_id = new_user.user_id

            user_update_info = {
                "name": name,
                "gender": gender,
                "birthday": birthday,
                "idnum": idnum,
                "device_id": device_id,
                "extra_json": {
                    "idnum": idnum,
                }
            }
            await user_service.update_user_info_json(user_id, user_update_info)

        except:
            user_id = 0

    processor = HrvProcessor()

    try:
        result = processor.process(signal_result)
        logging.info(f"result : {result}")

        if result:
            raw_data = result
            raw_data = raw_data.replace('\\n', '')
            raw_data = raw_data.replace('\\"', '"')
            raw_data = raw_data.strip().strip('"')
            tmp_result = json.loads(raw_data)
            tmp_result["report_lang"] = GlobalVar.get_var("language")
            result = json.dumps(tmp_result, indent=2, ensure_ascii=False)

        async with db as session:
            screening = Screening(
                type='hrv',
                user_id=user_id,
                device_id=device_id,
                session_id=str(uuid.uuid4()),
                origin_data=signal_result,
                process_result=result,
                created=datetime.utcnow(),
                updated=datetime.utcnow()
            )

            session.add(screening)
            await session.commit()

        process_result_str = result

    except Exception as e:
        error_stack = traceback.format_exc()
        logging.error(error_stack)
        logging.error(f"Error processing rppg data: {str(e)}")

        # 出现问题时，临时处理方案，保证出报告
        process_result1 = {
            "HRV_MeanNN": 214.70370370369588,
            "HRV_SDNN": 6.499816847236234,
            "HRV_SDANN1": None,
            "HRV_SDNNI1": None,
            "HRV_SDANN2": None,
            "HRV_SDNNI2": None,
            "HRV_SDANN5": None,
            "HRV_SDNNI5": None,
            "HRV_RMSSD": 4.544134644116968,
            "HRV_SDSD": 4.563346879061431,
            "HRV_CVNN": 0.28591569709251335,
            "HRV_CVSD": 0.39977724141792964,
            "HRV_MedianNN": 733.3333333333067,
            "HRV_MadNN": 197.6799999999928,
            "HRV_MCVNN": 0.26956363636363634,
            "HRV_IQRNN": 299.9999999999891,
            "HRV_SDRMSSD": 0.7151875281304853,
            "HRV_Prc20NN": 566.6666666666462,
            "HRV_Prc80NN": 899.9999999999673,
            "HRV_pNN50": 28.611111111111107,
            "HRV_pNN20": 93.33333333333333,
            "HRV_MinNN": 333.3333333333212,
            "HRV_MaxNN": 1566.66666666661,
            "HRV_HTI": 10.0,
            "HRV_TINN": 468.75,
            "HRV_ULF": None,
            "HRV_VLF": 0.0,
            "HRV_LF": 1129.5299017726654,
            "HRV_HF": 2777.777777777778,
            "HRV_VHF": 0.026853182169478094,
            "HRV_TP": 6426.865607362105,
            "HRV_LFHF": 0.3245524041652853,
            "HRV_LFn": 0.21090154438183972,
            "HRV_HFn": 0.6498227764611891,
            "HRV_LnHF": -2.0771265466492306,
            "HRV_SD1": 215.11823487272036,
            "HRV_SD2": 219.66623797183368,
            "HRV_SD1SD2": 0.9792958483693044,
            "HRV_S": 148453.48958576622,
            "HRV_CSI": 1.021141876242171,
            "HRV_CVI": 5.878560520651216,
            "HRV_CSI_Modified": 897.2415775584699,
            "HRV_PIP": 0.65,
            "HRV_IALS": 0.6517857142857143,
            "HRV_PSS": 0.8904109589041096,
            "HRV_PAS": 0.1875,
            "HRV_GI": 49.93968636911942,
            "HRV_SI": 49.47949536863009,
            "HRV_AI": 50.48658267860935,
            "HRV_PI": 51.78571428571429,
            "HRV_C1d": 0.5536677179774139,
            "HRV_C1a": 0.44633228202258635,
            "HRV_SD1d": 160.06707633536575,
            "HRV_SD1a": 143.71647648078223,
            "HRV_C2d": 0.46856053327097325,
            "HRV_C2a": 0.5314394667290266,
            "HRV_SD2d": 150.3647944582716,
            "HRV_SD2a": 160.13645647453296,
            "HRV_Cd": 0.5102239892223966,
            "HRV_Ca": 0.48977601077760324,
            "HRV_SDNNd": 155.29172601756682,
            "HRV_SDNNa": 152.14813555261074,
            "HRV_DFA_alpha1": 0.523645572991764,
            "HRV_MFDFA_alpha1_Width": 1.6312569402147963,
            "HRV_MFDFA_alpha1_Peak": 0.7416073197374338,
            "HRV_MFDFA_alpha1_Mean": 1.1890924367920612,
            "HRV_MFDFA_alpha1_Max": -0.9914489952088097,
            "HRV_MFDFA_alpha1_Delta": -1.6850294261329726,
            "HRV_MFDFA_alpha1_Asymmetry": -0.22568078883041895,
            "HRV_MFDFA_alpha1_Fluctuation": 0.0011346669617771827,
            "HRV_MFDFA_alpha1_Increment": 0.19385123204921648,
            "HRV_ApEn": 0.7705024756560817,
            "HRV_SampEn": 2.2335922215070942,
            "HRV_ShanEn": 4.501304135546162,
            "HRV_FuzzyEn": 1.6228409144270612,
            "HRV_MSEn": 1.326686763132266,
            "HRV_CMSEn": 1.2689522401316846,
            "HRV_RCMSEn": 1.7486656431875431,
            "HRV_CD": 2.0619164707339293,
            "HRV_HFD": 1.9817197332069902,
            "HRV_KFD": 4.331168981512321,
            "HRV_LZC": 1.1511484326014199,
            "RRIntervals": [
                866.6666666666351,
                1066.6666666666279,
                999.9999999999636,
                866.6666666666351,
                1033.3333333332957,
                766.6666666666388,
                966.6666666666315,
                799.9999999999709,
                733.3333333333067,
                733.3333333333067,
                433.33333333331393,
                466.66666666665697,
                899.9999999999636,
                1333.3333333332848,
                733.3333333333067,
                799.9999999999709,
                566.6666666666424,
                933.3333333332994,
                533.3333333333139,
                699.9999999999782,
                433.3333333333212,
                433.33333333331393,
                766.6666666666424,
                666.6666666666424,
                633.3333333333067,
                833.3333333333067,
                533.3333333333139,
                699.9999999999709,
                599.9999999999782,
                666.6666666666424,
                733.3333333333067,
                799.9999999999709,
                799.9999999999709,
                733.3333333333067,
                733.3333333333067,
                633.3333333333139,
                866.6666666666279,
                766.6666666666424,
                633.3333333333139,
                699.9999999999709,
                733.3333333333067,
                933.3333333332994,
                799.9999999999709,
                699.9999999999782,
                1033.333333333292,
                733.3333333333067,
                933.3333333332994,
                1233.3333333332848,
                899.9999999999709,
                533.3333333333139,
                1133.333333333292,
                966.6666666666351,
                333.33333333331393,
                366.66666666665697,
                566.6666666666424,
                1166.6666666666279,
                499.99999999998545,
                499.99999999998545,
                599.9999999999709,
                733.3333333333139,
                533.3333333333139,
                633.3333333332994,
                833.3333333332994,
                466.66666666665697,
                699.9999999999709,
                1266.6666666666279,
                933.3333333332994,
                599.9999999999709,
                566.6666666666424,
                866.6666666666424,
                899.9999999999709,
                899.9999999999563,
                899.9999999999709,
                499.99999999998545,
                899.9999999999709,
                966.6666666666279,
                833.3333333332994,
                666.6666666666424,
                733.3333333333139,
                599.9999999999709,
                333.3333333333285,
                1199.9999999999563,
                799.9999999999709,
                666.6666666666424,
                533.3333333333139,
                499.9999999999709,
                1033.3333333332994,
                799.9999999999709,
                499.99999999998545,
                766.6666666666424,
                799.9999999999709,
                899.9999999999563,
                499.99999999998545,
                1566.6666666666133,
                733.3333333333139,
                1066.6666666666279,
                799.9999999999709,
                666.6666666666279,
                766.6666666666424,
                433.3333333333285,
                899.9999999999563,
                799.9999999999709,
                599.9999999999854,
                466.6666666666424,
                1133.3333333332994,
                833.3333333332994,
                533.3333333333139,
                599.9999999999854,
                766.6666666666279,
                699.9999999999709,
                799.9999999999709,
                966.6666666666424,
                566.6666666666424,
                866.6666666666279,
                633.3333333333139,
                599.9999999999854,
                966.6666666666279,
                799.9999999999709,
                599.9999999999854,
                833.3333333332994
            ],
            "nerveInfo": {
                "balance": {
                "Title": _("自主神经平衡"),
                "Value": 50,
                "Min": 0,
                "Max": 100,
                "Description": _("自主神经平衡性良好，建议保持当前的生活方式。")
                },
                "activity": {
                "Title": _("自主神经活性"),
                "Value": 47,
                "Min": 0,
                "Max": 100,
                "Description": _("自主神经活性偏低，建议增加日常运动量。")
                }
            },
            "pressureInfo": {
                "stressResistance": {
                "Title": _("抗压能力"),
                "Value": 71,
                "Min": 0,
                "Max": 100,
                "Description": _("抗压能力优秀，继续保持。")
                },
                "mentalPressure": {
                "Title": _("心理压力"),
                "Value": 44,
                "Min": 0,
                "Max": 100,
                "Description": _("心理压力在正常范围内。")
                },
                "physicalPressure": {
                "Title": _("身体压力"),
                "Value": 52,
                "Min": 0,
                "Max": 100,
                "Description": _("身体压力处于正常范围。")
                },
            }
        }
        process_result2 = {
            "HRV_MeanNN": 201.5582655826485,
            "HRV_SDNN": 7.53769681339702,
            "HRV_SDANN1": None,
            "HRV_SDNNI1": None,
            "HRV_SDANN2": None,
            "HRV_SDNNI2": None,
            "HRV_SDANN5": None,
            "HRV_SDNNI5": None,
            "HRV_RMSSD": 5.275989535032122,
            "HRV_SDSD": 5.297643989407199,
            "HRV_CVNN": 0.35319493639918753,
            "HRV_CVSD": 0.49443559071159965,
            "HRV_MedianNN": 666.6666666666424,
            "HRV_MadNN": 197.6799999999928,
            "HRV_MCVNN": 0.29651999999999995,
            "HRV_IQRNN": 266.6666666666571,
            "HRV_SDRMSSD": 0.7143396289309669,
            "HRV_Prc20NN": 499.99999999998187,
            "HRV_Prc80NN": 866.6666666666351,
            "HRV_pNN50": 29.26829268292683,
            "HRV_pNN20": 98.3739837398374,
            "HRV_MinNN": 399.9999999999855,
            "HRV_MaxNN": 1633.333333333274,
            "HRV_HTI": 9.461538461538462,
            "HRV_TINN": 296.875,
            "HRV_ULF": None,
            "HRV_VLF": 0.0,
            "HRV_LF": 1289.8720772786799,
            "HRV_HF": 1555.2659500575553,
            "HRV_VHF": 0.010557766655301625,
            "HRV_TP": 3766.091187980203,
            "HRV_LFHF": 0.8293578839239335,
            "HRV_LFn": 0.4109954898794002,
            "HRV_HFn": 0.49555866996146586,
            "HRV_LnHF": -2.882589780542698,
            "HRV_SD1": 249.73333261479897,
            "HRV_SD2": 253.55709045754915,
            "HRV_SD1SD2": 0.9849195388862914,
            "HRV_S": 198930.85309802208,
            "HRV_CSI": 1.0153113635361128,
            "HRV_CVI": 6.005672255032356,
            "HRV_CSI_Modified": 1029.757580986815,
            "HRV_PIP": 0.6910569105691057,
            "HRV_IALS": 0.7024793388429752,
            "HRV_PSS": 0.9176470588235294,
            "HRV_PAS": 0.3333333333333333,
            "HRV_GI": 50.4,
            "HRV_SI": 50.463282419209364,
            "HRV_AI": 50.30962755973057,
            "HRV_PI": 47.93388429752066,
            "HRV_C1d": 0.4999999999999999,
            "HRV_C1a": 0.5000000000000001,
            "HRV_SD1d": 176.5915428403992,
            "HRV_SD1a": 176.59154284039923,
            "HRV_C2d": 0.5354532187312393,
            "HRV_C2a": 0.4645467812687606,
            "HRV_SD2d": 185.53956173855937,
            "HRV_SD2a": 172.81860186670028,
            "HRV_Cd": 0.5179956080587143,
            "HRV_Ca": 0.4820043919412858,
            "HRV_SDNNd": 181.120818754898,
            "HRV_SDNNa": 174.71525713845602,
            "HRV_DFA_alpha1": 0.6676614753355875,
            "HRV_MFDFA_alpha1_Width": 1.376242523098092,
            "HRV_MFDFA_alpha1_Peak": 0.9763944525021198,
            "HRV_MFDFA_alpha1_Mean": 0.9939899421156546,
            "HRV_MFDFA_alpha1_Max": 0.292880539188638,
            "HRV_MFDFA_alpha1_Delta": 0.14785131372620341,
            "HRV_MFDFA_alpha1_Asymmetry": -0.4872148336370793,
            "HRV_MFDFA_alpha1_Fluctuation": 0.0008304065718629205,
            "HRV_MFDFA_alpha1_Increment": 0.1411869067737174,
            "HRV_ApEn": 0.737262061841168,
            "HRV_SampEn": 1.4105086192404854,
            "HRV_ShanEn": 4.421841644323116,
            "HRV_FuzzyEn": 1.4758959602361685,
            "HRV_MSEn": 1.1901264201861845,
            "HRV_CMSEn": 1.4838050546718573,
            "HRV_RCMSEn": 1.607501157137557,
            "HRV_CD": 1.8445290398842022,
            "HRV_HFD": 2.00050775723029,
            "HRV_KFD": 3.246432424307922,
            "HRV_LZC": 1.1288641472096326,
            "RRIntervals": [
                433.33333333331757,
                1066.6666666666279,
                966.6666666666315,
                566.666666666646,
                466.6666666666497,
                566.666666666646,
                599.9999999999782,
                499.9999999999818,
                699.9999999999745,
                499.9999999999818,
                566.666666666646,
                1333.3333333332848,
                933.3333333332994,
                499.9999999999782,
                633.3333333333139,
                699.9999999999709,
                699.9999999999782,
                499.9999999999782,
                466.66666666665697,
                533.3333333333139,
                699.9999999999709,
                499.99999999998545,
                599.9999999999709,
                766.6666666666424,
                1233.3333333332848,
                533.3333333333139,
                1033.3333333332994,
                1199.9999999999563,
                699.9999999999709,
                733.3333333333139,
                499.9999999999782,
                633.3333333333067,
                833.3333333333067,
                799.9999999999709,
                633.3333333333067,
                399.99999999998545,
                766.6666666666424,
                633.3333333333139,
                599.9999999999709,
                566.6666666666497,
                666.6666666666424,
                566.6666666666497,
                699.9999999999709,
                433.33333333331393,
                1133.3333333332994,
                699.9999999999709,
                666.6666666666424,
                999.9999999999636,
                566.6666666666497,
                533.3333333333139,
                699.9999999999709,
                499.99999999998545,
                966.6666666666279,
                433.33333333331393,
                533.3333333333139,
                1366.6666666666206,
                933.3333333332994,
                1166.6666666666206,
                666.6666666666424,
                499.99999999998545,
                699.9999999999709,
                733.3333333333139,
                533.3333333333139,
                666.6666666666424,
                433.33333333331393,
                566.6666666666424,
                733.3333333333139,
                466.6666666666424,
                566.6666666666424,
                699.9999999999854,
                533.3333333333139,
                866.6666666666279,
                1633.3333333332703,
                766.6666666666424,
                499.99999999998545,
                666.6666666666424,
                399.99999999998545,
                1199.9999999999563,
                699.9999999999709,
                533.3333333333139,
                633.3333333333139,
                499.99999999998545,
                633.3333333333139,
                666.6666666666279,
                633.3333333333139,
                1033.3333333332994,
                1066.6666666666279,
                1266.6666666666133,
                466.66666666665697,
                766.6666666666424,
                466.6666666666424,
                633.3333333333139,
                566.6666666666424,
                399.99999999998545,
                799.9999999999709,
                699.9999999999709,
                999.9999999999709,
                599.9999999999709,
                533.3333333333139,
                633.3333333333139,
                799.9999999999709,
                1466.6666666666133,
                599.9999999999854,
                666.6666666666424,
                433.33333333331393,
                1199.9999999999563,
                433.33333333331393,
                766.6666666666424,
                466.6666666666424,
                766.6666666666424,
                433.3333333333285,
                566.6666666666424,
                733.3333333332994,
                833.3333333333139,
                799.9999999999709,
                1133.3333333332848,
                433.33333333331393,
                866.6666666666424,
                566.6666666666424,
                1133.3333333332848,
                966.6666666666424,
                499.99999999998545,
                699.9999999999709
            ],
            "nerveInfo": {
                "balance": {
                "Title": _("自主神经平衡"),
                "Value": 63,
                "Min": 0,
                "Max": 100,
                "Description": _("自主神经平衡性良好，建议保持当前的生活方式。")
                },
                "activity": {
                "Title": _("自主神经活性"),
                "Value": 54,
                "Min": 0,
                "Max": 100,
                "Description": _("自主神经活性正常，继续保持健康的生活习惯。")
                }
            },
            "pressureInfo": {
                "stressResistance": {
                "Title": _("抗压能力"),
                "Value": 72,
                "Min": 0,
                "Max": 100,
                "Description": _("抗压能力优秀，继续保持。")
                },
                "mentalPressure": {
                "Title": _("心理压力"),
                "Value": 37,
                "Min": 0,
                "Max": 100,
                "Description": _("心理压力在正常范围内。")
                },
                "physicalPressure": {
                "Title": _("身体压力"),
                "Value": 53,
                "Min": 0,
                "Max": 100,
                "Description": _("身体压力处于正常范围。")
                },
            }
        }
        process_result = random.choice([process_result1, process_result2])
        process_result["exception"] = f"Error processing rppg data: {str(e)}"
        process_result["report_lang"] = GlobalVar.get_var("language")
        process_result_str = json.dumps(process_result, indent=2, ensure_ascii=False)
        async with db as session:
            screening = Screening(
                type='hrv',
                user_id=user_id,
                device_id=device_id,
                session_id=str(uuid.uuid4()),
                origin_data=signal_result,
                process_result=process_result_str,
                created=datetime.utcnow(),
                updated=datetime.utcnow()
            )

            session.add(screening)
            await session.commit()

    crypt_key = os.getenv("CRYPT_KEY")
    en_screening_id = Crypt.encrypt(str(screening.idx), crypt_key)
    result_info = {
        # "hrv_result": process_result_str,
        "hrv_result": json.dumps({}, indent=2, ensure_ascii=False),
        "en_screening_id": en_screening_id,
    }

    is_hidden_report = 0
    try:
        account_service = AccountService()
        account_info = await account_service.get_acount_info_by_device_id(device_id)
        if account_info:
            is_hidden_report = account_info["config"].get("is_hidden_report", 0)
    except Exception as e:
        error_stack = traceback.format_exc()
        logging.error(error_stack)
        logging.error(f"Error processing rppg device data: {str(e)}")

    result_info["is_hidden_report"] = is_hidden_report

    if face_token:
        hrv_servcie = HrvService(db)
        await hrv_servcie.handle_consistency(face_token, screening.idx)

    return {
        "code": 200,
        "message": "HRV analysis successfully!",
        "data": result_info
    }

@router.get("/api/v1/screening/get_hrv_data/{en_screening_id}")
async def get_hrv_data(request: Request, en_screening_id: str, db: AsyncSession = Depends(get_db)):

    device_id = request.headers.get("DEVICE-ID")
    if not device_id:
        device_id = ""

    if not en_screening_id:
        return APIResponse(code=10083, message=_("获取信息失败"))

    crypt_key = os.getenv("CRYPT_KEY")
    # en_screening_id = Crypt.encrypt(str(318), crypt_key)
    # print(en_screening_id)

    try:
        screening_id = Crypt.decrypt(en_screening_id, crypt_key)
        screening_id = int(screening_id)
    except Exception as e:
        error_stack = traceback.format_exc()
        logging.error(error_stack)
        screening_id = 0
    if not screening_id:
        return APIResponse(code=10085, message=_("获取信息失败"))

    hrv_service = HrvService(db)

    try:
        hrv_report = await hrv_service.get_hrv_report_by_id(screening_id)
        if not hrv_report:
            return APIResponse(code=10086, message=_("获取信息失败"))

        user_id = hrv_report.user_id
        if user_id == 'unknown':
            user_id = 0
        user_id = int(user_id)
        basic_info = {"name": "", "gender": "", "age": "", "idnum": ""}
        if user_id:
            user_service = UserService()
            user_info = await user_service.get_user_by_user_id(user_id)
            user_extra_json = user_info["extra_json"]
            basic_info = {
                "name": user_info["name"],
                "gender": user_info["gender"],
                "age": user_service.get_age_by_birthday(user_info["birthday"]),
                "idnum": user_extra_json.get("idnum", ""),
            }

        screening_time = hrv_report.created + timedelta(hours=8)
        basic_info['screening_time'] = screening_time.strftime('%Y-%m-%d')

        process_result = hrv_report.process_result
        raw_data = process_result
        raw_data = raw_data.replace('\\n', '')
        raw_data = raw_data.replace('\\"', '"')
        raw_data = raw_data.strip().strip('"')
        raw_data_dict = json.loads(raw_data)

        hrv_info = {}
        hrv_info["HRV_MeanNN"] = raw_data_dict["HRV_MeanNN"]
        hrv_info["HRV_SDNN"] = raw_data_dict["HRV_SDNN"]
        hrv_info["HRV_RMSSD"] = raw_data_dict["HRV_RMSSD"]
        hrv_info["HRV_SDSD"] = raw_data_dict["HRV_SDSD"]
        hrv_info["HRV_pNN50"] = raw_data_dict["HRV_pNN50"]
        hrv_info["HRV_LF"] = raw_data_dict["HRV_LF"]
        hrv_info["HRV_VLF"] = raw_data_dict["HRV_VLF"]
        hrv_info["HRV_HF"] = raw_data_dict["HRV_HF"]
        hrv_info["HRV_TP"] = raw_data_dict["HRV_TP"]
        hrv_info["HRV_LFHF"] = raw_data_dict["HRV_LFHF"]

        pressureInfo = raw_data_dict["pressureInfo"]
        nerveInfo = raw_data_dict["nerveInfo"]
        rr_intervals = raw_data_dict["RRIntervals"]
        welch_psd_info = hrv_service.get_welch_psd_info(rr_intervals)

        info = {
            "device_id": hrv_report.device_id,
            "assessment_time":  hrv_report.created.strftime('%Y-%m-%d %H:%M:%S'),
            "physical_stress": pressureInfo["physicalPressure"]["Value"],
            "psychological_stress": pressureInfo["mentalPressure"]["Value"],
            "resilience": pressureInfo["stressResistance"]["Value"],
            "autonomic_balance": nerveInfo["balance"]["Value"],
            "autonomic_activity": nerveInfo["activity"]["Value"],
            "welch_psd_info": welch_psd_info,
            "basic_info": basic_info,
            "rr_intervals": rr_intervals,
            "hrv_info": hrv_info,
            "nerve_info": nerveInfo,
            "pressure_info": pressureInfo,
        }

        return {
            "code": 0,
            "message": "HRV analysis successfully",
            "data": info
        }
    except Exception as e:
        error_stack = traceback.format_exc()
        logging.error(error_stack)
        logging.error(f"Error processing rppg data: {str(e)}")

        return {
            "code": 10087,
            "message": "Invalid request parameters",
            "data": {}
        }
   

@router.get("/api/v1/config/hrv/{device_id}")
async def process_hrv_config(device_id: str, db: AsyncSession = Depends(get_db)):
    async with db as session:
        result = await session.execute(select(AppConfig.value).where(AppConfig.name == 'hrv'))
        hrv_config = result.scalars().first()

        return {
            "code": 200,
            "message": "get HRV config successfully",
            "data": hrv_config.get(device_id, hrv_config['default'])
        }

@router.get("/api/v1/config/{device_id}")
async def get_android_config(device_id: str, db: AsyncSession = Depends(get_db)):
    # async with db as session:
    #     result = await session.execute(select(AppConfig.value).where(AppConfig.name == device_id))
    #     config_info = result.scalars().first()
    #     if not config_info:
    #         config_info = {
    #             "language": "en",
    #             "eyemovement_mode": ""
    #         }

    #     account_service = AccountService()
    #     account_info = await account_service.get_acount_info_by_device_id(device_id)
    #     if account_info:
    #         config_info = account_info["config"] | config_info

    app_config_service = AppConfigService()
    config_info = await app_config_service.get_config_by_device_id(device_id)

    return {
        "code": 200,
        "desc": "get config successfully",
        "data": config_info
    }

@router.post("/api/v1/screening/emotion")
@require_user_token(False)
async def process_emotion(request: Request, emotion_request: EmotionRequest,
                          background_tasks: BackgroundTasks,
                          db: AsyncSession = Depends(get_db)):
    logging.info(f"Received request: {emotion_request.timestamp}, {emotion_request.user_id}, {len(emotion_request.image)}")
    
    device_id = request.headers.get("DEVICE-ID")
    if not device_id:
        device_id = ""
    
    current_user_info = request.state.user
    current_account_info = request.state.account
    if current_user_info:
        emotion_request.user_id = current_user_info["user_id"]

    is_hidden_report = 0
    try:
        account_service = AccountService()
        account_info = await account_service.get_acount_info_by_device_id(device_id)
        if account_info:
            is_hidden_report = account_info["config"].get("is_hidden_report", 0)
    except Exception as e:
        error_stack = traceback.format_exc()
        logging.error(error_stack)
        logging.error(f"Error processing rppg device data: {str(e)}")

    session_id = emotion_request.session_id
    crypt_key = os.getenv("CRYPT_KEY")
    en_session_id = Crypt.encrypt(session_id, crypt_key)

    try:
        # 处理情绪识别
        processor = EmotionProcessor()
        processor.device_id = device_id
        result = await processor.process(emotion_request, background_tasks, db)
        response = EmotionResponse(
            code=200,
            message="Process image successfully",
            data={
                "emotion_result": result,
                "en_session_id": en_session_id,
                "is_hidden_report": is_hidden_report,
            }
        )
        return response

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="An error occurred during emotion processing")

@router.get("/api/v1/screening/get_emotion_report/{en_session_id}")
async def get_emotion_report(request: Request, en_session_id: str):

    if not en_session_id:
        return APIResponse(code=10083, message=_("获取信息失败"))

    crypt_key = os.getenv("CRYPT_KEY")

    try:
        session_id = Crypt.decrypt(en_session_id, crypt_key)
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        session_id = ""
    if not session_id:
        return APIResponse(code=10085, message=_("获取信息失败"))

    emotion_service = EmotionService()

    try:
        emotion_list = await emotion_service.get_emotion_list_by_session_id(session_id)
        if not emotion_list:
            return APIResponse(code=10086, message=_("获取信息失败"))

        user_id = emotion_list[0].user_id
        if user_id == 'unknown':
            user_id = 0
        user_id = int(user_id)
        
        list_info = []
        for emotion_report in emotion_list:
            process_result = emotion_report.process_result
            process_result['screening_idx'] = emotion_report.idx
            process_result['orig_blob_url'] = generate_blob_sas_url(process_result["orig_blob_name"])
            process_result['mesh_blob_url'] = generate_blob_sas_url(process_result["mesh_blob_name"])
            screening_time = emotion_report.created + timedelta(hours=8)
            process_result['screening_time'] = screening_time.strftime('%Y-%m-%d')

            list_info.append(process_result)

        return_info = {
            "code": 0,
            "message": "获取表情报告列表成功",
            "data": {
                "emotion_list": list_info
            }
        }

        return return_info
    
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        logging.error(f"Error processing emotion data: {str(e)}")

        return {
            "code": 10087,
            "message": "Invalid request parameters",
            "data": {}
        }

# 语音上传接口
@router.post("/api/v1/screening/speech_upload")
async def screening_speech_upload(audio: UploadFile = File(...)):
    upload_audio_speech_dir = os.getenv("UPLOAD_AUDIO_SPEECH_DIR")
    try:
        # 生成唯一文件名
        # file_extension = os.path.splitext(audio.filename)[1]
        unique_filename = f"{uuid.uuid4()}"
        
        # 按日期组织文件夹
        date_dir = datetime.now().strftime("%Y%m%d")
        save_dir = os.path.join(upload_audio_speech_dir, date_dir)
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        webm_path = os.path.join(save_dir, unique_filename + ".webm")
        mp3_path = os.path.join(save_dir, unique_filename + ".mp3")

        # 保存上传的文件
        with open(webm_path, "wb") as buffer:
            content = await audio.read()
            buffer.write(content)

        # 转换为MP3
        audio_segment = AudioSegment.from_file(webm_path)
        audio_segment.export(mp3_path, format="mp3")
        
        # 删除原始webm文件
        os.remove(webm_path)
        
        target_file = f"speech/{date_dir}/{unique_filename}.mp3"
        meta_data = {"filetype": "audio/mpeg"}
        remote_file_url = await save_file_to_blob(mp3_path, target_file, meta_data)

        os.unlink(mp3_path)

        remote_file_url = generate_blob_sas_url(target_file)

        return {
            "code": 0,
            "message": _("上传成功"),
            "data": {
                "url": remote_file_url,
                "blob_name": target_file,
                "filename": unique_filename
            }
        }
    except Exception as e:
        return {
            "code": 500,
            "message": f"Upload Failed: {str(e)}",
            "data": None
        }

# 语音表情识别情绪接口
@router.post("/api/v1/screening/speech_analyze")
@require_user_token(False)
async def screening_speech(request: Request, audio_request: AudioAnalysisRequest,  db = Depends(get_db)):

    audio_url = audio_request.audio_url
    language = audio_request.language
    blob_name = audio_request.blob_name
    noise = audio_request.noise
    
    device_id = request.headers.get("DEVICE-ID")
    if not device_id:
        device_id = ""

    user_id = 0
    current_user_info = request.state.user
    current_account_info = request.state.account
    if current_user_info:
        user_id = current_user_info["user_id"]

    is_hidden_report = 0
    try:
        account_service = AccountService()
        account_info = await account_service.get_acount_info_by_device_id(device_id)
        if account_info:
            is_hidden_report = account_info["config"].get("is_hidden_report", 0)
    except Exception as e:
        error_stack = traceback.format_exc()
        logging.error(error_stack)
        logging.error(f"Error processing rppg device data: {str(e)}")

    speech_service = SpeechService(db, audio_request)
    speech_service.device_id = device_id
    result = await speech_service.analyize(audio_url, language, noise)

    if not result:
        return {
            "code": 400,
            "message": _("分析语音情绪失败"),
        }
    
    result_info = result.json()
    data = result_info["data"]
    data["orig_blob_name"] = blob_name
    screening = await speech_service.save_result(data, user_id)

    data['screening_idx'] = screening.idx
    screening_time = screening.created + timedelta(hours=8)
    data['screening_time'] = screening_time.strftime('%Y-%m-%d')

    total_status, new_emotions, positive_emotion, neutral_emotion, negative_emotion, arousal_result, valence_result = speech_service.format_emotions(data["emotions"])
    data["positive_emotion"] = positive_emotion
    data["neutral_emotion"] = neutral_emotion
    data["negative_emotion"] = negative_emotion
    data["arousal_result"] = arousal_result
    data["valence_result"] = valence_result
    data["total_status"] = total_status
    data["emotions"] = new_emotions

    crypt_key = os.getenv("CRYPT_KEY")
    en_screening_id = Crypt.encrypt(str(screening.idx), crypt_key)
    data["en_screening_id"] = en_screening_id

    data["is_hidden_report"] = is_hidden_report

    # result_info = json.dumps(data)

    return_info = {
        "code": 0,
        "message": _("分析语音情绪成功"),
        "data": data
    }

    return return_info

@router.get("/api/v1/screening/get_speech_report/{en_screening_id}")
async def get_speech_report(request: Request, en_screening_id: str):

    if not en_screening_id:
        return APIResponse(code=10083, message=_("获取信息失败"))

    crypt_key = os.getenv("CRYPT_KEY")

    try:
        screening_id = Crypt.decrypt(en_screening_id, crypt_key)
        screening_id = int(screening_id)
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        screening_id = 0
    if not screening_id:
        return APIResponse(code=10085, message=_("获取信息失败"))

    speech_service = SpeechService()

    try:
        speech_info = await speech_service.get_speech_by_id(screening_id)
        if not speech_info:
            return APIResponse(code=10086, message=_("获取信息失败"))

        user_id = speech_info["user_id"]
        if user_id == 'unknown':
            user_id = 0
        user_id = int(user_id)
        
        process_result = speech_info["process_result"]
        process_result['screening_idx'] = speech_info["idx"]
        screening_time = speech_info["created"] + timedelta(hours=8)
        process_result['screening_time'] = screening_time.strftime('%Y-%m-%d')

        total_status, new_emotions, positive_emotion, neutral_emotion, negative_emotion, arousal_result, valence_result = speech_service.format_emotions(process_result["emotions"])
        process_result["positive_emotion"] = positive_emotion
        process_result["neutral_emotion"] = neutral_emotion
        process_result["negative_emotion"] = negative_emotion
        process_result["arousal_result"] = arousal_result
        process_result["valence_result"] = valence_result
        process_result["total_status"] = total_status
        process_result["emotions"] = new_emotions

        return_info = {
            "code": 0,
            "message": "",
            "data": {
                "speech_info": process_result
            }
        }

        return return_info
    
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        logging.error(f"Error processing speech data: {str(e)}")

        return {
            "code": 10087,
            "message": "Invalid request parameters",
            "data": {}
        }


class ListSpeechReportRequest(BaseModel):
    device_id_list: str
    page: int
# 语音报告列表
@router.post("/api/v1/screening/list_speech_report")
async def list_speech_report(request: Request, list_speech_request: ListSpeechReportRequest,  db = Depends(get_db)):
    
    page = list_speech_request.page
    device_id_list = list_speech_request.device_id_list

    speech_service = SpeechService(db)
    device_id_list_str = os.getenv("DEVICE_ID_LIST_JIANXIANG")
    device_id_list = device_id_list_str.split(',')
    
    if page <= 0:
        page = 1
    limit = 20
    start = (page - 1) * limit
    speech_report_list = await speech_service.list_speech_report_by_device_id(device_id_list, start, limit)

    list_info = []
    for speech_report in speech_report_list:
        process_result = speech_report.process_result
        emotions = process_result["emotions"]
        info = {
            "device_id": speech_report.device_id,
            "assessment_time":  speech_report.created.strftime('%Y-%m-%d %H:%M:%S'),
            "happiness": "{:.2%}".format(emotions["happy"]),
            "surprise": "{:.2%}".format(emotions["surprised"]),
            "neutral": "{:.2%}".format(emotions["neutral"]),
            "anger": "{:.2%}".format(emotions["angry"]),
            "disgust": "{:.2%}".format(emotions.get("disgust", 0)),
            "fear": "{:.2%}".format(emotions["fearful"]),
            "sadness": "{:.2%}".format(emotions["sad"]),
        }
        list_info.append(info)

    return_info = {
        "code": 0,
        "message": "获取语音报告列表成功",
        "data": {
            "speech_report_list": list_info
        }
    }

    return return_info

# HRV报告列表
class ListHrvReportRequest(BaseModel):
    device_id_list: str
    page: int
@router.post("/api/v1/screening/list_hrv_report")
async def list_hrv_report(request: Request, list_hrv_request: ListHrvReportRequest,  db = Depends(get_db)):


    page = list_hrv_request.page
    device_id_list = list_hrv_request.device_id_list

    hrv_service = HrvService(db)
    device_id_list_str = os.getenv("DEVICE_ID_LIST_JIANXIANG")
    device_id_list = device_id_list_str.split(',')
    
    if page <= 0:
        page = 1
    limit = 20
    start = (page - 1) * limit
    hrv_report_list = await hrv_service.list_hrv_report_by_device_id(device_id_list, None, None, start, limit)

    list_info = []
    for hrv_report in hrv_report_list:
        process_result = hrv_report.process_result
        raw_data = process_result
        raw_data = raw_data.replace('\\n', '')
        raw_data = raw_data.replace('\\"', '"')
        raw_data = raw_data.strip().strip('"')
        raw_data_dict = json.loads(raw_data)

        pressureInfo = raw_data_dict["pressureInfo"]
        nerveInfo = raw_data_dict["nerveInfo"]

        info = {
            "device_id": hrv_report.device_id,
            "assessment_time":  hrv_report.created.strftime('%Y-%m-%d %H:%M:%S'),
            "physical_stress": pressureInfo["physicalPressure"]["Value"],
            "psychological_stress": pressureInfo["mentalPressure"]["Value"],
            "resilience": pressureInfo["stressResistance"]["Value"],
            "autonomic_balance": nerveInfo["balance"]["Value"],
            "autonomic_activity": nerveInfo["activity"]["Value"],
            "total_score": raw_data_dict.get("total_score", 0),
        }
        list_info.append(info)


    return_info = {
        "code": 0,
        "message": "获取HRV报告列表成功",
        "data": {
            "hrv_report_list": list_info
        }
    }

    return return_info

class ListEmotionReportRequest(BaseModel):
    device_id_list: str
    page: int
# 表情报告列表
@router.post("/api/v1/screening/list_emotion_report")
async def list_emotion_report(request: Request, list_emotion_request: ListEmotionReportRequest,  db = Depends(get_db)):

    page = list_emotion_request.page
    device_id_list = list_emotion_request.device_id_list

    emotion_service = EmotionService(db)
    device_id_list_str = os.getenv("DEVICE_ID_LIST_JIANXIANG")
    device_id_list = device_id_list_str.split(',')

    if page <= 0:
        page = 1
    limit = 20
    start = (page - 1) * limit
    emotion_report_list = await emotion_service.list_emotion_report_by_device_id(device_id_list, start, limit)

    list_info = []
    for emotion_report in emotion_report_list:
        process_result = emotion_report.process_result
        expression_list = process_result["expression_list"]
        valence_arousal = process_result["valence_arousal"]
        emotion_info = {}
        for emotion in expression_list:
            label = emotion["label"]
            emotion_info[label] = emotion["probs"]
        info = {
            "device_id": emotion_report.device_id,
            "assessment_time":  emotion_report.created.strftime('%Y-%m-%d %H:%M:%S'),
            "happiness": "{:.2%}".format(emotion_info["happiness"]),
            "surprise": "{:.2%}".format(emotion_info["surprise"]),
            "neutral": "{:.2%}".format(emotion_info["neutral"]),
            "anger": "{:.2%}".format(emotion_info["anger"]),
            "disgust": "{:.2%}".format(emotion_info.get("disgust", 0)),
            "fear": "{:.2%}".format(emotion_info["fear"]),
            "sadness": "{:.2%}".format(emotion_info["sadness"]),
            "valence": valence_arousal["valence_value"],
            "arousal": valence_arousal["arousal_value"],
        }
        list_info.append(info)

    return_info = {
        "code": 0,
        "message": "获取表情报告列表成功",
        "data": {
            "emotion_report_list": list_info
        }
    }

    return return_info

# 眼动报告列表
class ListEyemovementReportRequest(BaseModel):
    device_id_list: str
    page: int
@router.post("/api/v1/screening/list_eyemovement_report")
async def list_eyemovement_report(request: Request, list_eyemovement_request: ListEyemovementReportRequest,  db = Depends(get_db)):

    page = list_eyemovement_request.page
    device_id_list = list_eyemovement_request.device_id_list

    eyemovement_service = EyemovementService(db)
    device_id_list_str = os.getenv("DEVICE_ID_LIST_JIANXIANG")
    device_id_list = device_id_list_str.split(',')

    if page <= 0:
        page = 1
    limit = 20
    start = (page - 1) * limit
    eyemovement_report_list = await eyemovement_service.list_eyemovement_report_by_device_id(device_id_list, start, limit)

    list_info = []
    for eyemovement_report in eyemovement_report_list:
        report_info = eyemovement_report.report
        if not report_info or report_info == "" or report_info == "{}":
            vertical_off_duration = 0
            horizontal_off_duration = 0
            lissajous_off_duration = 0
            lissajous_initial_phase_speed = 0
            lissajous_continuity = 0
            lissajous_speed = 0
            lissajous_position = 0
            no_interference_fixations_count = 0
            no_interference_fixation_duration = 0
            orientation_reaction_time = 0
            nef_ = 0
            rss_1 = 0
            rss_2 = 0
            rss = 0
            d = 0
        else:
            report_json = json.loads(report_info)
            vertical_off_duration = report_json['details']['part1']['target1']['val']
            horizontal_off_duration = report_json['details']['part1']['target2']['val']
            lissajous_off_duration = report_json['details']['part1']['target3']['val']
            lissajous_initial_phase_speed = report_json['details']['part1']['target4']['val']
            lissajous_continuity = report_json['details']['part1']['target5']['val']
            lissajous_speed = report_json['details']['part1']['target6']['val']
            lissajous_position = report_json['details']['part1']['target7']['val']
            no_interference_fixations_count = report_json['details']['part2']['target1']['val']
            no_interference_fixation_duration = report_json['details']['part2']['target2']['val']
            orientation_reaction_time = report_json['details']['part3']['target1']['val']
            nef_ = report_json['details']['part5']['target1']['val']
            rss_1 = report_json['details']['part5']['target2']['val']
            rss_2 = report_json['details']['part5']['target3']['val']
            rss = report_json['details']['part5']['target4']['val']
            d = report_json['details']['part5']['target5']['val']
        info = {
            "device_id": eyemovement_report.device_id,
            "assessment_time":  eyemovement_report.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            "vertical_off_duration": vertical_off_duration,
            "horizontal_off_duration": horizontal_off_duration,
            "lissajous_off_duration": lissajous_off_duration,
            "lissajous_initial_phase_speed": lissajous_initial_phase_speed,
            "lissajous_continuity": lissajous_continuity,
            "lissajous_speed": lissajous_speed,
            "lissajous_position": lissajous_position,
            "no_interference_fixations_count": no_interference_fixations_count,
            "no_interference_fixation_duration": no_interference_fixation_duration,
            "orientation_reaction_time": orientation_reaction_time,
            "nef_": nef_,
            "rss_1": rss_1,
            "rss_2": rss_2,
            "rss": rss,
            "d": d,
        }
        list_info.append(info)

    return_info = {
        "code": 0,
        "message": "获取眼动报告列表成功",
        "data": {
            "eyemovement_report_list": list_info
        }
    }

    return return_info

class CreateScreeningRequest(BaseModel):
    profileId: Optional[str] = None
    category: str
    deviceId: Optional[str] = None
@router.post("/api/v1/screening/create_em_screening")
@require_user_token(False)
async def create_em_screening(request: Request, body: CreateScreeningRequest):
    
    user_id = 0
    current_user_info = request.state.user
    current_account_info = request.state.account
    if current_user_info:
        user_id = current_user_info["user_id"]
    
    em_service = EyemovementService()
    screening = ScreeningInfo(
        id=str(uuid.uuid4()),
        profile_id=body.profileId,
        user_id=user_id,
        category=body.category,
        device_id=body.deviceId
    )
    new_screening_info = await em_service.new_screening_info(screening)

    return_info = {
        "code": 0,
        "desc": "",
        "data": new_screening_info
    }

    return return_info

class SaveScreeningDetailRequest(BaseModel):
    screeningId: str
    name: str
    version: str
    detail: str
    deviceId: Optional[str] = None
@router.post("/api/v1/screening/save_em_screening_detail")
async def save_em_screening_detail(request: Request, body: SaveScreeningDetailRequest):
    em_service = EyemovementService()
    screening_info = await em_service.get_screening_info(body.screeningId)
    if not screening_info:
        return { "code": -1, "desc": "screening not found", "data": None }
    
    screening_detail = ScreeningDetail(
        id=str(uuid.uuid4()),
        screening_id=screening_info.id,
        category=screening_info.category,
        name=body.name,
        version=body.version,
        detail=body.detail,
        device_id=body.deviceId
    )
    new_screening_detail = await em_service.new_screening_detail(screening_detail)

    return_info = {
        "code": 0,
        "desc": "",
        "data": new_screening_detail
    }

    return return_info

class GetScreeningReportQuery(BaseModel):
    profileId: Optional[str] = None
    screeningId: str
    force: Optional[bool] = False
@router.get("/api/v1/screening/get_em_screening_report")
async def get_em_screening_report(request: Request, body: GetScreeningReportQuery):
    em_service = EyemovementService()

    screening_info = await em_service.get_screening_info(body.screeningId)
    if not screening_info:
        return { "code": -1, "desc": "screening not found", "data": None }
    
    if not screening_info.report or body.force:
        screening_details = await em_service.get_screening_detail_by_screening_info_id(screening_info.id)

        details = em_service.generate_detail(screening_details)
        results = em_service.generate_result(screening_details)

        await em_service.update_screening_report(screening_info.id, json.dumps({
            'reportId': screening_info.id,
            'createTime': str(screening_info.created_at),
            'user': {
                'name': 'test',
                'gender': 0,
                'age': 0,
                'grade': 0
            },
            'summary': {
                'score': em_service.calc_score(details),
                'content': ''
            },
            'details': details,
            'results': results
        }))
    return_info = {
        "code": 0,
        "desc": "",
        "data": screening_info
    }

    return return_info


class HrvWordsRequest(BaseModel):
    device_id: str
@router.post("/api/v1/screening/hrv_words")
async def hrv_words(request: Request, hrv_words_request: HrvWordsRequest,  db = Depends(get_db)):
    device_id = hrv_words_request.device_id
    hrv_word_service = HrvWordService()
    hrv_word_info = await hrv_word_service.get_words(device_id)
    return APIResponse(code=0, message="", data=hrv_word_info)

class VoiceWordsRequest(BaseModel):
    device_id: str
@router.post("/api/v1/screening/voice_words")
async def voice_words(request: Request, voice_words_request: VoiceWordsRequest,  db = Depends(get_db)):
    device_id = voice_words_request.device_id
    voice_word_service = VoiceWordService()
    voice_word_info = await voice_word_service.get_words(device_id)
    return APIResponse(code=0, message="", data=voice_word_info)

