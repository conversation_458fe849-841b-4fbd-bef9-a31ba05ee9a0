from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from datetime import datetime

from app.models.base import Base, TimestampMixin, ModelUpdateMixin

class TrainingPlanResult(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'training_plan_results'

    train_plan_result_id = Column(Integer, primary_key=True)
    category = Column(String(100), default="")
    user_id = Column(Integer, default=0)
    account_id = Column(Integer, default=0)
    config_json = Column(JSON, default=dict)
    result_json = Column(JSON, default=dict)
    report_json = Column(JSON, default=dict)
    start_time = Column(DateTime, default=None)
    end_time = Column(DateTime, default=None)
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<TrainingPlanResult(train_plan_result_id={self.train_plan_result_id}, category={self.category}, user_id={self.user_id})>"
