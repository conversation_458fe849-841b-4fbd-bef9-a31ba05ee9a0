import numpy as np
from torchvision import transforms


class FauHandler:

    def __init__(self) -> None:
        pass

    def expression_cal(self, probs):
        original_probs = probs
        probs = (probs >= 0.5)
        if original_probs[3] >= 0.3:
            probs[3] = True
        if original_probs[6] >= 0.12:
            probs[6] = True
        if original_probs[7] >= 0.3:
            probs[7] = True
        if original_probs[12] >= 0.2:
            probs[12] = True

        return_exp = 'neutral'
        confidence = 0.0

        if probs[2] and probs[3] and probs[5]:
            return_exp = 'Anger'
            confidence = (original_probs[2] + original_probs[3] + original_probs[5]) / 3
        elif (probs[4] and probs[9]) or (probs[4] and probs[11]) or (probs[0] and probs[1] and probs[9]) or (
                probs[0] and probs[1] and probs[11]):
            return_exp = 'Happiness'
            confidence = max(
                (original_probs[4] + original_probs[9]) / 2,
                (original_probs[4] + original_probs[11]) / 2,
                (original_probs[0] + original_probs[1] + original_probs[9]) / 3,
                (original_probs[0] + original_probs[1] + original_probs[11]) / 3
            )
        elif (probs[0] and probs[2] and probs[12]) or (probs[0] and probs[2] and probs[8]) or probs[12]:
            return_exp = 'Sadness'
            confidence = max(
                (original_probs[0] + original_probs[2] + original_probs[12]) / 3,
                (original_probs[0] + original_probs[2] + original_probs[8]) / 3,
                original_probs[12]
            )
        elif (probs[0] and probs[1] and probs[3]) or (probs[0] and probs[1] and probs[22]) or (
                probs[0] and probs[1] and probs[23]):
            return_exp = 'Surprise'
            confidence = max(
                (original_probs[0] + original_probs[1] + original_probs[3]) / 3,
                (original_probs[0] + original_probs[1] + original_probs[22]) / 3,
                (original_probs[0] + original_probs[1] + original_probs[23]) / 3
            )
        elif probs[6] and probs[7]:
            return_exp = 'Disgust'
            confidence = (original_probs[6] + original_probs[7]) / 2
        elif (probs[0] and probs[1] and probs[2]) or (probs[0] and probs[1] and probs[3] and probs[21]) or (
                probs[0] and probs[1] and probs[3] and probs[22]) or (probs[0] and probs[1] and probs[3] and probs[23]):
            return_exp = 'Fear'
            confidence = max(
                (original_probs[0] + original_probs[1] + original_probs[2]) / 3,
                (original_probs[0] + original_probs[1] + original_probs[3] + original_probs[21]) / 4,
                (original_probs[0] + original_probs[1] + original_probs[3] + original_probs[22]) / 4,
                (original_probs[0] + original_probs[1] + original_probs[3] + original_probs[23]) / 4
            )
        else:
            confidence = 1 - max(original_probs)  # 如果是中性表情，置信度为1减去最大的AU概率

        return return_exp, confidence


    def draw_text(self, words, probs):
        import cv2
        height, width = 728, 1024  # 可以根据需要调整大小
        channels = 3  # RGB 图像

        # 创建一个全白图像，RGB值全为255表示白色
        img = np.ones((height, width, channels), dtype=np.uint8) * 255


        AU_index = ['1','2','4','5','6','7','9','10','11','12','13','14','15','16','17','18','19','20','22','23','24','25','26','27',
                    '32','38','39','L1','R1','L2','R2','L4','R4','L6','R6','L10','R10','L12','R12','L14','R14']
        

        AU_names     = ['Inner brow raiser', 'Outer brow raiser', 'Brow lowerer', 'Upper lid raiser', 'Cheek raiser',
                        'Lid tightener', 'Nose wrinkler', 'Upper lip raiser', 'Nasolabial deepener', 'Lip corner puller',
                        'Sharp lip puller', 'Dimpler', 'Lip corner depressor', 'Lower lip depressor', 'Chin raiser',
                        'Lip pucker', 'Tongue show', 'Lip stretcher', 'Lip funneler', 'Lip tightener', 'Lip pressor',
                        'Lips part', 'Jaw drop', 'Mouth stretch', 'Lip bite', 'Nostril dilator', 'Nostril compressor',
                        'Left Inner brow raiser', 'Right Inner brow raiser', 'Left Outer brow raiser', 'Right Outer brow raiser',
                        'Left Brow lowerer', 'Right Brow lowerer', 'Left Cheek raiser', 'Right Cheek raiser',
                        'Left Upper lip raiser', 'Right Upper lip raiser', 'Left Nasolabial deepener', 'Right Nasolabial deepener',
                        'Left Dimpler', 'Right Dimpler']

        # img = cv2.imread(path)
        pos_y = img.shape[0] // 40
        pos_x = img.shape[1] + img.shape[1] // 100
        pos_x_ = img.shape[1] * 3 // 2 - img.shape[1] // 100

        img = cv2.copyMakeBorder(img, 0, 0, 0, img.shape[1], cv2.BORDER_CONSTANT, value=(255, 255, 255))

        for i, item in enumerate(range(21)):
            y = pos_y + (i * img.shape[0] // 22)
            color = (0, 0, 0)
            if float(probs[item]) > 0.5:
                color = (0, 0, 255)
            img = cv2.putText(img, AU_names[i] + ' -- AU' + AU_index[item] + ': {:.2f}'.format(probs[i]), (pos_x, y),
                            cv2.FONT_HERSHEY_SIMPLEX, round(img.shape[1] / 2800, 3), color, 2)

        for i, item in enumerate(range(21, 41)):
            y = pos_y + (i * img.shape[0] // 22)
            color = (0, 0, 0)
            if float(probs[item]) > 0.5:
                color = (0, 0, 255)
            img = cv2.putText(img, AU_names[item] + ' -- AU' + AU_index[item] + ': {:.2f}'.format(probs[item]), (pos_x_, y),
                            cv2.FONT_HERSHEY_SIMPLEX, round(img.shape[1] / 2800, 3), color, 2)
        # print(img.shape)
        img = img[:,img.shape[1]//2:]
        bgr_image = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
        return bgr_image


    def hybrid_prediction_infolist(self, pred, thresh):
        infostr_pred_probs = {'AU1: {:.2f} AU2: {:.2f} AU4: {:.2f} AU5: {:.2f} AU6: {:.2f} AU7: {:.2f} AU9: {:.2f} AU10: {:.2f} AU11: {:.2f} \
        AU12: {:.2f} AU13: {:.2f} AU14: {:.2f} AU15: {:.2f} AU16: {:.2f} AU17: {:.2f} AU18: {:.2f} AU19: {:.2f} AU20: {:.2f} \
        AU22: {:.2f} AU23: {:.2f} AU24: {:.2f} AU25: {:.2f} AU26: {:.2f} AU27: {:.2f} AU32: {:.2f} AU38: {:.2f} AU39: {:.2f}\
        AUL1: {:.2f} AUR1: {:.2f} AUL2: {:.2f} AUR2: {:.2f} AUL4: {:.2f} AUR4: {:.2f} AUL6: {:.2f} AUR6: {:.2f} AUL10: {:.2f} \
        AUR10: {:.2f} AUL12: {:.2f} AUR12: {:.2f} AUL14: {:.2f} AUR14: {:.2f}'.format(*[100.*x for x in pred])}

        AU_name_lists = ['Inner brow raiser', 'Outer brow raiser', 'Brow lowerer', 'Upper lid raiser', 'Cheek raiser',
                        'Lid tightener', 'Nose wrinkler', 'Upper lip raiser', 'Nasolabial deepener', 'Lip corner puller',
                        'Sharp lip puller', 'Dimpler', 'Lip corner depressor', 'Lower lip depressor', 'Chin raiser',
                        'Lip pucker', 'Tongue show', 'Lip stretcher', 'Lip funneler', 'Lip tightener', 'Lip pressor',
                        'Lips part', 'Jaw drop', 'Mouth stretch', 'Lip bite', 'Nostril dilator', 'Nostril compressor',
                        'Left Inner brow raiser', 'Right Inner brow raiser', 'Left Outer brow raiser', 'Right Outer brow raiser',
                        'Left Brow lowerer', 'Right Brow lowerer', 'Left Cheek raiser', 'Right Cheek raiser',
                        'Left Upper lip raiser', 'Right Upper lip raiser', 'Left Nasolabial deepener', 'Right Nasolabial deepener',
                        'Left Dimpler', 'Right Dimpler']

        AU_indexes = np.where(pred >= thresh)[0]
        AU_prediction = [AU_name_lists[i] for i in AU_indexes]
        infostr_au_pred = {*AU_prediction}
        return infostr_pred_probs, infostr_au_pred

    def softmax(self, x):
        # 对每个元素应用指数函数，并减去最大值以避免溢出问题
        e_x = np.exp(x - np.max(x))
        return e_x / e_x.sum()

    def preprocess_image(self, image):
        """
        Preprocess image to match the expected input shape (batch_size, 3, 256, 256)
        
        Args:
            image: Input PIL image
        Returns:
            numpy array of shape (1, 3, 256, 256)
        """
        if image is None:
            raise ValueError("Input image cannot be None")
            
        # Convert to RGB if needed
        img = image.convert('RGB')
        
        # Resize to 256x256 instead of 224x224
        img = img.resize((256, 256))
        
        # Convert to numpy array
        img_array = np.array(img)
        
        # Normalize to float32 between 0-1
        img_array = img_array.astype(np.float32) / 255.0
        
        # Transpose from HWC to CHW format
        img_array = np.transpose(img_array, (2, 0, 1))
        
        # Add batch dimension
        img_array = np.expand_dims(img_array, axis=0)
        
        # Verify the shape
        expected_shape = (1, 3, 256, 256)  # batch_size=1, channels=3, height=256, width=256
        if img_array.shape != expected_shape:
            raise ValueError(f"Invalid input shape. Expected {expected_shape}, got {img_array.shape}")
            
        return img_array


class image_eval(object):
    def __init__(self, img_size=256, crop_size=224):
        self.img_size = img_size
        self.crop_size = crop_size

    def __call__(self, img):
        # if isinstance(img, np.ndarray):
        #     img = Image.fromarray(img)
        normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                         std=[0.229, 0.224, 0.225])
        transform = transforms.Compose([
            transforms.Resize(self.img_size),
            transforms.CenterCrop(self.crop_size),
            transforms.ToTensor(),
            normalize
        ])
        img = transform(img)
        return img

