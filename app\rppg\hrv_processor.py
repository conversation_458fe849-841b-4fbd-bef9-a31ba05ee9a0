import json
import pytz
import logging
import numpy as np
import pandas as pd
import neurokit2 as nk
import plotly.graph_objects as go
from scipy import signal
import math

from plotly.subplots import make_subplots
from scipy import interpolate
from datetime import datetime
from app.rppg.pos_processor import PosProcessor
from app.rppg.digital_filter import DigitalFilter


class HrvProcessor():
    def __init__(self):
        self.digital_filter = DigitalFilter(10)
        self.pos_processor = PosProcessor(11)
        self.set_noraml_ranges()

    def process(self, rppg_data):
        timestamps = []
        ppg_values = []
        for item in rppg_data:
            pos_result = self.pos_processor.process(item[1:4])
            filter_result = self.digital_filter.process(pos_result)

            timestamps.append(item[0])
            ppg_values.append(filter_result)

        np_timestamps = np.array(timestamps)
        np_ppg_values = np.array(ppg_values)
        self.save_hrv_to_csv(np_timestamps, np_ppg_values)

        hrv_metrics = self.analyze_hrv(np_timestamps, np_ppg_values)
        return hrv_metrics

    def save_hrv_to_csv(self, np_timestamps, np_ppg_values):
        data = pd.DataFrame({
            'Timestamps': np_timestamps,
            'PPGResults': np_ppg_values
        })

        beijing_tz = pytz.timezone('Asia/Shanghai')
        current_time = datetime.now(beijing_tz)
        timestamp = current_time.strftime("%Y%m%d-%H%M%S")

        filename = f"data/{timestamp}.csv"
        data.to_csv(filename, index=False)

    def extract_and_visualize_rr_intervals(self, time_intervals, ppg_values):
        # 计算采样率
        sampling_rate = 1000 / np.mean(time_intervals)

        # 清理 PPG 信号
        ppg_clean = nk.ppg_clean(ppg_values, sampling_rate=sampling_rate)

        # 检测 PPG 信号中的峰值
        peaks, info = nk.ppg_peaks(ppg_clean, sampling_rate=sampling_rate)

        # 获取峰值的索引
        peak_indices = np.where(peaks['PPG_Peaks'] == 1)[0]

        # 计算累积时间
        cumulative_time = np.cumsum(time_intervals)

        # 获取峰值时间点
        peak_times = cumulative_time[peak_indices]

        # 计算 RR 间隔（实际上是脉搏间隔）
        rr_intervals = np.diff(peak_times)

        # 创建子图
        fig = make_subplots(rows=2, cols=1,
                            subplot_titles=("PPG Signal with Detected Peaks",
                                            "Distribution of RR Intervals"))

        # 绘制 PPG 信号和峰值
        fig.add_trace(
            go.Scatter(x=cumulative_time / 1000, y=ppg_clean,
                       mode='lines', name='PPG Signal'),
            row=1, col=1
        )
        fig.add_trace(
            go.Scatter(x=peak_times / 1000, y=ppg_clean[peak_indices],
                       mode='markers', name='Peaks',
                       marker=dict(color='red', size=8)),
            row=1, col=1
        )

        # 绘制 RR 间隔直方图
        fig.add_trace(
            go.Histogram(x=rr_intervals, nbinsx=50, name='RR Intervals'),
            row=2, col=1
        )

        # 更新布局
        fig.update_layout(height=800, width=1000, title_text="PPG Analysis")
        fig.update_xaxes(title_text="Time (s)", row=1, col=1)
        fig.update_yaxes(title_text="Amplitude", row=1, col=1)
        fig.update_xaxes(title_text="RR Interval (ms)", row=2, col=1)
        fig.update_yaxes(title_text="Frequency", row=2, col=1)

        # 显示图表
        fig.show()

        return rr_intervals

    def resample_ppg(self, time_intervals, ppg_values, target_fps=30):
        cumulative_time = np.cumsum(time_intervals)
        start_time = cumulative_time[0]
        end_time = cumulative_time[-1]

        # 计算当前采样率
        current_sampling_rate = len(ppg_values) / (end_time - start_time) * 1000
        logging.debug(f"原始数据点数: {len(ppg_values)}")
        logging.debug(f"原始时间范围: {start_time:.2f} ms 到 {end_time:.2f} ms")
        logging.debug(f"当前采样率: {current_sampling_rate:.2f} Hz")

        # 创建新的时间点
        new_time_points = np.arange(start_time, end_time, 1000 / target_fps)

        # 使用立方插值
        f = interpolate.interp1d(cumulative_time, ppg_values, kind='cubic', bounds_error=False,
                                 fill_value="extrapolate")
        new_ppg_values = f(new_time_points)

        logging.debug(f"重采样后数据点数: {len(new_ppg_values)}")
        logging.debug(f"重采样后时间范围: {new_time_points[0]:.2f} ms 到 {new_time_points[-1]:.2f} ms")
        logging.debug(f"新的采样率: {target_fps:.2f} Hz")

        new_timeinterval = np.diff(new_time_points)
        mean_diff = np.mean(new_timeinterval)
        new_timeinterval = np.insert(new_timeinterval, 0, mean_diff)

        return new_timeinterval, new_ppg_values

    def resample_ppg(self, timestamps, ppg_values, target_fps=30):
        start_time = timestamps[0]
        end_time = timestamps[-1]

        # 计算当前采样率
        duration = (end_time - start_time) / 1000  # 转换为秒
        current_sampling_rate = len(ppg_values) / duration
        logging.debug(f"原始数据点数: {len(ppg_values)}")
        logging.debug(f"原始时间范围: {start_time:.2f} ms 到 {end_time:.2f} ms")
        logging.debug(f"当前采样率: {current_sampling_rate:.2f} Hz")

        # 创建新的时间点
        new_timestamps = np.arange(start_time, end_time, 1000 / target_fps)

        # 使用立方插值
        f = interpolate.interp1d(timestamps, ppg_values, kind='cubic', bounds_error=False, fill_value="extrapolate")
        new_ppg_values = f(new_timestamps)

        logging.debug(f"重采样后数据点数: {len(new_ppg_values)}")
        logging.debug(f"重采样后时间范围: {new_timestamps[0]:.2f} ms 到 {new_timestamps[-1]:.2f} ms")
        logging.debug(f"新的采样率: {target_fps:.2f} Hz")

        new_timestamps_diff = np.diff(new_timestamps)
        mean_diff = np.mean(new_timestamps_diff)
        new_timestamps_diff = np.insert(new_timestamps_diff, 0, mean_diff)

        return new_timestamps_diff, new_ppg_values

    def analyze_hrv(self, old_time_intervals, old_ppg_values):
        timestamps = old_time_intervals.cumsum()
        np_timestamps, ppg_values = self.resample_ppg(timestamps, old_ppg_values)

        first_non_zero = np.argmax(ppg_values != 0)
        time_intervals = np_timestamps[first_non_zero:]
        ppg_values = ppg_values[first_non_zero:]

        # debug: show resampled ppg values
        # self.extract_and_visualize_rr_intervals(time_intervals, ppg_values)

        sampling_rate = 1000 / np.mean(time_intervals)
        peaks, info = nk.ppg_peaks(ppg_values, sampling_rate=sampling_rate)
        hrv_result = nk.hrv(peaks, sampling_rate=sampling_rate, show=False)

        '''
        hrv_freq = nk.hrv_frequency(peaks, sampling_rate=sampling_rate, show=False, method="welch")
        freq = hrv_freq["freq"]
        psd = hrv_freq["psd"]

        # 准备发送到前端的数据
        psd_data = {
            "freq": freq.tolist(),
            "psd": psd.tolist(),
            "vlfRange": [0.0033, 0.04],
            "lfRange": [0.04, 0.15],
            "hfRange": [0.15, 0.4]
        }
        '''

        peak_indices = np.where(peaks['PPG_Peaks'] == 1)[0]
        cumulative_time = np.cumsum(time_intervals)
        peak_times = cumulative_time[peak_indices]
        rr_intervals = np.diff(peak_times)

        MIN_RR = 300  # 最小RR间期（300ms）
        MAX_RR = 2000  # 最大RR间期（2000ms）
        filtered_rr_intervals_ms = rr_intervals[(rr_intervals >= MIN_RR) & (rr_intervals <= MAX_RR)]

        hrv_dict = hrv_result.to_dict(orient='records')[0]
        hrv_dict['RRIntervals'] = filtered_rr_intervals_ms.tolist()

        nerve_info = self.calculate_nerve_info(hrv_result)
        pressure_info = self.calculate_pressure_info(hrv_result)
        nerve_info_result, pressure_info_result, total_score = self.post_process(nerve_info, pressure_info)
        hrv_dict['nerveInfo'] = nerve_info_result
        hrv_dict['pressureInfo'] = pressure_info_result
        hrv_dict['total_score'] = total_score

        for key, value in hrv_dict.items():
            if isinstance(value, float) and (np.isnan(value) or np.isinf(value)):
                hrv_dict[key] = None

        hrv_dict["HRV_MeanNN"] = self.standardize_meannn(hrv_dict["HRV_MeanNN"])
        hrv_dict["HRV_SDNN"] = self.standardize_sdnn(hrv_dict["HRV_SDNN"])
        hrv_dict["HRV_RMSSD"] = self.standardize_rmssd(hrv_dict["HRV_RMSSD"])
        hrv_dict["HRV_SDSD"] = self.standardize_sdsd(hrv_dict["HRV_SDSD"])
        hrv_dict["HRV_pNN50"] = self.standardize_pnn50(hrv_dict["HRV_pNN50"])
        hrv_dict["HRV_LF"] = self.standardize_lf(hrv_dict["HRV_LF"])
        hrv_dict["HRV_VLF"] = self.standardize_lf(hrv_dict["HRV_VLF"])
        hrv_dict["HRV_HF"] = self.standardize_hf(hrv_dict["HRV_HF"])
        hrv_dict["HRV_TP"] = self.standardize_tp(hrv_dict["HRV_TP"])
        hrv_dict["HRV_LFHF"] = self.standardize_lf_hf_ratio(hrv_dict["HRV_LFHF"])
        hrv_json = json.dumps(hrv_dict, indent=2, ensure_ascii=False)
        return hrv_json

    def calculate_nerve_info(self, hrv_result):
        lf = hrv_result['HRV_LF'].iloc[0]
        hf = hrv_result['HRV_HF'].iloc[0]
        lf_hf_ratio = hrv_result['HRV_LFHF'].iloc[0]

        info = self.calculate_autonomic_activity(hrv_result)
        balance = int(info['balance_index'])
        activity = int(info['activity_index'])

        balance = max(0, min(balance, 100))
        activity = max(0, min(activity, 100))

        nerve_info = {
            'balance': {
                'Title': _('自主神经平衡'),
                'Value': balance,
                'Min': 0,
                'Max': 100,
                'Description': '',
                "Definition": _("自主神经平衡反映交感神经与副交感神经的协调程度。中等平衡性最好，意味着身体可以灵活切换状态；而过低或过高的平衡性可能导致问题。"),
                "DescDetail": '',
                "Suggestion": [],
            },
            'activity': {
                'Title': _('自主神经活性'),
                'Value': activity,
                'Min': 0,
                'Max': 100,
                'Description': '',
                "Definition": _("反映神经系统对外界刺激的反应能力，活性越高，说明适应能力越强。"),
                "DescDetail": '',
                "Suggestion": [],
            }
        }
        return nerve_info

    def calculate_pressure_info(self, hrv_result):
        sdnn = self.standardize_sdnn(hrv_result['HRV_SDNN'].iloc[0])
        rmssd = self.standardize_rmssd(hrv_result['HRV_RMSSD'].iloc[0])
        lf = self.standardize_lf(hrv_result['HRV_LF'].iloc[0])
        meannn = self.standardize_meannn(hrv_result['HRV_MeanNN'].iloc[0])
        tp = self.standardize_tp(hrv_result['HRV_TP'].iloc[0])
        pnn50 = self.standardize_pnn50(hrv_result['HRV_pNN50'].iloc[0])
        hf = self.standardize_hf(hrv_result['HRV_HF'].iloc[0])
        lf_hf_ratio = self.standardize_lf_hf_ratio(hrv_result['HRV_LFHF'].iloc[0])

        # physical_pressure = int(lf / 3000.0 * 100)
        physical_pressure = self.calculate_physical_stress(meannn, sdnn, tp)
        physical_pressure = int(physical_pressure)
        # mental_pressure = int(100 - (rmssd / 50.0 * 100))
        mental_pressure = self.calculate_psychological_stress(rmssd, pnn50, hf, lf_hf_ratio)
        mental_pressure = int(mental_pressure)
        # stress_resistance = int(sdnn / 100.0 * 100)
        stress_resistance = self.calculate_stress_resistance(sdnn, tp, lf, hf)
        stress_resistance = int(stress_resistance)

        physical_pressure = max(0, min(physical_pressure, 100))
        mental_pressure = max(0, min(mental_pressure, 100))
        stress_resistance = max(0, min(stress_resistance, 100))

        pressure_info = {
            'stressResistance': {
                'Title': _('抗压能力'),
                'Value': stress_resistance,
                'Min': 0,
                'Max': 100,
                'Description': '',
                "Definition": _("指人体对于各种外界压力（包括心理压力和身体压力）的抗衡能力，其指标越高表明人在压力环境下越能维持健康状态。"),
                "DescDetail": '',
                "Suggestion": [],
            },
            'mentalPressure': {
                'Title': _('心理压力'),
                'Value': mental_pressure,
                'Min': 0,
                'Max': 100,
                'Description': '',
                "Definition": _("是指个体对环境要求或挑战的主观感受，压力指数越高，表明焦虑、紧张等负面情绪的可能性越大。"),
                "DescDetail": '',
                "Suggestion": [],
            },
            'physicalPressure': {
                'Title': _('身体压力'),
                'Value': physical_pressure,
                'Min': 0,
                'Max': 100,
                'Description': '',
                "Definition": _("是指个体在面临生理负荷时的身体应激反应。过高的身体压力可能导致疲劳、免疫力下降等问题。"),
                "DescDetail": '',
                "Suggestion": [],
            },
        }
        return pressure_info

    def post_process(self, nerve_info, pressure_info):
        balance = nerve_info['balance']['Value']
        activity = nerve_info['activity']['Value']
        physical_pressure = pressure_info['physicalPressure']['Value']
        mental_pressure = pressure_info['mentalPressure']['Value']
        stress_resistance = pressure_info['stressResistance']['Value']

        # # Adjust thresholds and logic for better consistency
        # if balance > 70 and activity > 50:  # Changed activity threshold
        #     # Good balance and activity, adjust pressures and stress resistance
        #     physical_pressure = max(15, min(physical_pressure, 40))
        #     mental_pressure = max(10, min(mental_pressure, 30))
        #     stress_resistance = min(90, max(stress_resistance, 70))
        # elif balance < 50 and activity < 50:
        #     # Poor balance and activity, adjust pressures and stress resistance
        #     physical_pressure = min(85, max(physical_pressure, 60))
        #     mental_pressure = min(90, max(mental_pressure, 50))
        #     stress_resistance = max(15, min(stress_resistance, 50))
        # else:
        #     # Mixed or normal range, make smaller adjustments
        #     balance = max(40, min(balance, 70))
        #     activity = max(40, min(activity, 70))
        #     physical_pressure = max(40, min(physical_pressure, 60))
        #     mental_pressure = max(30, min(mental_pressure, 50))
        #     stress_resistance = max(50, min(stress_resistance, 70))

        # Update values
        nerve_info['balance']['Value'] = balance
        nerve_info['activity']['Value'] = activity
        pressure_info['stressResistance']['Value'] = stress_resistance
        pressure_info['physicalPressure']['Value'] = physical_pressure
        pressure_info['mentalPressure']['Value'] = mental_pressure

        total_score = self.calculate_total_score(stress_resistance, mental_pressure, physical_pressure, balance, activity)

        # Update descriptions based on new values
        self.update_descriptions(nerve_info, pressure_info)

        return nerve_info, pressure_info, total_score

    def update_descriptions(self, nerve_info, pressure_info):
        balance = nerve_info['balance']['Value']
        activity = nerve_info['activity']['Value']
        physical_pressure = pressure_info['physicalPressure']['Value']
        mental_pressure = pressure_info['mentalPressure']['Value']
        stress_resistance = pressure_info['stressResistance']['Value']

        # 自主神经平衡描述
        if balance > 70:
            nerve_info['balance']['Description'] = _("您的自主神经平衡性过高")
            nerve_info['balance']['DescDetail'] = _("您的交感和副交感神经切换过于频繁，可能导致神经系统过度敏感，容易焦虑、易怒，甚至出现心血管压力。")
            nerve_info['balance']['Suggestion'] = [
                _("减少刺激，避免高强度锻炼（如高强度间歇训练），减少对交感神经的过度激活。"),
                _("放松训练，每天进行10分钟正念冥想或渐进性肌肉放松训练，降低神经系统的高敏感性。"),
                _("饮食调节，减少咖啡因和高糖饮食，防止神经系统过度兴奋。"),
            ]
        elif balance < 34:
            nerve_info['balance']['Description'] = _("您的自主神经平衡性较低")
            nerve_info['balance']['DescDetail'] = _("您的副交感神经较强，交感神经较弱，可能导致精神萎靡、适应压力能力差、容易疲劳。")
            nerve_info['balance']['Suggestion'] = [
                _("增强交感神经，每天进行5分钟冷水浴或晨跑，激发交感神经，提高警觉性。"),
                _("调整作息，每天早起30分钟，增加早晨光照，提高白天的神经活跃度。"),
                _("增加蛋白质摄入，饮食中补充高蛋白食物（如鸡蛋、瘦肉、豆类），有助于增强交感神经功能。"),
            ]
        else:
            nerve_info['balance']['Description'] = _("您的自主神经平衡性良好")
            nerve_info['balance']['DescDetail'] = _("您的交感和副交感神经调节处于理想状态，能在放松和紧张之间自由切换。")
            nerve_info['balance']['Suggestion'] = [
                _("保持稳定的作息，避免熬夜和不规律的饮食习惯。"),
                _("适度运动，每周进行150分钟有氧运动（快走、瑜伽、游泳），保持神经系统的弹性。"),
                _("放松练习，每周安排户外活动，如徒步、园艺，帮助神经系统维持最佳状态。"),
            ]

        # 自主神经活性描述
        if activity > 70:
            nerve_info['activity']['Description'] = _("您的自主神经活性较高")
            nerve_info['activity']['DescDetail'] = _("您的神经系统对外界刺激反应敏锐，但长期高活性可能导致隐性疲劳。")
            nerve_info['activity']['Suggestion'] = [
                _("睡前避免饮用咖啡或浓茶，帮助神经系统放松。"),
                _("每天尝试正念冥想，缓解神经紧张状态。"),
            ]
        elif activity < 41:
            nerve_info['activity']['Description'] = _("您的自主神经活性较低")
            nerve_info['activity']['DescDetail'] = _("您的神经系统对外界刺激反应较弱，可能影响适应能力。")
            nerve_info['activity']['Suggestion'] = [
                _("每天早上用冷水洗脸，刺激神经系统的唤醒反应。"),
                _("每天吃早餐，补充能量，增强身体的神经活跃度。"),
            ]
        else:
            nerve_info['activity']['Description'] = _("您的自主神经活性适中")
            nerve_info['activity']['DescDetail'] = _("您的神经系统对外界刺激反应正常，整体状态平稳。")
            nerve_info['activity']['Suggestion'] = [
                _("每周安排轻松的户外活动，如散步或骑车，提升神经适应性。"),
                _("每工作90分钟休息5分钟，保持神经系统的平衡状态。"),
            ]

        # 身体压力描述
        if physical_pressure < 34:
            pressure_info['physicalPressure']['Description'] = _("您的身体压力较低")
            pressure_info['physicalPressure']['DescDetail'] = _("您的身体状态非常健康，能够很好地应对生活的身体负荷。")
            pressure_info['physicalPressure']['Suggestion'] = [
                _("继续保持规律的运动习惯，比如每天散步10-20分钟。"),
                _("避免长时间久坐，每工作一小时站起来活动5分钟，保持活力。"),
            ]
        elif physical_pressure > 66:
            pressure_info['physicalPressure']['Description'] = _("您的身体压力较高")
            pressure_info['physicalPressure']['DescDetail'] = _("您的身体可能正感到疲劳或不适，这可能是高负荷工作或生活的结果。")
            pressure_info['physicalPressure']['Suggestion'] = [
                _("每天安排15分钟进行拉伸或泡脚，帮助缓解肌肉的紧张感。"),
                _("减少夜间使用手机和电脑，确保充足的睡眠来恢复身体状态。"),
            ]
        else:
            pressure_info['physicalPressure']['Description'] = _("您的身体压力处于正常范围")
            pressure_info['physicalPressure']['DescDetail'] = _("您的身体状态良好，但仍需注意避免压力过度积累。")
            pressure_info['physicalPressure']['Suggestion'] = [
                _("每天多喝水（至少2升），帮助身体代谢压力相关的负担。"),
                _("每天补充优质蛋白（如鱼肉、豆类）和新鲜蔬菜，增强身体对抗压力的能力。"),
            ]

        # 心理压力描述
        if mental_pressure < 51:
            pressure_info['mentalPressure']['Description'] = _("您的心理压力较低")
            pressure_info['mentalPressure']['DescDetail'] = _("您的心理状态非常健康，情绪平稳且积极向上，能够很好地应对日常生活中的挑战。")
            pressure_info['mentalPressure']['Suggestion'] = [
                 _("每天做让自己开心的事情，比如听音乐或阅读，维持良好的情绪状态。"),
                 _("参与有意义的社交活动，比如与朋友共度时光，增加幸福感。"),
            ]
        elif mental_pressure > 70:
            pressure_info['mentalPressure']['Description'] = _("您的心理压力较高")
            pressure_info['mentalPressure']['DescDetail'] = _("您可能正经历较大的心理压力，表现为焦虑、疲惫或情绪波动明显。")
            pressure_info['mentalPressure']['Suggestion'] = [
                _("确定主要的压力来源，尝试分解问题，并逐步解决。"),
                _("每周安排一次面对面的沟通，与家人或朋友倾诉，释放内心压力。"),
            ]
        else:
            pressure_info['mentalPressure']['Description'] = _("您的心理压力偏高")
            pressure_info['mentalPressure']['DescDetail'] = _("您可能正感到一定的焦虑或紧张，可能是由于工作或生活压力所致。")
            pressure_info['mentalPressure']['Suggestion'] = [
                _("将焦虑的事情写下来并逐一解决，帮助理清思路，缓解紧张。"),
                _("每天抽出10分钟练习深呼吸或冥想，平复内心的情绪波动。"),
            ]

        # 抗压能力描述
        if stress_resistance > 70:
            pressure_info['stressResistance']['Description'] = _("您的抗压能力非常出色！")
            pressure_info['stressResistance']['DescDetail'] = _("您拥有优秀的压力调节能力，但需注意长期高压可能导致隐性疲劳。")
            pressure_info['stressResistance']['Suggestion'] = [
                _("每天留出15分钟“无干扰时间”，关闭电子设备，让自己彻底放松。"),
                _("避免过多摄入高咖啡因饮品，以减轻神经系统的负担。"),
            ]
        elif stress_resistance < 51:
            pressure_info['stressResistance']['Description'] = _("您的抗压能力较弱")
            pressure_info['stressResistance']['DescDetail'] = _("当前您的身体对压力的适应能力较弱，可能表现为容易疲劳、紧张或注意力不集中。")
            pressure_info['stressResistance']['Suggestion'] = [
                _("每天进行至少30分钟的中低强度运动，比如快走或瑜伽，帮助增强身体的抗压能力。"),
                _("制定固定的作息时间，确保每天至少7小时的高质量睡眠，帮助身体恢复平衡。"),
            ]
        else:
            pressure_info['stressResistance']['Description'] = _("您的抗压能力处于平衡状态")
            pressure_info['stressResistance']['DescDetail'] = _("您的身体能够较好地应对日常压力，但长期积累可能影响健康。")
            pressure_info['stressResistance']['Suggestion'] = [
                _("每天花10分钟梳理工作和生活的待办事项，避免因为无序导致额外压力。"),
                _("每周至少一次进行户外运动，如徒步或跑步，释放长期积累的压力。"),
            ]

    def set_noraml_ranges(self):
        self.normal_ranges = {
            'meanNN': (750, 950),  # ms
            'sdnn': (30, 100),     # ms
            'rmssd': (20, 70),     # ms
            'sdsd': (20, 70),      # ms
            'pnn50': (10, 50),     # %
            'tp': (1000, 4000),    # ms²
            'lf': (300, 1500),     # ms²
            'hf': (300, 1500),     # ms²
            'lf_hf': (0.5, 2.5)    # ratio
        }

    def standardize_sdnn(self, sdnn):
        # 所有NN间期的标准差，反映总体HRV
        # 正常范围：
        # 年轻人：30-60ms
        # 中年人：20-50ms
        # 老年人：15-40ms
        current_max_sdnn = 2500
        current_scale_sdnn = 2000
        if sdnn > current_max_sdnn:
            sdnn = current_max_sdnn
        new_sdnn = sdnn / current_scale_sdnn * 60
        return new_sdnn
    def standardize_rmssd(self, rmssd):
        # 相邻NN间期差值的均方根
        # 正常范围：
        # 年轻人：25-45ms
        # 中年人：20-35ms
        # 老年人：15-25ms
        current_max_rmssd = 4000
        current_scale_rmssd = 3000
        if rmssd > current_max_rmssd:
            rmssd = current_max_rmssd
        new_rmssd = rmssd / current_scale_rmssd * 45
        return new_rmssd
    def standardize_sdsd(self, sdsd):
        # 相邻NN间期差值的标准差
        # 正常范围：
        # 年轻人：25-45ms
        # 中年人：20-35ms
        # 老年人：15-25ms
        current_max_sdsd = 4000
        current_scale_sdsd = 3000
        if sdsd > current_max_sdsd:
            sdsd = current_max_sdsd
        new_sdsd = sdsd / current_scale_sdsd * 45
        return new_sdsd
    def standardize_meannn(self, meannn):
        # 所有相邻正常心跳(NN间期)的平均值，以毫秒(ms)为单位
        # 正常范围：成年人约750-850ms，但会随年龄增长而略有变化
        # 年轻人(18-25岁)：750-850ms
        # 中年人(26-55岁)：700-800ms
        # 老年人(>55岁)：650-750ms
        current_max_meannn = 4000
        current_scale_meannn = 3000
        if meannn > current_max_meannn:
            meannn = current_max_meannn
        new_meannn = meannn / current_scale_meannn * 850
        return new_meannn
    def standardize_pnn50(self, pnn50):
        # 相邻NN间期差值大于50ms的百分比
        # 正常范围：
        # 年轻人：10-30%
        # 中年人：5-20%
        # 老年人：2-10%
        current_max_pnn50 = 100
        current_scale_pnn50 = 90
        if pnn50 > current_max_pnn50:
            pnn50 = current_max_pnn50
        new_pnn50 = pnn50 / current_scale_pnn50 * 30
        return new_pnn50
    def standardize_lf(self, lf):
        # 反映交感和副交感神经的共同作用，但主要反映交感神经活动
        # 正常范围（单位：ms²）：
        # 年轻人(18-25岁)：500-2500
        # 中年人(26-55岁)：300-2000
        # 老年人(>55岁)：200-1500
        if not lf:
            lf = 0.0
        if math.isnan(lf):
            lf = 0.0
        current_max_lf = 0.1
        current_scale_lf = 0.09
        if lf > current_max_lf:
            lf = current_max_lf
        new_lf = lf / current_scale_lf * 2500
        return new_lf
    def standardize_hf(self, hf):
        # 主要反映副交感神经活动
        # 正常范围（单位：ms²）：
        # 年轻人：500-2500
        # 中年人：300-1800
        # 老年人：200-1000
        if not hf:
            hf = 0.0
        if math.isnan(hf):
            hf = 0.0
        current_max_hf = 0.1
        current_scale_hf = 0.09
        if hf > current_max_hf:
            hf = current_max_hf
        new_hf = hf / current_scale_hf * 2500
        return new_hf
    def standardize_lf_hf_ratio(self, lf_hf_ratio):
        # 反映交感-副交感神经平衡状态
        # 正常范围：
        # 年轻人：1.5-2.0
        # 中年人：2.0-2.5
        # 老年人：2.5-3.0
        if not lf_hf_ratio:
            lf_hf_ratio = 0.0
        if math.isnan(lf_hf_ratio):
            lf_hf_ratio = 0.0
        current_max_ratio = 4
        current_scale_ratio = 3
        if lf_hf_ratio > current_max_ratio:
            lf_hf_ratio = current_max_ratio
        new_lf_hf_ratio = lf_hf_ratio / current_scale_ratio * 3
        return new_lf_hf_ratio
    def standardize_tp(self, tp):
        # 反映整体的心率变异性水平
        # 正常范围（单位：ms²）：
        # 年轻人(18-25岁)：3000-8000
        # 中年人(26-55岁)：2000-6000
        # 老年人(>55岁)：1000-4000
        current_max_tp = 0.2
        current_scale_tp = 0.18
        if tp > current_max_tp:
            tp = current_max_tp
        new_tp = tp / current_scale_tp * 6000
        return new_tp

    # 计算自主神经活性和平衡性分值
    def calculate_autonomic_activity(self, hrv_result):

        sdnn = self.standardize_sdnn(hrv_result['HRV_SDNN'].iloc[0])
        rmssd = self.standardize_rmssd(hrv_result['HRV_RMSSD'].iloc[0])
        lf = self.standardize_lf(hrv_result['HRV_LF'].iloc[0])
        hf = self.standardize_hf(hrv_result['HRV_HF'].iloc[0])
        lf_hf_ratio = self.standardize_lf_hf_ratio(hrv_result['HRV_LFHF'].iloc[0])
        hf_nu = hf / (lf + hf) * 100

        # 标准化各项指标
        sdnn_score = min(sdnn / 100, 1.0)
        rmssd_score = min(rmssd / 50, 1.0)
        lf_hf_score = min(lf_hf_ratio / 3, 1.0)
        hf_nu_score = hf_nu / 100
        
        # 计算交感神经活性
        sympathetic_activity = (
            0.4 * (1 - hf_nu_score) +  # 高频功率的反向指标
            0.4 * lf_hf_score +        # LF/HF比值
            0.2 * (1 - rmssd_score)    # RMSSD的反向指标
        ) * 100
        
        # 计算副交感神经活性
        parasympathetic_activity = (
            0.4 * hf_nu_score +     # 高频功率
            0.4 * rmssd_score +     # RMSSD
            0.2 * sdnn_score        # SDNN
        ) * 100

        balance_index = sympathetic_activity / (parasympathetic_activity + sympathetic_activity) * 100
        activity_index = math.sqrt(math.pow(parasympathetic_activity, 2) + math.pow(sympathetic_activity, 2))

        return {
            'balance_index': balance_index,
            'activity_index': activity_index
        }

    def calculate_physical_stress(self, meanNN, sdnn, tp):
        """计算身体压力指数 (0-100)
        较低的meanNN、sdnn和tp通常表示较高的身体压力
        """
        # 归一化处理
        meanNN_score = self._normalize_reverse(meanNN, *self.normal_ranges['meanNN'])
        sdnn_score = self._normalize_reverse(sdnn, *self.normal_ranges['sdnn'])
        tp_score = self._normalize_reverse(tp, *self.normal_ranges['tp'])
        
        # 加权计算
        weights = [0.4, 0.3, 0.3]  # meanNN权重较大，因为它直接反映自主神经系统活动
        physical_stress = np.average([meanNN_score, sdnn_score, tp_score], weights=weights)
        
        return min(100, max(0, physical_stress * 100))
    
    def calculate_psychological_stress(self, rmssd, pnn50, hf, lf_hf):
        """计算心理压力指数 (0-100)
        较低的rmssd、pnn50、hf和较高的lf/hf比值通常表示较高的心理压力
        """
        # 归一化处理
        rmssd_score = self._normalize_reverse(rmssd, *self.normal_ranges['rmssd'])
        pnn50_score = self._normalize_reverse(pnn50, *self.normal_ranges['pnn50'])
        hf_score = self._normalize_reverse(hf, *self.normal_ranges['hf'])
        lf_hf_score = self._normalize(lf_hf, *self.normal_ranges['lf_hf'])
        
        # 加权计算
        weights = [0.3, 0.2, 0.3, 0.2]  # rmssd和hf权重较大，因为它们更直接反映副交感神经活动
        psychological_stress = np.average([rmssd_score, pnn50_score, hf_score, lf_hf_score], weights=weights)
        
        return min(100, max(0, psychological_stress * 100))
    
    def calculate_stress_resistance(self, sdnn, tp, lf, hf):
        """计算抗压能力指数 (0-100)
        较高的sdnn、tp、lf和hf通常表示较强的抗压能力
        """
        # 归一化处理
        sdnn_score = self._normalize(sdnn, *self.normal_ranges['sdnn'])
        tp_score = self._normalize(tp, *self.normal_ranges['tp'])
        lf_score = self._normalize(lf, *self.normal_ranges['lf'])
        hf_score = self._normalize(hf, *self.normal_ranges['hf'])
        
        # 加权计算
        weights = [0.3, 0.3, 0.2, 0.2]  # sdnn和tp权重较大，因为它们反映整体变异性
        resistance = np.average([sdnn_score, tp_score, lf_score, hf_score], weights=weights)
        
        return min(100, max(0, resistance * 100))
    
    def _normalize(self, value, min_normal, max_normal):
        """将值标准化到0-1范围，值越高分数越高"""
        if value <= min_normal:
            return max(0, value / min_normal)
        elif value >= max_normal:
            return min(1, 1 + (value - max_normal) / max_normal)
        else:
            return value / max_normal
    
    def _normalize_reverse(self, value, min_normal, max_normal):
        """将值标准化到0-1范围，值越高分数越低"""
        return 1 - self._normalize(value, min_normal, max_normal)
    
    def calculate_total_score(self, resilience, psychological_stress, physical_stress, autonomic_balance, autonomic_activity):
        """计算整体压力指数 (0-100)
        通过resilience, psychological_stress, physical_stress, autonomic_balance, autonomic_activity进行计算
        """
        # 数据调整
        resilience = 100 - resilience
        autonomic_balance = abs(autonomic_balance - 50) * 2

        # 加权计算
        weights = [0.2, 0.3, 0.3, 0.1, 0.1]
        total_score = np.average([resilience, psychological_stress, physical_stress, autonomic_balance, autonomic_activity], weights=weights)
        total_score = int(total_score)

        return min(100, max(0, total_score))
    