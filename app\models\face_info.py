from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from typing import Any, Dict, Optional
from app.models.base import Base, TimestampMixin, ModelUpdateMixin

class FaceInfo(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'face_infos'

    face_id = Column(Integer, primary_key=True)
    outer_id = Column(String(100), default="")
    face_token = Column(String(200), default="")
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<FaceInfo(face_id={self.face_id}, outer_id={self.outer_id}, face_token={self.face_token})>"
