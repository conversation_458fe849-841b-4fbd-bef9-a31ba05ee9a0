import asyncio
import dotenv
from app.services.faceplusplus_servcie import FacePlusPlusService

dotenv.load_dotenv()

class FaceppTools:
    
    def __init__(self) -> None:
        pass

    async def create_faceset(self, outer_id):
        facepp_service = FacePlusPlusService()
        await facepp_service.create_faceset(outer_id)

async def main():
    tools = FaceppTools()
    outer_id = "face_dataset_2"
    await tools.create_faceset(outer_id)

if __name__ == "__main__":
    asyncio.run(main())