from sqlalchemy.ext.declarative import as_declarative
from sqlalchemy import Column, DateTime
from datetime import datetime
from typing import Any, Dict
from copy import deepcopy
from sqlalchemy import inspect, JSON


@as_declarative()
class Base:
    id: int
    __name__: str


class TimestampMixin:
    created = Column(DateTime, default=datetime.utcnow)
    updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ModelUpdateMixin:
    """
    Model mixin class that provides automatic update functionality for SQLAlchemy models
    """
    
    def _deep_merge(self, source: Dict, destination: Dict) -> Dict:
        """
        递归合并两个字典，用于 JSON 字段的深度更新
        
        Args:
            source: 源字典（新数据）
            destination: 目标字典（原有数据）
            
        Returns:
            合并后的字典
        """
        for key, value in source.items():
            if key in destination and isinstance(destination[key], dict) and isinstance(value, dict):
                # 如果两边都是字典，递归合并
                self._deep_merge(value, destination[key])
            else:
                # 否则直接更新值
                destination[key] = deepcopy(value)
        return destination

    async def update(self, data: Dict[str, Any], session) -> None:
        """
        自动更新模型的字段，包括普通字段和 JSON 类型字段
        对于 JSON 字段会进行递归的深层合并更新
        
        Args:
            data: 要更新的字段数据字典
            session: SQLAlchemy session 对象
            
        Raises:
            Exception: 更新失败时抛出异常
        """
        try:
            # 获取模型的所有字段信息
            mapper = inspect(self.__class__)
            columns = {c.key: c for c in mapper.columns}
            
            for field_name, new_value in data.items():
                # 检查字段是否存在于模型中
                if field_name not in columns:
                    continue
                
                column = columns[field_name]
                current_value = getattr(self, field_name)
                
                # 处理 JSON 类型字段
                if isinstance(column.type, JSON):
                    if current_value is None:
                        current_value = {}
                    
                    if isinstance(new_value, dict):
                        # 深度合并 JSON 数据
                        merged_value = deepcopy(current_value)
                        self._deep_merge(new_value, merged_value)
                        setattr(self, field_name, merged_value)
                    else:
                        # 如果新值不是字典，直接替换
                        setattr(self, field_name, new_value)
                else:
                    # 处理普通字段
                    setattr(self, field_name, new_value)
            
            await session.commit()
            
        except Exception as e:
            await session.rollback()
            raise Exception(f"更新字段失败: {str(e)}")

    def get_field(self, field_name: str, *keys) -> Any:
        """
        获取字段值，支持获取嵌套的 JSON 字段值
        
        Args:
            field_name: 字段名
            *keys: JSON 字段的键路径
            
        Returns:
            字段值
            
        Example:
            user.get_field('extra_json', 'address', 'city')
            user.get_field('name')
        """
        value = getattr(self, field_name, None)
        
        if not keys or value is None:
            return value
            
        # 遍历 JSON 路径
        current = value
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
                
        return current
