import json
from fastapi import Depends, Request, Response, UploadFile, File, Form
from fastapi import APIRouter
import os
from datetime import datetime
import aiofiles
from pydantic import BaseModel, Field
import traceback
import time
from typing import Any, Dict

from app.schemas.base import APIResponse
from app.services.upload_record_video_service import UploadRecordVideoService
from app.services.upload_record_audio_service import UploadRecordAudioService
from app.core.redis import AsyncRedisClient
from app.core.logger import main_logger
from app.utils.crypt import Crypt
from app.utils.blob import save_file_to_blob, generate_blob_sas_url
from app.core.celery_app import celery_app
from app.tasks.upload_video_task import task_video_upload_blob

router = APIRouter()


# 实时视频上传处理 初始化接口
@router.post("/video/record/init")
async def upload_video_stream_init(request: Request):
    # 创建录制会话目录
    upload_service = UploadRecordVideoService()
    user_id = 0 # todo
    new_record = await upload_service.insert(user_id)
    record_id = new_record["record_id"]
    # session_dir = f"tmp/upload_records/{record_id}"
    # os.makedirs(session_dir, exist_ok=True)

    return_info = {
        "code": 0,
        "message": "记录开始",
        "data": {
            "record_id": record_id
        }
    }

    return return_info


# 实时视频上传处理 持续上传
@router.post("/video/record/uploading")
async def upload_video_stream_chunk(
    request: Request,
    record_id : int = Form(...),
    sequence : int = Form(...),
    chunk: UploadFile = File(...)
):
    
    record_service = UploadRecordVideoService()
    record_info = await record_service.get_by_record_id(record_id)
    if not record_info:
        return APIResponse(
            code=10083, message="未找到记录", data={}
        )
    
    session_dir = f"{record_service.tmp_dir}/{record_id}"
    os.makedirs(session_dir, exist_ok=True)
    os.chmod(session_dir, 0o777)
    chunk_path = f"{session_dir}/chunk_{sequence:06d}.webm"

    redis_client = AsyncRedisClient()
    
    try:
        async with aiofiles.open(chunk_path, 'wb') as f:
            while chunk_content := await chunk.read(8192):
                await f.write(chunk_content)

        redis_key = "upload_record_video_{}".format(record_id)
        await redis_client.command("lpush", redis_key, sequence)
        await redis_client.command("expire", redis_key, 86400)

        return APIResponse(
            code=0, message="成功上传", data={"sequence": sequence, "record_id": record_id}
        )
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        return APIResponse(
            code=10081, message="上传失败", data={"sequence": sequence, "record_id": record_id}
        )

class UploadFinishRequest(BaseModel):
    record_id: int
    total_chunks: int
    data: Dict[str, Any]
@router.post("/video/record/finish")
async def upload_video_stream_finish(request: Request, upload_finish_request: UploadFinishRequest):
    record_id = upload_finish_request.record_id
    total_chunks = upload_finish_request.total_chunks
    data = upload_finish_request.data
    
    redis_client = AsyncRedisClient()
    record_service = UploadRecordVideoService()
    
    try:

        # 验证所有分片是否上传完成
        record_len = await redis_client.command("llen", "upload_record_video_{}".format(record_id))
        print(record_len)
        if int(record_len) + 1 < total_chunks:
            return APIResponse(
                code=10085, message="未全部上传完成", data={"record_id": record_id}
            )

        record_info = await record_service.get_by_record_id(record_id)
        if not record_info:
            return APIResponse(code=10080, message="record is error")
        
        # 合并视频分片
        output_file = await record_service.merge_video_chunks(record_id)
        
        # 上传存储处理，并放到音频记录表
        date_dir = datetime.now().strftime("%Y%m%d")
        target_file = f"upload_video/{date_dir}/{record_id}.webm"

        # 异步任务，处理视频上传到云，并处理本地文件和更新数据
        task_video_upload_blob.apply_async(args=[record_id, output_file, target_file, "video/webm"], countdown=5)

        type_id = data.get("type_id", "")
        type = data.get("type", "")
        if type_id:
            crypt_key = os.getenv("CRYPT_KEY")
            type_id = Crypt.decrypt(type_id, crypt_key)
            data["type_id"] = type_id
        # if remote_file_url:
        #     data["blob_url"] = target_file
            data["blob_url"] = ""

        update_data = {
            "type": type,
            "type_id": type_id,
            "extra_json": data,
        }

        await record_service.update_record_by_id(record_id, update_data)
        return APIResponse(
            code=0, message="更新成功", data={"record_id": record_id}
        )
    
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        return APIResponse(
            code=10086, message="更新失败", data={"record_id": record_id}
        )


@router.post("/audio/record/init")
async def upload_audio_stream_init(request: Request):
    # 创建录制会话目录
    upload_service = UploadRecordAudioService()
    user_id = 0 # todo
    new_record = await upload_service.insert(user_id)
    record_id = new_record["record_id"]

    return_info = {
        "code": 0,
        "message": "记录开始",
        "data": {
            "record_id": record_id
        }
    }

    return return_info


# 实时音频上传处理 持续上传
@router.post("/audio/record/uploading")
async def upload_video_stream_chunk(
    request: Request,
    record_id : int = Form(...),
    sequence : int = Form(...),
    chunk: UploadFile = File(...)
):

    record_service = UploadRecordAudioService()
    record_info = await record_service.get_by_record_id(record_id)
    if not record_info:
        return APIResponse(
            code=10083, message="未找到记录", data={}
        )

    session_dir = f"{record_service.tmp_dir}/{record_id}"
    os.makedirs(session_dir, exist_ok=True)
    os.chmod(session_dir, 0o777)
    chunk_path = f"{session_dir}/chunk_{sequence:06d}.webm"

    redis_client = AsyncRedisClient()

    try:
        async with aiofiles.open(chunk_path, 'wb') as f:
            while chunk_content := await chunk.read(8192):
                await f.write(chunk_content)

        redis_key = "upload_record_audio_{}".format(record_id)
        await redis_client.command("lpush", redis_key, sequence)
        await redis_client.command("expire", redis_key, 86400)

        return APIResponse(
            code=0, message="成功上传", data={"sequence": sequence, "record_id": record_id}
        )
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        return APIResponse(
            code=10081, message="上传失败", data={"sequence": sequence, "record_id": record_id}
        )

# 实时音频上传处理 完成上传
class UploadAudioFinishRequest(BaseModel):
    record_id: int
    total_chunks: int
    data: Dict[str, Any]
@router.post("/audio/record/finish")
async def upload_audio_stream_finish(request: Request, upload_finish_request: UploadAudioFinishRequest):
    record_id = upload_finish_request.record_id
    total_chunks = upload_finish_request.total_chunks
    data = upload_finish_request.data

    redis_client = AsyncRedisClient()
    record_service = UploadRecordAudioService()

    try:
        # 验证所有分片是否上传完成
        record_len = await redis_client.command("llen", "upload_record_audio_{}".format(record_id))
        if int(record_len) + 1 < total_chunks:
            return APIResponse(
                code=10085, message="未全部上传完成", data={"record_id": record_id}
            )

        record_info = await record_service.get_by_record_id(record_id)
        if not record_info:
            return APIResponse(code=10080, message="record is error")
        

        # 合并音频分片
        output_file = await record_service.merge_audio_chunks(record_id)

        # 上传存储处理，并放到音频记录表
        date_dir = datetime.now().strftime("%Y%m%d")
        target_file = f"upload_audio/{date_dir}/{record_id}.mp3"
        meta_data = {"filetype": "audio/mpeg"}
        remote_file_url = await save_file_to_blob(output_file, target_file, meta_data)
        os.unlink(output_file)
        # remote_file_url = generate_blob_sas_url(target_file)

        # 清理临时文件
        await record_service.cleanup_chunks(record_id)

        type_id = data.get("type_id", "")
        type = data.get("type", "")
        if type_id:
            crypt_key = os.getenv("CRYPT_KEY")
            type_id = Crypt.decrypt(type_id, crypt_key)
            data["type_id"] = type_id
        if remote_file_url:
            data["blob_url"] = target_file
            
        update_data = {
            "type": type,
            "type_id": type_id,
            "extra_json": data,
        }
        await record_service.update_record_by_id(record_id, update_data)

        return APIResponse(
            code=0, message="成功上传", data={"record_id": record_id}
        )

    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        return APIResponse(
            code=10086, message="上传失败", data={"record_id": record_id}
        )
