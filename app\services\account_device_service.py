from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.account_device import AccountDevice


class AccountDeviceService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_account_device(self, new_account_device):
        try:
            async with self.db_session() as session:
                session.add(new_account_device)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_account_device: {str(e)}")
            raise

        return new_account_device
    

    async def get_list_by_account_id(self, account_id):
        try:
            async with self.db_session() as session:
                conditions = [
                    AccountDevice.account_id == account_id,
                    AccountDevice.status.in_([1])
                ]
                stmt = select(AccountDevice).where(and_(*conditions))
                result = await session.execute(stmt)
                result_info = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_list_by_account_id: {str(e)}")
            raise

        return result_info

    async def get_info_by_device_id(self, device_id):
        try:
            async with self.db_session() as session:
                conditions = [
                    AccountDevice.device_id == device_id,
                    AccountDevice.status.in_([1])
                ]
                stmt = select(AccountDevice).where(and_(*conditions))
                result = await session.execute(stmt)
                result_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_info_by_device_id: {str(e)}")
            raise

        return result_info