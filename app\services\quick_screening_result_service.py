from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.quick_screening_result import QuickScreeningResult

class QuickScreeningResultService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_quick_screening_result(self, new_quick_screening_result):
        try:
            async with self.db_session() as session:
                session.add(new_quick_screening_result)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_quick_screening_result: {str(e)}")
            raise

        return new_quick_screening_result

    async def get_quick_screening_result_by_id(self, qs_result_id):
        try:
            async with self.db_session() as session:
                stmt = select(QuickScreeningResult).where(QuickScreeningResult.qs_result_id == qs_result_id)
                result = await session.execute(stmt)
                qs_result_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_quick_screening_result_by_id: {str(e)}")
            raise

        return qs_result_info

    async def update_result_by_id(self, qs_result_id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(QuickScreeningResult).where(QuickScreeningResult.qs_result_id == qs_result_id)
                result = await session.execute(stmt)
                result_info = result.scalars().first()

                if not result_info:
                    return False

                await result_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_result_by_id: {str(e)}")
            raise

        return True