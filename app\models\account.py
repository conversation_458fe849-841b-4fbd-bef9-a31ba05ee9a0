from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from typing import Any, Dict, Optional
from app.models.base import Base, TimestampMixin, ModelUpdateMixin


class Account(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'accounts'

    account_id = Column(Integer, primary_key=True)
    name = Column(String(100), default="")
    password = Column(String(32), default="")
    salt = Column(String(6), default="")
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<Account(account_id={self.account_id}, name={self.name})>"
