from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta
import logging
from sqlalchemy.exc import SQLAlchemyError
import httpx
import os
import jwt
from dateutil.relativedelta import relativedelta

from app.models.user import User
from app.services.faceplusplus_servcie import FacePlusPlusService
from app.utils.common import CommonUtils
from app.core.database import get_db
from app.core.logger import main_logger


class UserService:
    def __init__(self, request = None):
        self.db_session = get_db
        self.request = request
        self.account_id = 0

    def gen_token(self, user_id, user_password, account_id):
        user_token_secret = os.getenv("USER_TOKEN_SECRET")

        payload = {
            'user_id': user_id,
            'account_id': account_id,
            'user_password': user_password,
            'exp': datetime.utcnow() + timedelta(hours=8760),  # 过期时间
            'iat': datetime.utcnow()  # 发行时间
        }

        token = jwt.encode(payload, user_token_secret, algorithm="HS256")

        return token

    async def verify_token(self, token):
        user_token_secret = os.getenv("USER_TOKEN_SECRET")
        try:
            decoded_payload = jwt.decode(token, user_token_secret, algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            print("Token has expired")
            raise
            return False
        except jwt.PyJWTError:
            print("Invalid token")
            raise
            return False
        
        if not decoded_payload:
            return False

        user_id = decoded_payload["user_id"]
        account_id = decoded_payload["account_id"]
        user_password = decoded_payload['user_password']

        user_info = await self.get_user_by_user_id(user_id)
        if not user_info:
            raise Exception({"code": "10011", "message": _("登录状态失效，请重新登录")})
            return False
        if user_info["password"] != user_password:
            raise Exception({"code": "10011", "message": _("登录状态失效，请重新登录")})
            return False

        account_info = {"account_id": account_id}

        return user_info, account_info

    async def pw_login(self, username, password):

        async with self.db_session() as session:
            stmt = select(User).where(User.username == username)
            result = await session.execute(stmt)
            user_info = result.scalars().first()

        if not user_info:
            return {"code": 10301, "msg": _("用户名或密码不正确")}
        
        user_info = user_info.__dict__
        password_hash = CommonUtils.generate_md5_hash(password + user_info["salt"])
        if password_hash != user_info["password"]:
            return {"code": 10301, "msg": _("用户名或密码不正确")}
        
        token = await self.login(user_info["user_id"], 'pwlogin')
        data = {
            "token": token,
            "info": user_info,
        }

        main_logger.info({"what": "pw_login", "user_id": user_info["user_id"]})

        return {"code": 0, "msg": "", "data": data}

    async def facepp_login(self, image_base64_list):
        
        # 找到图片质量最好的图片,保证图片质量
        best_image_index, best_result = await self.facepp_best_detect(image_base64_list)
        # best_image_base64 = image_base64_list[best_image_index]

        # 人脸搜索图片
        search_result = await self.facepp_search(best_result["faces"][0]["face_token"], image_base64_list["image1"])
        
        if not search_result:
            raise Exception("Search Error")
            # 注册
            user_id, token = await self.facepp_register_user(best_result)
        else:
            # 登录
            user_id = search_result.get("user_id")
            token = await self.login(user_id, 'facepp')

        main_logger.info({"what": "facepp_login", "user_id": user_id})

        return user_id, token
            
    async def facepp_register_user(self, best_result):

        facepp_service = FacePlusPlusService()
        face_token = best_result["faces"][0]["face_token"]
        outer_id = "face_dataset_1"

        new_user = await self.register()
        
        await facepp_service.setuserid(face_token, new_user.user_id)
        await facepp_service.facesets_addface(outer_id, face_token)

        main_logger.info({"what": "facepp_register_user", "new_user": new_user.user_id})
        
        token = await self.login(new_user.user_id, 'faceapp')
        
        return new_user.user_id, token

    async def login(self, user_id, login_type):
        user_info = await self.get_user_by_user_id(user_id)
        if not user_info:
            raise Exception({"code": "100012", "message": _("用户不存在")})

        account_id = 1
        if self.account_id:
            account_id = self.account_id
        elif self.request:
            account_id = self.request.state.account["account_id"]
        
        token = self.gen_token(user_id, user_info["password"], account_id)

        # todo 登录日志记录
        main_logger.info({"what": "login", "user_id": user_id, "account_id": account_id, "login_type": login_type})
        
        return token

    async def register(self, username = "", password = ""):

        timestamp = datetime.utcnow().strftime('%Y%m%d%H%M%S%f')
        salt = CommonUtils.generate_random_string(6)
        origin_username = username
        if not username:
            username = timestamp + CommonUtils.generate_random_string(6)
        if not password:
            password = CommonUtils.generate_random_string(10)
        password_hash = CommonUtils.generate_md5_hash(password + salt)
        
        new_user = User(
            username=username,
            password=password_hash,
            salt=salt,
            extra_json={}
        )
        try:
            async with self.db_session() as session:
                session.add(new_user)
                await session.commit()

                if not origin_username:
                    new_user.username = "new_user_{}".format(new_user.user_id)
                    await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return new_user

    async def facepp_search(self, face_token, image_base64 = "", outer_id = "face_dataset_1"):
        
        facepp_service = FacePlusPlusService()

        search_result = await facepp_service.facepp_search(face_token, image_base64, outer_id)
        if not search_result:
            return None
        results = search_result.get("results", None)
        # todo 未使用，考虑如何使用
        thresholds = search_result.get("search_result", None)
        if not results:
            return None
        search_result = results[0]
        if search_result["confidence"] < 80:
            return None
        
        return search_result
    
    async def facepp_best_detect(self, image_base64_list):
        facepp_service = FacePlusPlusService()

        best_image_index = None
        best_score = 0
        best_result = None
        for key, image_base64 in image_base64_list.items():
            detect_result = await facepp_service.faceapp_detect(image_base64)
            if not detect_result.get("faces"):
                main_logger.warning(f"No face detected in image:{key}")
                continue

            face = detect_result['faces'][0]
            attributes = face.get("attributes")

            # 当前仅使用人脸质量参数判断图片人脸质量，可以增加更多元素，比如模糊度blur，遮挡度occlusion
            facequality = attributes.get("facequality")
            if not facequality:
                continue
            score = facequality["value"]

            if score > best_score:
                best_score = score
                best_image_index = key
                best_result = detect_result

        if best_score <= 0:
            main_logger.warning(f"No face detected")
            raise Exception({"code": "10081", "message": _("图片质量无法识别人脸")})
        
        if best_score <= 80:
            raise Exception({"code": "10081", "message": _("请重新刷脸，尽量拍到清晰完整脸部")})
        
        return best_image_index, best_result
    
    async def update_user_info(self, user_id, update_data):

        if not user_id:
            return False
        try:
            async with self.db_session() as session:
                stmt = update(User).where(
                    User.user_id == user_id
                ).values(**update_data)
                
                await session.execute(stmt)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return True
    
    async def get_user_by_user_id(self, user_id) -> dict:
        try:
            async with self.db_session() as session:
                stmt = select(User).where(User.user_id == user_id)
                result = await session.execute(stmt)
                user_info = result.scalars().first()
                if user_info:
                    user_info = user_info.__dict__
        except Exception as e:
            main_logger.error(f"Unexpected error in save_result: {str(e)}")
            raise

        return user_info
    
    async def get_user_list_by_user_ids(self, user_ids):
        try:
            async with self.db_session() as session:
                stmt = select(User).where(User.user_id.in_(user_ids))
                result = await session.execute(stmt)
                user_info = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_user_list_by_user_ids: {str(e)}")
            raise

        return user_info
    
    async def get_user_list_by_idnum(self, idnum, device_ids = [], start = 0, limit = 20):
        try:
            async with self.db_session() as session:
                conditions = [
                    User.idnum == idnum,
                    User.device_id.in_(device_ids)
                ]
                stmt = select(User).where(*conditions).offset(start).limit(limit)
                result = await session.execute(stmt)
                user_info = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_user_list_by_idnum: {str(e)}")
            raise

        return user_info

    
    async def update_user_info_json(self, user_id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(User).where(User.user_id == user_id)
                result = await session.execute(stmt)
                user_info = result.scalars().first()

                if not user_info:
                    return False
                
                await user_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_user_info_json: {str(e)}")
            raise

        return True
    
    def get_age_by_birthday(self, birthdate_str, date_format="%Y-%m-%d"):
        """
        计算年龄

        :param birthdate_str: 出生日期的字符串，例如 "1990-01-01"
        :param date_format: 出生日期字符串的格式，默认为"%Y-%m-%d"
        :return: 年龄（整数）
        """
        try:
            # 将字符串转换为日期对象
            birthdate = datetime.strptime(birthdate_str, date_format)
        except ValueError:
            raise ValueError("日期格式不正确，请使用格式：%s" % date_format)
        
        today = datetime.today()
        
        # 计算初步年龄
        age = today.year - birthdate.year
        
        # 如果今天还没到生日，则年龄减一
        if (today.month, today.day) < (birthdate.month, birthdate.day):
            age -= 1
        
        return age
    
    def convert_grade_to_text(self, grade: int) -> str:
        """
        将数字年级转换为中文文本表示
        
        Args:
            grade (int): 年级数字（-1表示未知，0表示幼儿园，1-12表示一年级到高三）
        
        Returns:
            str: 年级的中文表示
        """
        # 数字到中文数字的映射
        num_to_chinese = {
            1: _('一'),
            2: _('二'),
            3: _('三'),
            4: _('四'),
            5: _('五'),
            6: _('六'),
            7: _('七'),
            8: _('八'),
            9: _('九'),
            10: _('十')
        }
        
        if grade == -1:
            return _('未知')
        if grade == 0:
            return _('幼儿园')
        
        # 1-6年级（小学）
        if 1 <= grade <= 6:
            return _("{}年级").format(num_to_chinese[grade])
        
        # 7-9年级（初中）
        if 7 <= grade <= 9:
            middle_school_year = grade - 6
            return _("初{}").format(num_to_chinese[middle_school_year])
        
        # 10-12年级（高中）
        if 10 <= grade <= 12:
            high_school_year = grade - 9
            return _("高{}").format(num_to_chinese[high_school_year])
        
        return _('未知')
    
    def get_birthday_by_age(self, age):
        now = datetime.now()
        birth_datetime = now - relativedelta(years=age)
        return birth_datetime.date()