from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.services.user_service import UserService
from app.services.evaluation_result_service import EvaluationResultService
from app.core.database import get_db
from app.core.logger import main_logger
from app.utils.common import CommonUtils
from app.models.training_plan_result import TrainingPlanResult
from app.models.training_item_plan import TrainingItemPlan
from app.models.training_item_result import TrainingItemResult
from app.services.training_item_plan_service import TrainingItemPlanService
from app.services.training_item_service import TrainingItemService
from app.services.training_plan_result_service import TrainingPlanResultService
from app.services.training_item_result_service import TrainingItemResultService
from app.core.global_var import GlobalVar

class TrainingService:

    def __init__(self) -> None:
        self.db_session = get_db

        self.category_list = {
            "attention": {
                "name": _("注意力训练"),
                "desc": _("完成对应训练，提高你的注意力能力"),
                "key": "attention",
            },
            "memory": {
                "name": _("记忆力训练"),
                "desc": "",
                "key": "memory",
            },
            "execution": {
                "name": _("执行功能训练"),
                "desc": "",
                "key": "execution",
            },
            "language": {
                "name": _("语言能力训练"),
                "desc": "",
                "key": "language",
            },
            "logical": {
                "name": _("逻辑思维训练"),
                "desc": "",
                "key": "logical",
            },
            "space": {
                "name": _("空间认知训练"),
                "desc": "",
                "key": "space",
            },
            "learning": {
                "name": _("学习策略训练"),
                "desc": "",
                "key": "learning",
            },
            "society": {
                "name": _("社会认知训练"),
                "desc": "",
                "key": "society",
            },
        }

    # 获取分类列表
    async def get_category_list(self, user_id):
        user_service = UserService()
        training_plan_result_service = TrainingPlanResultService()

        user_info = await user_service.get_user_by_user_id(user_id)
        user_extra_json = user_info["extra_json"]

        result_id_list = []
        for key, value in self.category_list.items():
            train_plan_result_id = user_extra_json.get("train_" + key, 0)
            if train_plan_result_id:
                result_id_list.append(train_plan_result_id)
        
        result_info_list_dict = {}
        if len(result_id_list) > 0:
            result_info_list = await training_plan_result_service.get_plan_result_list_by_ids(result_id_list)
            for result_info in result_info_list:
                result_info_list_dict[result_info.train_plan_result_id] = result_info
        
        info_list = []
        for key, value in self.category_list.items():
            train_plan_result_id = user_extra_json.get("train_" + key, 0)
            status = 0
            if train_plan_result_id:
                result_info = result_info_list_dict[train_plan_result_id]
                status = result_info.status
            info = {
                "name": value["name"],
                "desc": value["desc"],
                "category": value["key"],
                "status": status,
            }
            info_list.append(info)

        return info_list

    # 获取分类对应的训练计划，返回训练项目和计划进度信息
    async def get_item_list_by_category(self, category, user_id):
        # 根据用户的信息，查询是否有正在进行的训练计划，如果没有，找到训练项目。
        # 如果有，则返回正在训练数据。
        # {"train_attention": "train_plan_result_id", "train_memory": "train_plan_result_id"}
        user_service = UserService()
        user_info = await user_service.get_user_by_user_id(user_id)
        user_extra_json = user_info["extra_json"]
        train_plan_result_id = user_extra_json.get("train_" + category, 0)

        training_item_service = TrainingItemService()
        training_item_plan_servcie = TrainingItemPlanService()
        training_item_result_servcie = TrainingItemResultService()
        training_plan_result_service = TrainingPlanResultService()

        if not train_plan_result_id:
            return {"code": 10101, "msg": _("当前没有训练计划，请先进行评估")}
        
        train_plan_result_info = await training_plan_result_service.get_plan_result_by_id(train_plan_result_id)
        if not train_plan_result_info:
            return {"code": 10102, "msg": _("当前没有训练计划，请先进行评估")}
        
        # 判断是否在计划周期内
        train_plan_config = train_plan_result_info.config_json
        duration_limit_start = train_plan_config["duration_limit_start"]
        duration_limit_end = train_plan_config["duration_limit_end"]
        current_time = int(datetime.utcnow().timestamp() * 1000)

        if current_time < duration_limit_start or current_time > duration_limit_end:
            # todo需要有个延迟队列关闭训练
            return {"code": 10103, "msg": _("当前训练不在训练周期内")}

        train_item_plan_info_list = await training_item_plan_servcie.get_item_plan_list_by_train_plan_result_id(train_plan_result_id)
        if not train_item_plan_info_list:
            return {"code": 10104, "msg": _("当前没有训练计划，请先进行评估")}

        # 时长、次数完成训练情况统计
        train_item_progress_info_list = {}
        for train_item_plan_info in train_item_plan_info_list:

            item_plan_config = train_item_plan_info.config_json
            train_item_plan_id = train_item_plan_info.train_item_plan_id
            
            item_result_list = []
            item_result_info_list = await training_item_result_servcie.get_item_result_list_by_train_item_plan_id(train_item_plan_id)
            for item_result_info in item_result_info_list:
                if item_result_info.status == 3:
                    item_result_list.append(item_result_info.result_json)

            train_item_progress_info = self.get_next_start_item(item_plan_config, item_result_list)
            train_item_progress_info_list[train_item_plan_id] = train_item_progress_info

        # 训练项目列表
        info_list = []
        for train_item_plan_info in train_item_plan_info_list:
            item_info = await training_item_service.get_item_by_id(train_item_plan_info.train_item_id)
            extra_json = item_info.extra_json
            bg_img = extra_json.get("bg_img", "https://llmagentstorage.blob.core.windows.net/static/ts/res/train_attention1_bg.jpg")
            game_url = extra_json.get("game_url", "")
            return_info = {
                "category": item_info.category,
                "name": _(item_info.name),
                "desc": _(item_info.desc),
                "key": item_info.key,
                "bg_img": bg_img,
                "game_url": game_url,
                "train_item_id": item_info.train_item_id,
                "train_item_plan_id": train_item_plan_info.train_item_plan_id,
                "complete_data": train_item_progress_info_list[train_item_plan_info.train_item_plan_id],
                "status": train_item_plan_info.status,
                "item_plan": train_item_plan_info.config_json
            }
            info_list.append(return_info)
        
        return_train_info = {
            "name": self.category_list[category]["name"],
            "desc": self.category_list[category]["desc"],
            "status": train_plan_result_info.status,
            "train_item_list": info_list,
            "plan": train_plan_result_info.config_json
        }

        return {"code": 0, "msg": "", "data": return_train_info}

    # 开始训练指定项目
    async def start_item(self, train_item_plan_id, user_id):

        train_item_plan_service = TrainingItemPlanService()
        train_item_result_service = TrainingItemResultService()

        item_plan_info = await train_item_plan_service.get_item_plan_by_id(train_item_plan_id)
        if not item_plan_info:
            return {"code": 10105, "msg": _("没有此训练项目计划")}
        
        if user_id != item_plan_info.user_id:
            return {"code": 10111, "msg": _("当前用户没有权限进行此训练项目")}
        
        category = item_plan_info.category

        user_service = UserService()
        user_info = await user_service.get_user_by_user_id(user_id)
        user_extra_json = user_info["extra_json"]
        train_plan_result_id = user_extra_json.get("train_" + category, 0)
        if train_plan_result_id != item_plan_info.train_plan_result_id:
            return {"code": 10122, "msg": _("本次训练项目不在当前训练计划中")}

        training_plan_result_service = TrainingPlanResultService()
        train_plan_result_info = await training_plan_result_service.get_plan_result_by_id(train_plan_result_id)
        if not train_plan_result_info:
            return {"code": 10106, "msg": _("当前没有训练计划，请先进行评估")}

        if item_plan_info.status == 0:
            return {"code": 10207, "msg": _("此训练项目已删除")}

        if item_plan_info.status == 3:
            return {"code": 10206, "msg": _("此训练项目已完成")}

        if train_plan_result_info.status == 1:
            update_data = {
                "start_time": datetime.utcnow(),
                "status": 2,
            }
            await train_item_plan_service.update_item_plan_by_id(train_item_plan_id, update_data)

            await self.start_training(train_plan_result_id)

        item_plan_config = item_plan_info.config_json
        
        item_result_list = []
        item_result_info_list = await train_item_result_service.get_item_result_list_by_train_item_plan_id(train_item_plan_id)
        for item_result_info in item_result_info_list:
            if item_result_info.status == 3:
                item_result_list.append(item_result_info.result_json)

        next_item_info = self.get_next_start_item(item_plan_config, item_result_list)
        if not next_item_info["next_item_info"]:
            return {"code": 10107, "msg": _("该项目训练计划已全部完成")}

        training_item_service = TrainingItemService()
        train_item_info = await training_item_service.get_item_by_id(item_plan_info.train_item_id)
        item_key = train_item_info.key
        category = train_item_info.category
        config_json = next_item_info["next_item_info"]

        new_training_item_result_info = TrainingItemResult(
            category=category,
            train_plan_result_id=train_plan_result_id,
            train_item_id=train_item_info.train_item_id,
            train_item_plan_id=train_item_plan_id,
            user_id=item_plan_info.user_id,
            account_id=item_plan_info.account_id,
            config_json=config_json,
            start_time=datetime.utcnow(),
            status=2,
        )
        new_training_item_result = await train_item_result_service.new_training_item_result(new_training_item_result_info)

        return_info = {
            "next_item_info": next_item_info["next_item_info"],
            "train_item_plan_id": train_item_plan_id,
            "train_item_result_id": new_training_item_result.train_item_result_id,
            "train_item_key": item_key,
        }

        return {"code": 0, "msg": "", "data": return_info}

    def get_next_start_item(self, item_plan_config, item_result_list):

        duration = item_plan_config["duration"]
        train_info = item_plan_config["train_info"]
        total_time = 0
        result_level = {"level1": 0, "level2": 0, "level3": 0}
        plan_level = {"level1": 0, "level2": 0, "level3": 0}
        for item_result in item_result_list:
            if item_result["level"] == 1:
                result_level["level1"] += 1
            elif item_result["level"] == 2:
                result_level["level2"] += 1
            elif item_result["level"] == 3:
                result_level["level3"] += 1
            total_time += item_result["total_time"]

        next_item_info = {}
        complete_progress = 0
        complete_num = 0
        for info in train_info:
            level_str = "level" + str(info["level"])
            result_level[level_str] -= 1
            if result_level[level_str] < 0:
                next_item_info["level"] = info["level"]
                break
            complete_num += 1

        complete_progress = complete_num / len(train_info) * 100

        return_info = {
            "complete_progress": complete_progress,
            "next_item_info": next_item_info,
            "complete_num": complete_num,
            "total_num": len(train_info),
        }

        return return_info 

    async def start_training(self, train_plan_result_id):

        training_plan_result_service = TrainingPlanResultService()

        update_data = {
            "start_time": datetime.utcnow(),
            "status": 2,
        }
        await training_plan_result_service.update_plan_result_by_id(train_plan_result_id, update_data)
        

    async def end_item(self, train_item_result_id, user_id, item_collect_data):

        train_item_plan_service = TrainingItemPlanService()
        train_item_result_service = TrainingItemResultService()

        train_item_result_info = await train_item_result_service.get_item_result_by_id(train_item_result_id)
        if not train_item_result_info:
            return {"code": 10108, "msg": _("该项目训练不存在")}

        if train_item_result_info.user_id != user_id:
            return {"code": 10112, "msg": _("当前用户没有权限进行此训练")}
        
        if train_item_result_info.status == 3:
            return {"code": 10206, "msg": _("此训练项目已完成")}
        
        config_json = train_item_result_info.config_json
        if not config_json:
            config_json = {}
        level = item_collect_data.get("level", 0)
        if level <= 0:
            return {"code": 10216, "msg": _("该项目训练不存在")}
        if config_json.get("level", 0) != level:
            return {"code": 10217, "msg": _("该项目训练不存在")}

        training_item_plan_id = train_item_result_info.train_item_plan_id

        item_plan_info = await train_item_plan_service.get_item_plan_by_id(training_item_plan_id)
        if not item_plan_info:
            return {"code": 10109, "msg": _("该项目训练计划不存在")}

        category = item_plan_info.category

        user_service = UserService()
        user_info = await user_service.get_user_by_user_id(user_id)
        user_extra_json = user_info["extra_json"]
        train_plan_result_id = user_extra_json.get("train_" + category, 0)
        if train_plan_result_id != item_plan_info.train_plan_result_id:
            return {"code": 10122, "msg": _("本次训练项目不在当前训练计划中")}

        training_plan_result_service = TrainingPlanResultService()
        train_plan_result_info = await training_plan_result_service.get_plan_result_by_id(train_plan_result_id)
        if not train_plan_result_info:
            return {"code": 10110, "msg": _("该训练计划不存在")}

        if item_plan_info.status == 0:
            return {"code": 10207, "msg": _("此训练项目已删除")}

        if item_plan_info.status == 3:
            return {"code": 10206, "msg": _("此训练项目计划已完成")}


        training_item_service = TrainingItemService()
        train_item_info = await training_item_service.get_item_by_id(item_plan_info.train_item_id)
        item_key = train_item_info.key
        category = train_item_info.category
        # 根据key，调用不同的处理方式对数据进行处理
        result_data = training_item_service.cal_result_info(item_key, item_collect_data)

        update_data = {
            "collect_json": item_collect_data,
            "result_json": result_data,
            "end_time": datetime.utcnow(),
            "status": 3,
        }
        await train_item_result_service.update_item_result_by_id(train_item_result_id, update_data)

        # 判断是否该训练项目的计划是否完成
        item_result_list = []
        item_result_info_list = await train_item_result_service.get_item_result_list_by_train_item_plan_id(training_item_plan_id)
        for item_result_info in item_result_info_list:
            if item_result_info.status == 3:
                item_result_list.append(item_result_info.result_json)

        next_item_info = self.get_next_start_item(item_plan_info.config_json, item_result_list)
        if not next_item_info["next_item_info"]: # 训练项目计划已完成
            update_data = {
                "end_time": datetime.utcnow(),
                "status": 3,
            }
            await train_item_plan_service.update_item_plan_by_id(training_item_plan_id, update_data)

        # 判断所有项目是否都已经完成，进行整体分类数据计算和评估
        is_training_finish = await self.end_trainning(train_plan_result_id)

        return_info = {
            "is_training_finish": is_training_finish,
            "train_plan_result_id": train_plan_result_id,
            "category": category,
        }
        return {"code": 0, "msg": "", "data": return_info}

    async def end_trainning(self, train_plan_result_id):

        train_item_plan_service = TrainingItemPlanService()
        train_item_result_service = TrainingItemResultService()
        training_plan_result_service = TrainingPlanResultService()

        train_plan_result_info = await training_plan_result_service.get_plan_result_by_id(train_plan_result_id)
        if not train_plan_result_info:
            return False
        category = train_plan_result_info.category

        train_item_result_data_list = []
        train_item_plan_info_list = await train_item_plan_service.get_item_plan_list_by_train_plan_result_id(train_plan_result_id)
        for train_item_plan_info in train_item_plan_info_list:

            item_plan_config = train_item_plan_info.config_json
            train_item_plan_id = train_item_plan_info.train_item_plan_id
            
            item_result_list = []
            item_result_info_list = await train_item_result_service.get_item_result_list_by_train_item_plan_id(train_item_plan_id)
            for item_result_info in item_result_info_list:
                if item_result_info.status == 3:
                    item_result_list.append(item_result_info.result_json)
                    train_item_result_data_list.append(item_result_info.result_json)

            next_item_info = self.get_next_start_item(item_plan_config, item_result_list)
            if next_item_info["complete_progress"] < 100:
                return False

        # 根据category，调用不同的处理方式对数据进行处理
        result_data = self.cal_result_info(category, train_item_result_data_list)

        update_data = {
            "result_json": result_data,
            "end_time": datetime.utcnow(),
            "status": 3,
        }
        await training_plan_result_service.update_plan_result_by_id(train_plan_result_id, update_data)

        await self.gen_report_info(train_plan_result_id)

        return True

    async def gen_report_info(self, train_plan_result_id):

        train_item_plan_service = TrainingItemPlanService()
        train_item_result_service = TrainingItemResultService()
        training_plan_result_service = TrainingPlanResultService()
        training_item_service = TrainingItemService()

        train_plan_result_info = await training_plan_result_service.get_plan_result_by_id(train_plan_result_id)
        if not train_plan_result_info:
            return False
        if train_plan_result_info.status != 3:
            return False

        category = train_plan_result_info.category

        item_plan_config_list = []
        item_complete_info_list = []
        train_item_plan_info_list = await train_item_plan_service.get_item_plan_list_by_train_plan_result_id(train_plan_result_id)
        for train_item_plan_info in train_item_plan_info_list:
            item_plan_config = train_item_plan_info.config_json
            item_plan_config_list.append(item_plan_config)

            train_item_plan_id = train_item_plan_info.train_item_plan_id
            train_item_info = await training_item_service.get_item_by_id(train_item_plan_info.train_item_id)
            
            item_result_list = []
            item_result_info_list = await train_item_result_service.get_item_result_list_by_train_item_plan_id(train_item_plan_id)
            for item_result_info in item_result_info_list:
                if item_result_info.status == 3:
                    item_result_list.append(item_result_info.result_json)

            next_item_info = self.get_next_start_item(item_plan_config, item_result_list)
            item_complete_info = {
                "name": _(train_item_info.name),
                "status": train_item_plan_info.status,
                "complete_progress": round(next_item_info["complete_progress"], 2)
            }
            item_complete_info_list.append(item_complete_info)

        user_service = UserService()
        user_id = train_plan_result_info.user_id
        user_info = await user_service.get_user_by_user_id(user_id)
        if not user_info:
            return False
        
        age = user_service.get_age_by_birthday(user_info["birthday"])

        result_json = train_plan_result_info.result_json
        plan_config_json = {
            "plan_config": train_plan_result_info.config_json,
            "item_plan_config": item_plan_config_list
        }
        report_info_dict = self.handle_report_data(category, plan_config_json, result_json, age)

        report_name = _("{}报告").format(self.category_list[category]["name"])
        start_time = plan_config_json["plan_config"]["duration_limit_start"]
        end_time = plan_config_json["plan_config"]["duration_limit_end"]

        total_time = report_info_dict["total_time"]

        total_item_num = 0
        for item_plan_config in plan_config_json["item_plan_config"]:
            total_item_num += len(item_plan_config["train_info"])

        complete_item_num = len(result_json)

        last_training_result = None
        last_training_result_list = await training_plan_result_service.get_plan_result_list_by_user_id(user_id, category, [3], 1, 1)
        if last_training_result_list:
            last_training_result = last_training_result_list[0]

        last_training_result_info = {}
        if last_training_result:
            last_report_json = last_training_result.report_json
            last_training_result_info = {
                "train_plan_result_id": last_training_result.train_plan_result_id,
                "score": last_report_json["score"],
            }

        return_report_info = {
            "report_name": report_name,
            "start_time": start_time,
            "end_time": end_time,
            "total_time": total_time,
            "total_item_num": total_item_num,
            "complete_item_num": complete_item_num,
            "user_info": {"name": user_info["name"], "age": age, "gender": user_info["gender"], "grade": user_info["grade"]},
            "last_training_result_info": last_training_result_info,
            "item_complete_info_list": item_complete_info_list,
            "version": 1,
            "report_time": int(datetime.utcnow().timestamp() * 1000), #报告生成时间
            "report_lang": GlobalVar.get_var("language"),
        }
        for key, value in report_info_dict.items():
            return_report_info[key] = value

        update_data = {
            "report_json": return_report_info,
        }
        await training_plan_result_service.update_plan_result_by_id(train_plan_result_id, update_data)

        return True

    async def get_report_info(self, train_plan_result_id, user_id):

        training_plan_result_service = TrainingPlanResultService()
        train_plan_result_info = await training_plan_result_service.get_plan_result_by_id(train_plan_result_id)

        if not train_plan_result_info:
            return {"code": 10119, "msg": _("此训练不存在")}
        if train_plan_result_info.status != 3:
            return {"code": 10109, "msg": _("此训练还未完成")}
        if not train_plan_result_info.report_json:
            return {"code": 10110, "msg": _("报告还未生成，请稍后")}
        if user_id != train_plan_result_info.user_id:
            return {"code": 10111, "msg": _("当前用户无权限查看此报告")}

        report_json_info = train_plan_result_info.report_json
        category = train_plan_result_info.category

        user_service = UserService()

        total_time = CommonUtils.format_time(report_json_info["total_time"])
        user_info = report_json_info["user_info"]
        user_age = user_info["age"]
        user_name = user_info["name"]
        user_gender = _("男") if user_info["gender"] == 1 else _("女")
        user_grade = user_service.convert_grade_to_text(user_info["grade"])
        
        report_info_dict = self.format_report_data(category, report_json_info)

        score = report_json_info["score"]
        last_training_result_info = report_json_info["last_training_result_info"]
        last_score_percent = 0
        if last_training_result_info:
            last_score = last_training_result_info.get("score", 0)
            if last_score != 0:
                last_score_percent = (score - last_score) / last_score

        start_time = int(report_json_info["start_time"] / 1000)
        start_time_str = CommonUtils.timestamp_to_string(start_time, "%Y-%m-%d")
        end_time = int(report_json_info["end_time"] / 1000)
        end_time_str = CommonUtils.timestamp_to_string(end_time, "%Y-%m-%d")

        complete_item_num = report_json_info["complete_item_num"]
        total_item_num = report_json_info["total_item_num"]

        return_report_info = {
            "version": report_json_info["version"],
            "report_name": report_json_info["report_name"],
            "start_time": start_time_str,
            "end_time": end_time_str,
            "total_time": total_time,
            "total_item_num": total_item_num,
            "complete_item_num": complete_item_num,
            "user_age": user_age,
            "user_name": user_name,
            "user_gender": user_gender,
            "user_grade": user_grade,
            "last_score_percent": round(last_score_percent, 4),
            "score": round(score),
            "completion_percent": round(complete_item_num / total_item_num, 4),
            "item_complete_info_list": report_json_info["item_complete_info_list"],
        }
        for key, value in report_info_dict.items():
            return_report_info[key] = value

        return {"code": 0, "msg": "", "data": return_report_info}

    def format_report_data(self, key, report_json):
        try:
            # 获取对应的处理器实例
            handler = CommonUtils.get_handler("app.services.training" ,key)
            # 调用处理器的方法
            result = handler.format_report_data(report_json)
            print(result)
        except ValueError as e:
            print(f"Wrong: {e}")

        return result

    def handle_report_data(self, key, plan_config_json, result_json, age):
        try:
            # 获取对应的处理器实例
            handler = CommonUtils.get_handler("app.services.training" ,key)
            # 调用处理器的方法
            result = handler.handle_report_data(plan_config_json, result_json, age)
            print(result)
        except ValueError as e:
            print(f"Wrong: {e}")

        return result

    def cal_result_info(self, category, train_item_result_data_list):
        try:
            # 获取对应的处理器实例
            handler = CommonUtils.get_handler("app.services.training", category)
            # 调用处理器的方法
            result = handler.cal_result_info(train_item_result_data_list)
            print(result)
        except ValueError as e:
            print(f"Wrong: {e}")

        return result
    
    def cal_score(self, category, collect_info):
        
        try:
            # 获取对应的处理器实例
            handler = CommonUtils.get_handler("app.services.training" ,category)
            # 调用处理器的方法
            result = handler.cal_score(collect_info)
            print(result)
        except ValueError as e:
            print(f"Wrong: {e}")

        return result

    # 自动生成训练计划
    async def gen_training_plan(self, result_id):

        # 根据评估结果生成训练计划
        evaluation_result_service = EvaluationResultService()
        result_info = await evaluation_result_service.get_result_by_id(result_id)
        category = result_info.category
        user_id = result_info.user_id
        account_id = result_info.account_id
        report_json = result_info.report_json

        score = report_json["score"]
        target_score = report_json["score"] + 10
        if target_score > 100:
            target_score = 100


        #训练计划
        current_time = datetime.utcnow()
        duration_limit_start = current_time
        duration_limit_end = current_time + timedelta(days=7)
        config_json = {
            "duration_limit_start": int(duration_limit_start.timestamp() * 1000),
            "duration_limit_end": int(duration_limit_end.timestamp() * 1000),
            "current_score": score,
            "target_score": target_score
        }
        
        training_plan_result_service = TrainingPlanResultService()
        new_train_plan_result_info = TrainingPlanResult(
            category=category,
            user_id=user_id,
            account_id=account_id,
            config_json=config_json,
            result_json={},
            report_json={},
            extra_json={},
        )
        new_train_plan_result = await training_plan_result_service.new_training_plan_result(new_train_plan_result_info)
        
        train_plan_result_id = new_train_plan_result.train_plan_result_id


        training_item_service = TrainingItemService()
        item_list = await training_item_service.get_items_by_category(category, [1])

        for item_info in item_list:
            
            # 训练项目计划
            item_config_json = {
                "train_item_id": item_info.train_item_id,
                "duration": 300, # 要求达到的时长(ms)
                "train_info": [ # 训练配置，训练次数和训练级别
                    {"level": 1},
                    # {"level": 2},
                    # {"level": 3},
                ]
            }
            
            training_item_plan_service = TrainingItemPlanService()
            new_training_item_plan_info = TrainingItemPlan(
                category=category,
                train_plan_result_id=train_plan_result_id,
                train_item_id=item_info.train_item_id,
                user_id=user_id,
                account_id=account_id,
                config_json=item_config_json,
                extra_json={}
            )
            new_training_item_plan = await training_item_plan_service.new_training_item_plan(new_training_item_plan_info)

        
        # 更新用户信息，需要进行的训练计划
        user_service = UserService()
        update_user_data = {
            "extra_json": {
                "train_" + category: train_plan_result_id
            }
        }
        await user_service.update_user_info_json(user_id, update_user_data)

        return True