import asyncio

from app.core.celery_app import celery_app
from app.services.upload_record_video_service import UploadRecordVideoService

@celery_app.task(queue='video_upload')
def task_video_upload_blob(*args, **kwargs):
    loop = asyncio.get_event_loop()
    loop.run_until_complete(_task_video_upload_blob(*args, **kwargs))

async def _task_video_upload_blob(*args, **kwargs):
    record_service = UploadRecordVideoService()
    await record_service.video_upload_blob(*args, **kwargs)