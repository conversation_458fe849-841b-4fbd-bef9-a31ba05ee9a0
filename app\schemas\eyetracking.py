from pydantic import BaseModel, Field, conint
from typing import List, Tuple


class EmPoint(BaseModel):
    x: float
    y: float


class EmRecord(BaseModel):
    type: int
    image: str
    antisaccade: bool
    timestamp: float
    trace: EmPoint
    target: EmPoint
    distractor: EmPoint


class EmRawData(BaseModel):
    stability: List[EmRecord] = Field(None, description="Gaze stability test data")
    saccade: List[EmRecord] = Field(None, description="Same direction saccade test data")
    antisaccade: List[EmRecord] = Field(None, description="Backtracking test data")
    lissajous: List[EmRecord] = Field(None, description="Smooth pursuit test data")
    explorations: List[EmRecord] = Field(None, description="Exploration test data")
    freestyle: List[EmRecord] = Field(None, description="Free viewing mode test data")

class StabilityRecord(BaseModel):
    timestamp: List[float] = Field(..., description="the timestamp for each point, default is []")
    trace: List[Tuple[float, float]] = Field(..., description="the trace points position, default is []")
    target: List[Tuple[float, float]] = Field(..., description="the target points position, default is []")
    distractor: List[Tuple[float, float]] = Field(..., description="the distractor points position, default is []")


class SaccadeRecord(BaseModel):
    timestamp: List[float] = Field(..., description="the timestamp for each point, default is []")
    trace: List[Tuple[float, float]] = Field(..., description="the trace points position, default is []")
    target: List[Tuple[float, float]] = Field(..., description="the target points position, default is []")


class AntiSaccadeRecord(BaseModel):
    timestamp: List[float] = Field(..., description="the timestamp for each point, default is []")
    trace: List[Tuple[float, float]] = Field(..., description="the trace points position, default is []")
    target: List[Tuple[float, float]] = Field(..., description="the target points position, default is []")
    antisaccade: List[bool] = Field(..., description="the ani-saccade flag")


class LissajousRecord(BaseModel):
    timestamp: List[float] = Field(..., description="the timestamp for each point, default is []")
    trace: List[Tuple[float, float]] = Field(..., description="the trace points position, default is []")
    target: List[Tuple[float, float]] = Field(..., description="the target points position, default is []")


class ExplorationRecord(BaseModel):
    image: str = Field(..., description="the image id")
    timestamp: List[float] = Field(..., description="the timestamp for each point, default is []")
    trace: List[Tuple[float, float]] = Field(..., description="the trace points position, default is []")
    target: List[Tuple[float, float]] = Field(..., description="the target points position, default is []")


class EmTestRawData(BaseModel):
    stability: StabilityRecord = Field(None, description="Gaze stability test data")
    saccade: SaccadeRecord = Field(None, description="Same direction saccade test data")
    antisaccade: AntiSaccadeRecord = Field(None, description="Backtracking test data")
    lissajous: LissajousRecord = Field(None, description="Smooth pursuit test data")
    explorations: List[ExplorationRecord] = Field(None, description="Exploration test data")
    freestyle: List[ExplorationRecord] = Field(None, description="Free viewing mode test data")
