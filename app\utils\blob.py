import asyncio
import io
import os
import logging
from datetime import datetime, timedelta
from azure.storage.blob import BlobServiceClient, ContentSettings, BlobSasPermissions, generate_blob_sas

logging.getLogger("azure").setLevel(logging.WARNING)


def generate_blob_sas_url(blob_name, permission=BlobSasPermissions(read=True), expiry_hours=16):
    account_name = os.getenv("AZURE_BLOB_ACCOUNT_NAME")
    account_key = os.getenv("AZURE_BLOB_ACCOUNT_KEY")
    container_name = os.getenv("AZURE_BLOB_CONTAINER_NAME")
    account_url = f"https://{account_name}.blob.core.windows.net"

    blob_sas = generate_blob_sas(account_name=account_name,
                                 account_key=account_key,
                                 container_name=container_name,
                                 blob_name=blob_name,
                                 permission=permission,
                                 expiry=datetime.utcnow() + timedelta(hours=expiry_hours))

    blob_sas_url = f"{account_url}/{container_name}/{blob_name}?{blob_sas}"
    return blob_sas_url


async def save_image_to_blob(image, blob_name):
    account_name = os.getenv("AZURE_BLOB_ACCOUNT_NAME")
    account_key = os.getenv("AZURE_BLOB_ACCOUNT_KEY")
    container_name = os.getenv("AZURE_BLOB_CONTAINER_NAME")
    account_url = f"https://{account_name}.blob.core.windows.net"

    # 创建同步客户端
    blob_service_client = BlobServiceClient(account_url=account_url, credential=account_key)
    blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

    # 将图像转换为字节流
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='PNG')
    img_byte_arr = img_byte_arr.getvalue()

    content_settings = ContentSettings(
        content_type='image/png',
        cache_control='max-age=3600'
    )

    # 在线程中执行上传操作
    await asyncio.to_thread(
        blob_client.upload_blob,
        img_byte_arr,
        overwrite=True,
        content_settings=content_settings
    )

    return f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}"


async def save_file_to_blob(local_file, blob_name, metadata=None, content_type=None, container_name=None):
    account_name = os.getenv("AZURE_BLOB_ACCOUNT_NAME")
    account_key = os.getenv("AZURE_BLOB_ACCOUNT_KEY")

    if not container_name:
        container_name = os.getenv("AZURE_BLOB_CONTAINER_NAME")

    account_url = f"https://{account_name}.blob.core.windows.net"

    if not os.path.isfile(local_file):
        raise FileNotFoundError(f"The file {local_file} does not exist.")

    # 创建同步客户端
    blob_service_client = BlobServiceClient(account_url=account_url, credential=account_key)
    blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

    def upload():
        with open(local_file, "rb") as data:
            blob_client.upload_blob(data, overwrite=True, metadata=metadata, content_type=content_type)

    # 在线程中执行上传操作
    await asyncio.to_thread(upload)

    return f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}"
