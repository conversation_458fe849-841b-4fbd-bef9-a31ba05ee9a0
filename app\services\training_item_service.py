from sqlalchemy import select, and_, or_, func, desc, update

from app.models.training_item import TrainingItem
from app.core.database import get_db
from app.core.logger import main_logger
from app.utils.common import CommonUtils


class TrainingItemService:

    def __init__(self) -> None:
        self.db_session = get_db

    async def get_items_by_category(self, category, status):
        try:
            async with self.db_session() as session:
                conditions = {
                    TrainingItem.category == category,
                    TrainingItem.status.in_(status)
                }
                stmt = select(TrainingItem).where(*conditions)
                result = await session.execute(stmt)
                item_info_list = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_items_by_category: {str(e)}")
            raise

        return item_info_list
    
    async def get_item_by_id(self, train_item_id):
        try:
            async with self.db_session() as session:
                stmt = select(TrainingItem).where(TrainingItem.train_item_id == train_item_id)
                result = await session.execute(stmt)
                item_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_item_by_id: {str(e)}")
            raise

        return item_info

    # 根据收集数据，计算需要的数据
    # 根据key不同，不同的计算和使用的数据
    def cal_result_info(self, key, collect_info):
        
        try:
            # 获取对应的处理器实例
            handler = CommonUtils.get_handler("app.services.training_item" ,key)
            # 调用处理器的方法
            result = handler.cal_result_info(collect_info)
            print(result)
        except ValueError as e:
            print(f"错误: {e}")

        return result

