import secrets
import string
import hashlib
import importlib
import traceback
from datetime import datetime

from app.core.logger import main_logger

class CommonUtils:

    @classmethod
    def generate_random_string(cls, length = 6):
        
        # 定义可用的字符集：大小写字母和数字
        characters = string.ascii_letters + string.digits
        
        # 生成一个长度为length的随机字符串
        random_string = ''.join(secrets.choice(characters) for _ in range(length))
        
        return random_string
    
    @classmethod
    def generate_md5_hash(cls, input_string):
        # 创建 MD5 哈希对象
        md5_hash = hashlib.md5()
        
        # 更新哈希对象，传入要哈希的字符串（需要是字节类型）
        md5_hash.update(input_string.encode('utf-8'))
        
        # 获取十六进制格式的哈希值
        hex_dig = md5_hash.hexdigest()
    
        return hex_dig
    
    @classmethod
    def calculate_md5(cls, file_path, chunk_size=8192):
        """
        计算文件的MD5值。

        :param file_path: 文件的路径
        :param chunk_size: 每次读取的字节数，默认为8KB
        :return: 文件的MD5值（十六进制字符串）
        """
        md5 = hashlib.md5()
        try:
            with open(file_path, 'rb') as f:
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    md5.update(chunk)
            return md5.hexdigest()
        except FileNotFoundError:
            print(f"文件未找到: {file_path}")
            return None
        except IOError:
            print(f"无法读取文件: {file_path}")
            return None

    @classmethod
    def get_handler(cls, module_path, handler_type):
        """
        根据handler_type动态导入并实例化对应的处理器类
        
        Args:
            handler_type: 处理器类型 ('a' 或 'b')
        
        Returns:
            处理器实例
        """
        try:
            # 构造模块路径
            module_path = f"{module_path}.handler_{handler_type.lower()}"
            # 动态导入模块
            module = importlib.import_module(module_path)
            # 构造类名
            class_name = f"Handler{handler_type.capitalize()}"
            # 获取类对象
            handler_class = getattr(module, class_name)
            # 实例化并返回
            return handler_class()
        except (ImportError, AttributeError) as e:
            error_stack = traceback.format_exc()
            main_logger.error(error_stack)
            raise ValueError(f"无法加载处理器: {handler_type}") from e

    @classmethod
    def format_time(cls, seconds) -> str:
        """
        将秒数格式化为人类可读的时间格式

        参数:
            seconds: 秒数
        返回:
            str: 格式化后的时间字符串 (比如 "2小时 3分钟 45秒")
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60

        parts = []
        if hours > 0:
            parts.append(_("{}小时").format(hours))
        if minutes > 0:
            parts.append(_("{}分钟").format(minutes))
        if secs > 0 or not parts:  # 如果秒数大于0或者没有小时和分钟
            parts.append(_("{}秒").format(secs))

        return " ".join(parts)
    
    @classmethod
    def timestamp_to_string(cls, timestamp, format='%Y-%m-%d %H:%M:%S'):
        dt_object = datetime.fromtimestamp(timestamp)
        return dt_object.strftime(format)