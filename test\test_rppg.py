import pandas as pd
import plotly.graph_objects as go
import numpy as np
from scipy import interpolate


def resample_ppg(time_intervals, ppg_values, target_fps=30):
    cumulative_time = np.cumsum(time_intervals)
    start_time = cumulative_time[0]
    end_time = cumulative_time[-1]

    # 计算当前采样率
    current_sampling_rate = len(ppg_values) / (end_time - start_time) * 1000
    print(f"原始数据点数: {len(ppg_values)}")
    print(f"原始时间范围: {start_time:.2f} ms 到 {end_time:.2f} ms")
    print(f"当前采样率: {current_sampling_rate:.2f} Hz")

    # 创建新的时间点
    new_time_points = np.arange(start_time, end_time, 1000 / target_fps)

    # 使用立方插值
    f = interpolate.interp1d(cumulative_time, ppg_values, kind='cubic', bounds_error=False, fill_value="extrapolate")
    new_ppg_values = f(new_time_points)

    print(f"重采样后数据点数: {len(new_ppg_values)}")
    print(f"重采样后时间范围: {new_time_points[0]:.2f} ms 到 {new_time_points[-1]:.2f} ms")
    print(f"新的采样率: {target_fps:.2f} Hz")

    return new_time_points, new_ppg_values


# 读取CSV文件
df = pd.read_csv('../data/20241010-211706.csv')
df['timestamp'] = df['Timestamps'].cumsum()

new_timestamps, new_ppg_values = resample_ppg(df['Timestamps'].values, df['PPGResults'].values)

# 创建图表
fig = go.Figure()

# 添加原始信号
fig.add_trace(go.Scatter(x=df['timestamp'], y=df['PPGResults'],
                         mode='lines', name='Original Signal'))

# 添加重采样后的信号
fig.add_trace(go.Scatter(x=new_timestamps, y=new_ppg_values,
                         mode='lines', name='Resampled Signal (30 Hz)'))

# 更新布局
fig.update_layout(
    title='Original vs Resampled PPG Signal',
    xaxis_title='Timestamp (ms)',
    yaxis_title='PPG Value',
    hovermode='x unified'
)

# 显示图表
fig.show()