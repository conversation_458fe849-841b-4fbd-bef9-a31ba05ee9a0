from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from app.models.base import Base, TimestampMixin


class AppConfig(Base, TimestampMixin):
    __tablename__ = 'appconfig'

    idx = Column(Integer, primary_key=True)
    name = Column(String(512), default='')
    value = Column(JSON, default=dict)

    def __repr__(self):
        return f"<AppConfig(name={self.name}, name={self.value})>"
