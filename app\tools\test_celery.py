import os
from datetime import datetime

from app.tasks.upload_video_task import task_video_upload_blob
from app.core.celery_app import make_celery
from app.services.upload_record_video_service import UploadRecordVideoService

celery = make_celery()
record_service = UploadRecordVideoService()

record_id = 73
chunks_dir = f"{record_service.tmp_dir}/{record_id}"
output_path_webm = f"{record_id}.webm"
output_file = os.path.join(chunks_dir, output_path_webm)
date_dir = datetime.now().strftime("%Y%m%d")
target_file = f"upload_video/{date_dir}/{record_id}.webm"

task_video_upload_blob.apply_async(args=[record_id, output_file, target_file, "video/webm"], countdown=5)