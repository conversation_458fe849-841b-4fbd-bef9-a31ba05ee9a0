from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from app.models.base import Base, TimestampMixin, ModelUpdateMixin

class Screening(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'screening'

    idx = Column(Integer, primary_key=True)
    type = Column(String(128), default='')
    user_id = Column(String(512), default='')
    face_id = Column(Integer, default=0)
    device_id = Column(String(100), default='')
    session_id = Column(String(255), default='')
    origin_data = Column(JSON, default=dict)
    process_result = Column(JSON, default=dict)
    process_result_act = Column(JSON, default=dict)

    def __repr__(self):
        return f"<Screening(type={self.type}, user_id={self.user_id}, created={self.created})>"
