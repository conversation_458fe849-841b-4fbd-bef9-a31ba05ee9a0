import asyncio
import dotenv
from datetime import datetime

from app.services.account_service import AccountService
from app.models.account import Account
from app.services.account_key_service import AccountKeyService
from app.services.account_device_service import AccountDeviceService
from app.models.account_key import AccountKey
from app.models.account_device import AccountDevice
from app.utils.common import CommonUtils

dotenv.load_dotenv()

class AccountTools:
    
    def __init__(self) -> None:
        pass

    async def create_account(self, name, password):
        account_service = AccountService()
        salt = CommonUtils.generate_random_string(6)
        password_hash = CommonUtils.generate_md5_hash(password + salt)
        new_account = Account(
            name=name,
            password=password_hash,
            salt=salt,
            extra_json={}
        )
        return await account_service.new_account(new_account)

    async def create_account_key(self, account_id):
        account_key_service = AccountKeyService()
        timestamp = datetime.utcnow().strftime('%Y%m%d%H')
        app_id = "id-" + timestamp + CommonUtils.generate_random_string(6)
        app_key = "sk-" + timestamp + CommonUtils.generate_random_string(30)
        new_account_key = AccountKey(
            account_id=account_id,
            app_id=app_id,
            app_key=app_key,
            extra_json={}
        )
        return await account_key_service.new_account_key(new_account_key)
    
    async def create_account_device(self, device_id, account_id):
        account_device_service = AccountDeviceService()
        new_account_device = AccountDevice(
            account_id=account_id,
            device_id=device_id,
            extra_json={}
        )
        return await account_device_service.new_account_device(new_account_device)

async def main():
    tools = AccountTools()
    name = "test_account"
    password = "tsc102"
    new_account = await tools.create_account(name, password)

    account_id = new_account.account_id

    await tools.create_account_key(account_id)

    device_id = "53b8179041e0da49"
    await tools.create_account_device(device_id, account_id)

if __name__ == "__main__":
    asyncio.run(main())