import asyncio
import dotenv
import os
import json

from app.services.quick_screening_question_service import QuickScreeningQuestionService
from app.models.quick_screening_questions import QuickScreeningQuestion
from app.utils.blob import save_file_to_blob
from app.utils.common import CommonUtils

dotenv.load_dotenv()

class QSTools:
    
    def __init__(self) -> None:
        pass

    async def get_question_list(self, file_list_dir):
        try:
            for question_dir in os.listdir(file_list_dir):
                question_dir_full = os.path.join(file_list_dir, question_dir)
                if not os.path.isdir(question_dir_full):
                    continue
                for file in os.listdir(question_dir_full):
                    if file == 'content.json':
                        file_path = os.path.join(question_dir_full, file)
                        with open(file_path, 'rb') as infile:
                            json_str = infile.read()
                            question_info = json.loads(json_str)
                            # print(question_info)
                            await self.handle_question_info(question_dir_full, question_info)
        except Exception as e:
            print(f"Error cleaning up chunks: {e}")
    
    async def handle_media_file(self, content, content_path, content_type, part):
        if not os.path.isfile(content_path):
            return False
        file_md5 = CommonUtils.calculate_md5(content_path)
        blob_name = f'ts/res/question/{part}/{file_md5}_{content}'
        blob_url = await save_file_to_blob(content_path, blob_name, None, content_type, "static")
        return blob_url

    async def handle_question_info(self, question_path, question_info):
        if not question_info:
            return
        if not question_path:
            return

        print("handle_question_info {}".format(question_path))

        part = question_info.get("part", "0")

        content_json = question_info.get("content_json", [])
        for index, value in enumerate(content_json):
            content = value.get("content")
            content_path = os.path.join(question_path, content)

            if value.get("type") == "image":
                blob_url = await self.handle_media_file(content, content_path, "image/jpeg", part)
                content_json[index]["content"] = blob_url

            if value.get("type") == "audio":
                blob_url = await self.handle_media_file(content, content_path, "audio/mpeg", part)
                content_json[index]["content"] = blob_url

        question_info["content_json"] = content_json

        option_json = question_info.get("option_json", {})
        option_list = option_json.get("option_list", {})
        option_type = option_json.get("type", {})
        if option_type == "single_media_choice":
            for key, value in option_list.items():
                for index, subvalue in enumerate(value):
                    content = subvalue.get("content")
                    content_path = os.path.join(question_path, content)
                    if subvalue.get("type") == 'image':
                        blob_url = await self.handle_media_file(content, content_path, "image/jpeg", part)
                        option_list[key][index]["content"] = blob_url
                    if subvalue.get("type") == 'audio':
                        blob_url = await self.handle_media_file(content, content_path, "audio/mpeg", part)
                        option_list[key][index]["content"] = blob_url
        if option_type == "single_choice":
            pass

        question_info["option_json"]["option_list"] = option_list

        new_question = QuickScreeningQuestion(
            title=question_info["title"],
            desc=question_info["desc"],
            content_json=question_info["content_json"],
            key=question_info["key"],
            type=question_info["type"],
            category=question_info["category"],
            part=question_info["part"],
            option_json=question_info["option_json"],
            ref_answer_json=question_info["ref_answer_json"],
            extra_json={},
        )

        await self.create_qs_question(new_question)

    async def create_qs_question(self, new_question):
        qs_service = QuickScreeningQuestionService()
        await qs_service.new_question(new_question)

async def main():
    tools = QSTools()
    
    question_list = [
        {
            "title": "当我感到害怕时，出现呼吸困难（出气不顺）。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
        {
            "title": "我害怕时，心跳会加快。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
        {
            "title": "我总是感到紧张不安。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
        {
            "title": "我担心将来会发生什么事情。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
        {
            "title": "父母无论去哪里我总是离不开他们。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
        {
            "title": "当我与不熟悉的人在一起时就感到紧张。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
        {
            "title": "当我与不熟悉的人在一起时觉得害羞。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
        {
            "title": "我担心又要去上学。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
        {
            "title": "我害怕去上学。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
        {
            "title": "当我害怕时，感到恶心或想吐。",
            "desc": "",
            "key": "emotion_survey",
            "type": "choice",
            "category": "",
            "part": "emotion_survey",
            "option_json": {"option_list": {"opt1": "从不", "opt2": "有时", "opt3": "经常"}, "type": "single_choice"},
            "ref_answer_json": {},
        },
    ]

    question_list = [
        {
            "title": "你看远处（如黑板上的字）时，是否会模糊或看不清？",
            "desc": "",
            "content_json": [],
            "key": "visual_survey",
            "type": "choice",
            "category": "myopia", # 近视筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "从来没有模糊",
                    "opt2": "有时模糊",
                    "opt3": "经常模糊",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {},
        },
        {
            "title": "你看近处（如书上的字）时，是否会觉得很难看清？",
            "desc": "",
            "content_json": [],
            "key": "visual_survey",
            "type": "choice",
            "category": "myopia", # 近视筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "从来没有模糊",
                    "opt2": "有时模糊",
                    "opt3": "经常模糊",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {},
        },
        {
            "title": "你用一只眼睛闭上另一只眼睛时，是否感觉视力明显变模糊？",
            "desc": "",
            "content_json": [],
            "key": "visual_survey",
            "type": "choice",
            "category": "amblyopia", # 弱势筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "从来没有",
                    "opt2": "有时候会",
                    "opt3": "经常会",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {},
        },
        {
            "title": "你用两只眼睛一起看时，是否觉得一只眼睛比另一只眼睛用力更多？",
            "desc": "",
            "content_json": [],
            "key": "visual_survey",
            "type": "choice",
            "category": "amblyopia", # 弱势筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "没有这种感觉",
                    "opt2": "有时候会",
                    "opt3": "经常会",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {},
        },
        {
            "title": "你是否在看3D电影或图片时感觉不到立体效果？",
            "desc": "",
            "content_json": [],
            "key": "visual_survey",
            "type": "choice",
            "category": "stereopsis", # 立体视筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "从来没有",
                    "opt2": "有时候会",
                    "opt3": "经常会",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {},
        },
        # {
        #     "title": "",
        #     "desc": "",
        #     "content_json": [{"type": "image", "content": "https://"}],
        #     "key": "visual_survey",
        #     "type": "choice",
        #     "category": "color_blindness", # 色盲筛查
        #     "part": "visual_survey",
        #     "option_json": {
        #         "option_list": {
        #             # "opt1": [{"type": "text", "content": "从不"}, {"type": "image", "content": "https://xxxx"}],
        #             "opt1": [{"type": "image", "content": "https://xxxx"}],
        #             "opt2": [{"type": "image", "content": "https://xxxx"}],
        #             "opt3": [{"type": "image", "content": "https://xxxx"}],
        #         },
        #         "type": "single_media_choice"
        #     },
        #     "ref_answer_json": {},
        # },
    ]

    question_list = [
        {
            "title": "在日常生活中，您是否常常难以判断物体的远近或距离？如伸手接球、倒水时距判断失误较多。",
            "desc": "",
            "content_json": [],
            "key": "visual_survey",
            "type": "choice",
            "category": "stereopsis", # 立体视筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "基本不会出现问题",
                    "opt2": "偶尔会出现误差",
                    "opt3": "经常因距离判断不准而出错",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {},
        },
        {
            "title": "",
            "desc": "",
            "content_json": [{"type": "image", "content": "https://learning-ability-static.ybbywb.com/img/visual_survey/29.jpg"}],
            "key": "visual_survey_answer",
            "type": "choice",
            "category": "color_blindness", # 色盲筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "29",
                    "opt2": "39",
                    "opt3": "看不清",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt1",
            },
        },
        {
            "title": "",
            "desc": "",
            "content_json": [{"type": "image", "content": "https://learning-ability-static.ybbywb.com/img/visual_survey/69.jpg"}],
            "key": "visual_survey_answer", # 根据答案来进行算分
            "type": "choice",
            "category": "color_blindness", # 色盲筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "69",
                    "opt2": "89",
                    "opt3": "看不清",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt1",
            },
        },
        {
            "title": "",
            "desc": "",
            "content_json": [{"type": "image", "content": "https://learning-ability-static.ybbywb.com/img/visual_survey/606.jpg"}],
            "key": "visual_survey_answer", # 根据答案来进行算分
            "type": "choice",
            "category": "color_blindness", # 色盲筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "308",
                    "opt2": "606",
                    "opt3": "看不清",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt2",
            },
        },
        {
            "title": "",
            "desc": "",
            "content_json": [{"type": "image", "content": "https://learning-ability-static.ybbywb.com/img/visual_survey/602.jpg"}],
            "key": "visual_survey_answer", # 根据答案来进行算分
            "type": "choice",
            "category": "color_blindness", # 色盲筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "98",
                    "opt2": "602",
                    "opt3": "看不清",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt2",
            },
        },
        {
            "title": "",
            "desc": "",
            "content_json": [{"type": "image", "content": "https://learning-ability-static.ybbywb.com/img/visual_survey/%E4%B8%89%E8%A7%92%E5%9C%86.jpg"}],
            "key": "visual_survey_answer", # 根据答案来进行算分
            "type": "choice",
            "category": "color_blindness", # 色盲筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "△/O",
                    "opt2": "口/O",
                    "opt3": "看不清",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt1",
            },
        },
        {
            "title": "",
            "desc": "",
            "content_json": [{"type": "image", "content": "https://learning-ability-static.ybbywb.com/img/visual_survey/55.jpg"}],
            "key": "visual_survey_answer", # 根据答案来进行算分
            "type": "choice",
            "category": "color_blindness", # 色盲筛查
            "part": "visual_survey",
            "option_json": {
                "option_list": {
                    "opt1": "55",
                    "opt2": "45",
                    "opt3": "看不清",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt1",
            },
        },
    ]

    question_list = [
        {
            "title": "本次为练习，熟悉规则，数字序列开始长度为2，最大序列长度为3",
            "desc": "",
            "content_json": {"start_seq": 2, "max_seq": 3, "environment": "demo"},
            "key": "cognitive_survey",
            "type": "game",
            "category": "cognitive", # 认知能力筛查
            "part": "cognitive_survey",
            "option_json": {},
            "ref_answer_json": {},
        },
        {
            "title": "本次为实际筛查，数字序列开始长度为3，最大序列长度为18",
            "desc": "",
            "content_json": {"start_seq": 3, "max_seq": 18, "environment": "prod"},
            "key": "cognitive_survey",
            "type": "game",
            "category": "cognitive", # 认知能力筛查
            "part": "cognitive_survey",
            "option_json": {},
            "ref_answer_json": {},
        },
    ],

    question_list = [
        {
            "title": "小明的书包上有五个徽章，其中有一个是他最喜欢的‘火箭徽章’，你能帮他找到正确的描述吗？",
            "desc": "",
            "content_json": [],
            "key": "visual_discrimination",
            "type": "choice",
            "category": "visual_discrimination", # 视觉辨别
            "part": "visual",
            "option_json": {
                "option_list": {
                    "opt1": "一个蓝色的火箭，尾部有两条火焰。",
                    "opt2": "一个蓝色的火箭，尾部有三条火焰。",
                    "opt3": "一个红色的火箭，尾部有三条火焰。",
                    "opt4": "一个蓝色的飞机，尾部有三条火焰。",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt2",
            },
        },
        {
            "title": "你刚刚看到一只戴着红色帽子的小熊在跳舞。现在你能回忆起它吗？",
            "desc": "",
            "content_json": [],
            "key": "visual_memory",
            "type": "choice",
            "category": "visual_memory", # 视觉记忆
            "part": "visual",
            "option_json": {
                "option_list": {
                    "opt1": "一只戴着蓝色帽子的小熊在跳舞。",
                    "opt2": "一只戴着红色帽子的小熊在跳舞。",
                    "opt3": "一只戴着红色帽子的小熊在睡觉。",
                    "opt4": "一只戴着红色帽子的兔子在跳舞。",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt2",
            },
        },
        {
            "title": "",
            "desc": "",
            "content_json": [{"type": "audio", "content": "https://llmagentstorage.blob.core.windows.net/static/ts/res/improve_learning/improve_learning_listening_1.mp3"}],
            "key": "listening",
            "type": "choice",
            "category": "listening", # 听讲
            "part": "listening",
            "option_json": {
                "option_list": {
                    "opt1": "小明喜欢绿色的苹果。",
                    "opt2": "小华喜欢大而甜的苹果。",
                    "opt3": "小明喜欢大而甜的苹果。",
                    "opt4": "小明和小华都喜欢一样的苹果。",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt3",
            },
        },
        {
            "title": "小明正在做作业，他一坐下来就开始专心写题目，虽然有电话响起，但他还是继续写作业。你觉得小明在做作业时表现如何？",
            "desc": "",
            "content_json": [],
            "key": "attention",
            "type": "choice",
            "category": "attention", # 专注力
            "part": "exam",
            "option_json": {
                "option_list": {
                    "opt1": "小明听到电话铃声后立刻停下来看电话。",
                    "opt2": "小明完全没有注意到电话，专心完成作业。",
                    "opt3": "小明一会儿做作业，一会儿玩手机。",
                    "opt4": "小明做作业时总是分心，无法坚持。",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt2",
            },
        },
        {
            "title": "小红在看完一个故事后，能记住每个细节，回家时还跟妈妈讲述故事内容。你觉得她的记忆力如何？",
            "desc": "",
            "content_json": [],
            "key": "memory",
            "type": "choice",
            "category": "memory", # 记忆力
            "part": "exam",
            "option_json": {
                "option_list": {
                    "opt1": "小红记得的只是故事的开头，后面忘了。",
                    "opt2": "小红能准确回忆整个故事的内容，甚至细节都能记得。",
                    "opt3": "小红只能记住故事的结尾。",
                    "opt4": "小红记得的只是故事的某些人物。",
                },
                "type": "single_choice"
            },
            "ref_answer_json": {
                "answer": "opt2",
            },
        },
    ]

    # for value in question_list:

    #     new_question = QuickScreeningQuestion(
    #         title=value["title"],
    #         desc=value["desc"],
    #         content_json=value["content_json"],
    #         key=value["key"],
    #         type=value["type"],
    #         category=value["category"],
    #         part=value["part"],
    #         option_json=value["option_json"],
    #         ref_answer_json=value["ref_answer_json"],
    #         extra_json={},
    #     )
    #     await tools.create_qs_question(new_question)

    file_list_dir = "/Users/<USER>/Desktop/考试能力demo"
    await tools.get_question_list(file_list_dir)

if __name__ == "__main__":
    asyncio.run(main())