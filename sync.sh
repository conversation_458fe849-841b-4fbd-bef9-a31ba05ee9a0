branch=$(git rev-parse --abbrev-ref HEAD)
commit=$(git rev-parse --short HEAD)
commit_count=$(git rev-list --count HEAD)
echo "generate version version=${branch}.${commit_count}.${commit}"
echo "{\"version\":\"${branch}.${commit_count}.${commit}\"}" > version.json

project_dir=llm-app
current_dir=$(pwd)
cd ..
echo "sync workspace"
rsync -azP --exclude-from="$project_dir/exclude-list.txt" --delete $project_dir/ root@alitest:/opt/ybbywb/backend/$project_dir/
rsync -azP --exclude-from="$project_dir/exclude-list.txt" --delete $project_dir/ llm@gpu2:/home/<USER>/projects/$project_dir/
cd $current_dir

