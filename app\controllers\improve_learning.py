from fastapi import APIRouter, HTTPException, Request
from fastapi import Depends
from pydantic import BaseModel, Field
import traceback
import json
from typing import Any, Dict

from app.core.decorators import require_user_token
from app.schemas.base import APIResponse
from app.services.user_service import UserService
from app.core.logger import main_logger
from app.services.improve_learning_service import ImproveLearningService


router = APIRouter()


# 获取提升学习力评估状态
@router.post("/get_info")
@require_user_token()
async def get_info(request: Request):

    current_user_info = request.state.user
    current_account_info = request.state.account

    improve_learning_service = ImproveLearningService()
    user_service = UserService()

    il_status = 1

    try:
        user_info = await user_service.get_user_by_user_id(current_user_info["user_id"])
        user_extra_json = user_info["extra_json"]
        il_result_id = user_extra_json.get("il_result_id", 0)
        il_status = user_extra_json.get("il_status", 0)
        il_status_info = user_extra_json.get("il_status_info", {})

    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)

    data = {
        "status": il_status,
        "status_info": il_status_info,
        "il_result_id": il_result_id,
    }

    return APIResponse(
        code=0, message=_("提交成功"), data=data
    )

# 获取题目列表
class GetQuestionList(BaseModel):
    part: str
@router.post("/get_question_list")
@require_user_token()
async def get_question_list(request: Request, questionRequest: GetQuestionList):

    current_user_info = request.state.user
    current_account_info = request.state.account

    part = questionRequest.part

    improve_learning_service = ImproveLearningService()

    user_service = UserService()
    user_info = await user_service.get_user_by_user_id(current_user_info["user_id"])
    user_extra_json = user_info["extra_json"]
    il_status = user_extra_json.get("il_status", 0)
    if il_status == 3:
        return APIResponse(code=10086, message="已经完成学习力提升评估")
    il_status_info = user_extra_json.get("il_status_info", {})
    if not il_status_info:
        il_status_info = {}
    if il_status_info.get(part, 0) == 3:
        return APIResponse(code=10086, message="已经完成该部分评估")

    try:
        list, question_list_key = await improve_learning_service.get_list(part)
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        list = []
        question_list_key = ""

    data = {
        "question_list_key": question_list_key,
        "list": list,
    }

    return APIResponse(
        code=0, message=_("获取成功"), data=data
    )

# 重新开始
@router.post("/restart")
@require_user_token()
async def restart(request: Request):

    current_user_info = request.state.user
    current_account_info = request.state.account

    improve_learning_service = ImproveLearningService()
    user_service = UserService()

    try:
        user_update_data = {
            "extra_json": {
                "il_result_id": 0,
                "il_status": 1,
                "il_status_info": 0,
            }
        }
        await user_service.update_user_info_json(current_user_info["user_id"], user_update_data)

    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)

    return APIResponse(
        code=0, message=_("提交成功"), data={}
    )

# 用户提交的回答数据处理
class AnswerRequest(BaseModel):
    collect_answer: Dict[str, Any]
@router.post("/answer")
@require_user_token()
async def answer(request: Request, answer_request: AnswerRequest):
    current_user_info = request.state.user
    current_account_info = request.state.account

    user_service = UserService()
    user_info = await user_service.get_user_by_user_id(current_user_info["user_id"])
    user_extra_json = user_info["extra_json"]
    il_status = user_extra_json.get("il_status", 0)
    if il_status == 3:
        return APIResponse(code=10086, message="已经完成学习力提升评估")

    improve_learning_service = ImproveLearningService()
    collect_answer = answer_request.collect_answer

    try:
        result = await improve_learning_service.answer_question(collect_answer, current_user_info["user_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10087, message="提升学习力提交出错")

    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    return APIResponse(
        code=0, message=_("提交成功"), data=data
    )

# 获取报告数据
class GetReportRequest(BaseModel):
    il_result_id: int
@router.post("/get_report")
@require_user_token()
async def get_report(request: Request, get_report_request: GetReportRequest):
    
    current_user_info = request.state.user
    current_account_info = request.state.account

    il_result_id = get_report_request.il_result_id
    improve_learning_service = ImproveLearningService()

    try:
        result = await improve_learning_service.get_report(il_result_id, current_user_info["user_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10087, message=_("获取报告失败"))

    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    return APIResponse(
        code=0, message=_("提交成功"), data=data
    )
