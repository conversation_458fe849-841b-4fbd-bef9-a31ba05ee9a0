from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.account_key import AccountKey


class AccountKeyService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_account_key(self, new_account_key):
        try:
            async with self.db_session() as session:
                session.add(new_account_key)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_account_key: {str(e)}")
            raise

        return new_account_key
    

    async def get_account_key_by_app_key(self, app_key):
        try:
            async with self.db_session() as session:
                conditions = [
                    AccountKey.app_key == app_key,
                    AccountKey.status.in_([1])
                ]
                stmt = select(AccountKey).where(and_(*conditions))
                result = await session.execute(stmt)
                result_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_account_key_by_app_key: {str(e)}")
            raise

        return result_info
