from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from app.models.base import Base, TimestampMixin


class Message(Base, TimestampMixin):
    __tablename__ = 'messages'

    idx = Column(Integer, primary_key=True)
    owner = Column(Integer, default=1)
    user_id = Column(String(512), default="")
    session_id = Column(String(512), default="")
    text_data = Column(Text, default="")
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<Message(user={self.user_id}, owner={self.owner}, text_data={self.text_data})>"
