from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc

from app.models.copilot_summary import CopilotSummary


class CopilotService:
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create_copilot_summary(self, data: CopilotSummary) -> CopilotSummary:
        new_summary = data
        self.db_session.add(new_summary)
        await self.db_session.commit()
        return new_summary

    async def fetch_all_by_channel_no(self, channel_no, start, limit):
        conditions = [
            CopilotSummary.channel_no == channel_no
        ]
        query = select(CopilotSummary).where(and_(*conditions)).offset(start).limit(limit)
        order = desc(CopilotSummary.id)
        query = query.order_by(order)
        result = await self.db_session.execute(query)
        return result.scalars().all()