from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from app.models.base import Base, TimestampMixin, ModelUpdateMixin


class ImproveLearningResult(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'improve_learning_results'

    il_result_id = Column(Integer, primary_key=True)
    user_id = Column(Integer, default=0)
    result_json = Column(JSON, default=dict)
    report_json = Column(JSON, default=dict)
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<ImproveLearningResult(qs_result_id={self.il_result_id}, user_id={self.user_id})>"
