import urllib.request
import urllib.error
import time
import httpx
import os
import json

from app.core.logger import main_logger
from app.core.database import get_db

# 旷世人脸接口封装
class FacePlusPlusService:
    
    def __init__(self) -> None:
        self.facepp_api_url = os.getenv("FACEPP_API_URL")
        self.db_session = get_db

    async def facepp_search(self, face_token, image_base64, outer_id):
        request_url = self.facepp_api_url + "/facepp/v3/search"
        form_data = {
            "outer_id": outer_id,
            "return_result_count": 1
        }
        if face_token:
            form_data["face_token"] = face_token
        if image_base64:
            form_data["image_base64"] = image_base64
        try:
            response = await self.request(request_url, form_data)
        except Exception as e:
            error = e.args[0]
            if error["reason"]["error_message"] == "EMPTY_FACESET":
                return None
            else:
                raise

        main_logger.info("facepp_search success repsonse: " + json.dumps(response))

        return response

    async def faceapp_detect(self, image_base64):
        request_url = self.facepp_api_url + "/facepp/v3/detect"
        form_data = {
            "image_base64": image_base64,
            "return_landmark": 2,
            "return_attributes": "gender,age,smiling,headpose,facequality,blur,eyestatus,emotion,beauty,mouthstatus,eyegaze,skinstatus",
        }
        response = await self.request(request_url, form_data)
        main_logger.info("faceapp_detect success repsonse: " + json.dumps(response))
        return response

    async def create_faceset(self, outer_id, display_name = "", tags = ""):
        request_url = self.facepp_api_url + "/facepp/v3/faceset/create"
        form_data = {
            "display_name": display_name,
            "outer_id": outer_id,
            "tags": tags
        }
        response = await self.request(request_url, form_data)
        main_logger.info("create_faceset success repsonse: " + json.dumps(response))
        return response

    def get_facesets(self, tags):
        pass

    async def facesets_addface(self, outer_id, face_tokens):
        request_url = self.facepp_api_url + "/facepp/v3/faceset/addface"
        form_data = {
            "outer_id": outer_id,
            "face_tokens": face_tokens
        }
        response = await self.request(request_url, form_data)
        main_logger.info("facesets_addface success repsonse: " + json.dumps(response))
        return response

    async def facesets_removeface(self, outer_id, face_tokens):
        request_url = self.facepp_api_url + "/facepp/v3/faceset/removeface"
        form_data = {
            "outer_id": outer_id,
            "face_tokens": face_tokens
        }
        response = await self.request(request_url, form_data)
        main_logger.info("facesets_removeface success repsonse: " + json.dumps(response))
        return response

    async def setuserid(self, face_token, user_id):
        request_url = self.facepp_api_url + "/facepp/v3/face/setuserid"
        form_data = {
            "face_token": face_token,
            "user_id": user_id
        }
        response = await self.request(request_url, form_data)
        main_logger.info("setuserid success repsonse: " + json.dumps(response))
        return response

    async def request(self, request_url, form_data):
        timeout = httpx.Timeout(
            connect=10.0,  # 连接超时（秒）
            read=30.0,  # 读取超时（秒）
            write=10.0,  # 写入超时（秒）
            pool=None  # 连接池超时（秒），可以设置为None以使用默认值
        )
        api_key = os.getenv("FACEPP_API_KEY")
        api_secret = os.getenv("FACEPP_API_SECRET")
        form_data["api_key"] = api_key
        form_data["api_secret"] = api_secret
        async with httpx.AsyncClient(timeout=timeout) as client:
            service_addr = request_url
            # headers = {
            #     "Content-Type": "multipart/form-data; charset=utf-8",
            # }
            try:
                result = await client.post(service_addr, data=form_data)
            except Exception as e:
                print(f"An error occurred: {e}")
                raise
            
            if result.status_code != 200:
                error_log = {"what": "facepp api failed", "request_url": request_url, "response": result.text}
                main_logger.error(json.dumps(error_log, ensure_ascii=False))
                raise Exception({"code": "10030", "message": "调用face接口失败", "reason": json.loads(result.text)})
            response = json.loads(result.text)

        return response

    async def new_face_info(self, new_face_info):
        try:
            async with self.db_session() as session:
                session.add(new_face_info)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_face_info: {str(e)}")
            raise

        return new_face_info