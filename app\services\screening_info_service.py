from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.screening_info import ScreeningInfo

class ScreeningInfoService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_screening_info(self, new_screening_info):
        try:
            async with self.db_session() as session:
                session.add(new_screening_info)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_screening_info: {str(e)}")
            raise

        return new_screening_info


    async def get_screening_info_by_id(self, id):
        try:
            async with self.db_session() as session:
                stmt = select(ScreeningInfo).where(ScreeningInfo.id == id)
                result = await session.execute(stmt)
                info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_screening_info_by_id: {str(e)}")
            raise

        return info
    
    async def update_screening_info_by_id(self, id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(ScreeningInfo).where(ScreeningInfo.id == id)
                result = await session.execute(stmt)
                info = result.scalars().first()

                if not info:
                    return False

                await info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_screening_info_by_id: {str(e)}")
            raise

        return True