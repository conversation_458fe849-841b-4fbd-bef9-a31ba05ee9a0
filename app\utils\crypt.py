from cryptography.hazmat.primitives import hashes, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
import os, base64

class Crypt:

    @classmethod
    def generate_key(cls, password, salt=None, length=16):
        if salt is None:
            salt = os.urandom(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=length,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        key = kdf.derive(password.encode())
        encode_key = base64.urlsafe_b64encode(key).decode('utf-8')
        return encode_key

    @classmethod
    def encrypt(cls, plaintext, key):
        key = base64.urlsafe_b64decode(key)
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(plaintext.encode()) + padder.finalize()
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()
        return base64.urlsafe_b64encode(iv + ciphertext).decode('utf-8')

    @classmethod
    def decrypt(cls, ciphertext, key):
        ciphertext = base64.urlsafe_b64decode(ciphertext)
        key = base64.urlsafe_b64decode(key)
        iv = ciphertext[:16]
        actual_ciphertext = ciphertext[16:]
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        decryptor = cipher.decryptor()
        padded_plaintext = decryptor.update(actual_ciphertext) + decryptor.finalize()
        unpadder = padding.PKCS7(128).unpadder()
        plaintext = unpadder.update(padded_plaintext) + unpadder.finalize()
        return plaintext.decode('utf-8')

    @classmethod
    def encode_for_url(cls, data):
        return base64.urlsafe_b64encode(data).decode('utf-8')