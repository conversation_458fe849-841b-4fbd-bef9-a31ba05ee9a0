from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.improve_learning_result import ImproveLearningResult

class ImproveLearningResultService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_improve_learning_result(self, new_improve_learning_result):
        try:
            async with self.db_session() as session:
                session.add(new_improve_learning_result)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_improve_learning_result: {str(e)}")
            raise

        return new_improve_learning_result

    async def get_improve_learning_result_by_id(self, il_result_id):
        try:
            async with self.db_session() as session:
                stmt = select(ImproveLearningResult).where(ImproveLearningResult.il_result_id == il_result_id)
                result = await session.execute(stmt)
                qs_result_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_improve_learning_result_by_id: {str(e)}")
            raise

        return qs_result_info

    async def update_result_by_id(self, il_result_id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(ImproveLearningResult).where(ImproveLearningResult.il_result_id == il_result_id)
                result = await session.execute(stmt)
                result_info = result.scalars().first()

                if not result_info:
                    return False

                await result_info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_result_by_id: {str(e)}")
            raise

        return True