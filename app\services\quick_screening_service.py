from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta
import numpy as np

from app.services.quick_screening_question_service import QuickScreeningQuestionService
from app.services.quick_screening_answer_service import QuickScreeningAnswerService
from app.services.quick_screening_result_service import QuickScreeningResultService
from app.services.user_service import UserService
from app.models.quick_screening_result import QuickScreeningResult
from app.models.quick_screening_answer import QuickScreeningAnswer
from app.core.database import get_db
from app.core.logger import main_logger
from app.core.global_var import GlobalVar

class QuickScreeningService:

    def __init__(self) -> None:
        self.db_session = get_db

    # 获取快筛列表
    async def get_list(self):

        question_service = QuickScreeningQuestionService()

        emotion_survey_question_list = await question_service.get_list_by_part("emotion_survey")
        visual_survey_question_list = await question_service.get_list_by_part("visual_survey")
        cognitive_survey_question_list = await question_service.get_list_by_part("cognitive_survey")

        info_list = {}

        # option_json = {"option_list": ["opt1": "xxx", "opt2": "yyy"], "type": "single_choice"}
        # key = "emotion_survey"
        # type = "choice"
        emotion_survey_list = []
        for value in emotion_survey_question_list:
            option_list = value.option_json["option_list"]
            new_option_list = {}
            for opt, opt_name in option_list.items():
                new_option_list[opt] = _(opt_name)
            value.option_json["option_list"] = new_option_list
            info = {
                "question_id": value.question_id,
                "title": _(value.title),
                "desc": value.desc,
                "category": value.category,
                "key": value.key,
                "type": value.type,
                "status": value.status,
                "option_info": value.option_json,
            }
            emotion_survey_list.append(info)

        info_list["emotion_survey"] = emotion_survey_list

        visual_survey_list = []
        for value in visual_survey_question_list:
            content_json = value.content_json
            if content_json:
                for key, content in enumerate(content_json):
                    if content.get("type") == "text":
                        content["content"] = _(content["content"])
                        content_json[key] = content
            if value.title != "":
                value.title = _(value.title)
            option_type = value.option_json["type"]
            option_list = value.option_json["option_list"]
            new_option_list = {}
            if option_type == "single_choice":
                for opt, opt_name in option_list.items():
                    new_option_list[opt] = _(opt_name)
                value.option_json["option_list"] = new_option_list
            elif option_type == "single_media_choice":
                pass
            info = {
                "question_id": value.question_id,
                "title": value.title,
                "desc": value.desc,
                "content_info": content_json,
                "category": value.category,
                "key": value.key,
                "type": value.type,
                "status": value.status,
                "option_info": value.option_json,
            }
            visual_survey_list.append(info)
        
        info_list["visual_survey"] = visual_survey_list

        cognitive_survey_list = []
        for value in cognitive_survey_question_list:
            content_json = value.content_json
            start_seq = content_json.get("start_seq", 2)
            end_seq = content_json.get("end_seq", 3)
            if value.title != "":
                value.title = _(value.title)
            info = {
                "question_id": value.question_id,
                "title": value.title,
                "desc": value.desc,
                "content_info": content_json,
                "category": value.category,
                "key": value.key,
                "type": value.type,
                "status": value.status,
            }
            cognitive_survey_list.append(info)

        info_list["cognitive_survey"] = cognitive_survey_list

        return info_list

    # 处理提交的回答
    # collect_answer = {
    #     "emotion_survey": [
    #         {"question_id": 123, "type": "choice", "answer": ["opt1"]},
    #         {"question_id": 124, "type": "choice", "answer": ["opt1"]},
    #     ],
    # }
    async def answer_question(self, collect_answer, user_id):
        
        emotion_survey = collect_answer.get("emotion_survey", None)
        if not emotion_survey:
            return {"code": 10206, "msg": _("提交数据不存在")}
        visual_survey = collect_answer.get("visual_survey", None)
        if not visual_survey:
            return {"code": 10207, "msg": _("提交数据不存在")}
        cognitive_survey = collect_answer.get("cognitive_survey", None)
        if not cognitive_survey:
            return {"code": 10208, "msg": _("提交数据不存在")}

        answer_json = {}
        emotion_survey_answer_json =  await self.handle_emotion_survey_result(emotion_survey)
        answer_json["emotion_survey"] = emotion_survey_answer_json

        visual_survey_answer_json = await self.handle_visual_survey_result(visual_survey)
        answer_json["visual_survey"] = visual_survey_answer_json
    
        cognitive_survey_answer_json = await self.handle_cognitive_survey_result(cognitive_survey)
        answer_json["cognitive_survey"] = cognitive_survey_answer_json

        result_service = QuickScreeningResultService()

        new_result = QuickScreeningResult(
            user_id=user_id,
            result_json=answer_json,
            report_json={},
            extra_json={},
        )
        new_result_info = await result_service.new_quick_screening_result(new_result)
        qs_result_id = new_result_info.qs_result_id
        print("qs_result_id: " + str(qs_result_id))

        await self.handle_emotion_survey_answer(emotion_survey_answer_json, qs_result_id, user_id)
        await self.handle_visual_survey_answer(visual_survey_answer_json, qs_result_id, user_id)
        await self.handle_cognitive_survey_answer(cognitive_survey_answer_json, qs_result_id, user_id)

        await self.gen_report_data(qs_result_id)

        return {"code": 0, "msg": "", "data": {"qs_result_id": qs_result_id}}
        
    async def get_report(self, qs_result_id, user_id):

        result_service = QuickScreeningResultService()
        user_service = UserService()

        result_info = await result_service.get_quick_screening_result_by_id(qs_result_id)
        if not result_info:
            return {"code": 10206, "msg": _("获取报告失败")}
        report_json_info = result_info.report_json
        if not report_json_info:
            return {"code": 10207, "msg": _("获取报告失败")}
        if result_info.user_id != user_id:
            return {"code": 10208, "msg": _("获取报告失败")}

        user_info = report_json_info["user_info"]
        user_age = user_info["age"]
        user_name = user_info["name"]
        user_gender = _("男") if user_info["gender"] == 1 else _("女")
        user_grade = user_service.convert_grade_to_text(user_info["grade"])
        
        return_report_info = {
            "version": report_json_info["version"],
            "user_age": user_age,
            "user_name": user_name,
            "user_gender": user_gender,
            "user_grade": user_grade,
            "emotion_survey": report_json_info["emotion_survey"],
            "visual_survey": report_json_info["visual_survey"],
        }

        return {"code": 0, "msg": "", "data": return_report_info}

    async def gen_report_data(self, qs_result_id):
        
        result_service = QuickScreeningResultService()
        user_service = UserService()
        qs_result_info = await result_service.get_quick_screening_result_by_id(qs_result_id)

        report_json = {}

        result_json = qs_result_info.result_json
        emotion_survey_result = result_json.get("emotion_survey")
        emotion_survey_report_json = self.gen_emotion_survey_report_data(emotion_survey_result)
        report_json["emotion_survey"] = emotion_survey_report_json

        visual_survey_result = result_json.get("visual_survey")
        visual_survey_report_json = self.gen_visual_survey_report_data(visual_survey_result)
        report_json["visual_survey"] = visual_survey_report_json

        cognitive_survey_result = result_json.get("cognitive_survey")
        cognitive_survey_report_json = self.gen_cognitive_survey_report_data(cognitive_survey_result)
        report_json["cognitive_survey"] = cognitive_survey_report_json

        user_info = await user_service.get_user_by_user_id(qs_result_info.user_id)
        age = user_service.get_age_by_birthday(user_info["birthday"])

        report_json["version"] = 1
        report_json["report_time"] = int(datetime.utcnow().timestamp() * 1000) #报告生成时间
        report_json["report_lang"] = GlobalVar.get_var("language")
        report_json["user_info"] = {"name": user_info["name"], "age": age, "gender": user_info["gender"], "grade": user_info["grade"]}

        update_data = {
            "report_json": report_json
        }
        await result_service.update_result_by_id(qs_result_id, update_data)

        return True

    def gen_emotion_survey_report_data(self, emotion_survey_result):
        if not emotion_survey_result:
            return {}
        
        total_score = 0.0
        for question_id, answer in emotion_survey_result.items():
            total_score += answer["score"]

        emotion_level = ""
        emotion_level_desc = ""
        if total_score >= 0 and total_score < 6:
            emotion_level = _("无明显情绪障碍")
            emotion_level_desc = _("情绪状态健康，没有显著问题。")
        elif total_score >= 6 and total_score < 11:
            emotion_level = _("轻度情绪障碍")
            emotion_level_desc = _("存在轻微焦虑情绪，但未严重影响日常生活。")
        elif total_score >= 11 and total_score < 16:
            emotion_level = _("中度情绪障碍")
            emotion_level_desc = _("情绪问题可能已开始影响学习、社交或生活质量。")
        elif total_score >= 16 and total_score < 21:
            emotion_level = _("重度情绪障碍")
            emotion_level_desc = _("情绪问题严重，可能对日常生活造成较大影响。")

        emotion_report_json = {
            "score": total_score,
            "emotion_level": emotion_level,
            "emotion_level_desc": emotion_level_desc,
        }
        return emotion_report_json

    async def handle_emotion_survey_answer(self, emotion_survey_answer_json, qs_result_id, user_id):
        
        answer_service = QuickScreeningAnswerService()
        for key, value in emotion_survey_answer_json.items():
            new_answer = QuickScreeningAnswer(
                question_id=key,
                user_id=user_id,
                qs_result_id=qs_result_id,
                answer_json=value,
                extra_json={}
            )
            new_answer_info = await answer_service.new_quick_screening_answer(new_answer)

    async def handle_emotion_survey_result(self, emotion_survey):

        answer_list_dict = {}
        for value in emotion_survey:
            answer_list_dict[value["question_id"]] = value
        
        question_service = QuickScreeningQuestionService()

        total_score = []
        answer_json = {}
        emotion_survey_question_list = await question_service.get_list_by_part("emotion_survey")
        for value in emotion_survey_question_list:
            answer_info = answer_list_dict.get(value.question_id, None)
            if not answer_info:
                total_score.append(0.0)
                answer_json[value.question_id] = {"answer": None, "score": 0.0}
                continue

            answer = answer_info.get("answer", [])
            if not answer:
                total_score.append(0.0)
                answer_json[value.question_id] = {"answer": None, "score": 0.0}
                continue
            answer_0 = answer[0]

            # 情绪障碍筛查得分计算
            score = 0.0
            if answer_0 == "opt1":
                score = 0.0
            elif answer_0 == "opt2":
                score = 1.0
            elif answer_0 == "opt3":
                score = 2.0
            total_score.append(score)
            answer_json[value.question_id] = {"answer": answer, "score": score}

        return answer_json
    
    async def handle_visual_survey_result(self, visual_survey):

        answer_list_dict = {}
        for value in visual_survey:
            answer_list_dict[value["question_id"]] = value
        
        question_service = QuickScreeningQuestionService()

        total_score = []
        answer_json = {}
        visual_survey_question_list = await question_service.get_list_by_part("visual_survey")
        for value in visual_survey_question_list:
            answer_info = answer_list_dict.get(value.question_id, None)
            if not answer_info:
                total_score.append(0.0)
                answer_json[value.question_id] = {"answer": None, "score": 0.0, "category": value.category}
                continue

            answer = answer_info.get("answer", [])
            if not answer:
                total_score.append(0.0)
                answer_json[value.question_id] = {"answer": None, "score": 0.0, "category": value.category}
                continue
            answer_0 = answer[0]

            score = 0.0
            if value.key == "visual_survey_answer":
                ref_answer = value.ref_answer_json
                if ref_answer.get("answer") != answer_0:
                    score = 1.0
            else:
                if answer_0 == "opt1":
                    score = 0.0
                elif answer_0 == "opt2":
                    score = 1.0
                elif answer_0 == "opt3":
                    score = 2.0
            total_score.append(score)
            answer_json[value.question_id] = {"answer": answer, "score": score, "category": value.category}

        return answer_json
    
    async def handle_visual_survey_answer(self, visual_survey_answer_json, qs_result_id, user_id):
        
        answer_service = QuickScreeningAnswerService()
        for key, value in visual_survey_answer_json.items():
            new_answer = QuickScreeningAnswer(
                question_id=key,
                user_id=user_id,
                qs_result_id=qs_result_id,
                answer_json=value,
                extra_json={}
            )
            new_answer_info = await answer_service.new_quick_screening_answer(new_answer)

    def gen_visual_survey_report_data(self, visual_survey_result):
        if not visual_survey_result:
            return {}
        
        total_score = {}
        for question_id, answer in visual_survey_result.items():
            cateory = answer.get("category", "")
            total_score[cateory] = total_score.get(cateory, 0) + answer["score"]

        risk_list = {
            "myopia": {
                "level": [
                    {"level": "low", "name": _("低风险"), "score_range": [0, 0], "suggestion": _("视力整体状况良好，无需过度担心，建议您保持良好的用眼习惯并定期检查。")},
                    {"level": "moderate", "name": _("中风险"), "score_range": [1, 2], "suggestion": _("建议您密切关注用眼状况，适度减少电子产品使用时间，保持良好用眼姿势，并定期进行视力检查；如有疑虑，请及时咨询专业眼科医生。")},
                    {"level": "high", "name": _("高风险"), "score_range": [3, 4], "suggestion": _("建议您进行进一步的专业诊断，如有必要应及时配镜或接受其他医学干预，并加强日常护眼措施。")}
                ],
                "name": _("近视"),
            },
            "amblyopia": {
                "level": [
                    {"level": "low", "name": _("低风险"), "score_range": [0, 0], "suggestion": _("建议您保持良好的用眼习惯，并定期进行视力检查，如有任何疑虑可随时咨询专业医生。")},
                    {"level": "moderate", "name": _("中风险"), "score_range": [1, 2], "suggestion": _("建议您及时关注用眼状况，保持正确的读写姿势和用眼距离，并考虑进一步检查或咨询专业眼科医生以获得专业建议。")},
                    {"level": "high", "name": _("高风险"), "score_range": [3, 4], "suggestion": _("建议您尽快就医进行专业检查及诊断，必要时接受相应的治疗或康复训练，并在日常生活中更加注意用眼卫生。")}
                ],
                "name": _("弱视"),
            },
            "stereopsis": {
                "level": [
                    {"level": "low", "name": _("低风险"), "score_range": [0, 0], "suggestion": _("基本无明显问题，日常无需过度担心，可定期复查。")},
                    {"level": "moderate", "name": _("中风险"), "score_range": [1, 2], "suggestion": _("建议关注用眼情况，若有不适或疑虑可尽早咨询专业眼科医生。")},
                    {"level": "high", "name": _("高风险"), "score_range": [3, 4], "suggestion": _("立体视功能可能有较明显异常，建议及时就诊以确认原因并进行专业矫正或干预。")}
                ],
                "name": _("立体视"),
            },
            "color_blindness": {
                "level": [
                    {"level": "low", "name": _("低风险"), "score_range": [0, 3], "suggestion": _("建议您继续保持健康的用眼习惯，并定期进行相关检查，如有疑虑可随时咨询专业医生。")},
                    {"level": "moderate", "name": _("中风险"), "score_range": [4, 5], "suggestion": _("建议您在日常生活中多加留意色彩辨识，必要时可做进一步检查，以便在学习或工作中做好相应的调整或适应。")},
                    {"level": "high", "name": _("高风险"), "score_range": [6, 6], "suggestion": _("建议您尽快就医进行专业诊断，并在日常生活、学习或工作中结合实际情况进行辅助和适应性调整，必要时可求助专业人士。")}
                ],
                "name": _("色盲"),
            },
        }
        visual_risk_list = []
        total_score_num = 0.0
        for category, risk in risk_list.items():
            score = total_score.get(category, 0.0)
            total_score_num += score
            for level_risk in risk["level"]:
                if score >= level_risk["score_range"][0] and score <= level_risk["score_range"][1]:
                    level_risk["score"] = score
                    level_risk["category_name"] = risk["name"]
                    level_risk["category"] = category
                    visual_risk_list.append(level_risk)

        # visual_suggestion = [
        #     _("在日常生活中注意用眼卫生与护眼措施。"),
        #     _("如果有任何不适或疑问，请随时咨询专业眼科医生。"),
        # ]

        visual_report_json = {
            "score": total_score_num,
            "visual_risk_list": visual_risk_list,
            # "visual_suggestion": visual_suggestion,
        }
        return visual_report_json
    
    async def handle_cognitive_survey_result(self, cognitive_survey):
        answer_list_dict = {}
        for value in cognitive_survey:
            answer_list_dict[value["question_id"]] = value
        
        question_service = QuickScreeningQuestionService()

        total_score = []
        answer_json = {}
        cognitive_survey_question_list = await question_service.get_list_by_part("cognitive_survey")
        for value in cognitive_survey_question_list:
            answer_info = answer_list_dict.get(value.question_id, None)
            if not answer_info:
                total_score.append(0.0)
                answer_json[value.question_id] = {"answer": None, "score": 0.0, "memory_score": 0.0, "reaction_score": 0.0}
                continue

            answer = answer_info.get("answer", None)
            if not answer:
                total_score.append(0.0)
                answer_json[value.question_id] = {"answer": None, "score": 0.0, "memory_score": 0.0, "reaction_score": 0.0}
                continue

            answer_sample = {
                "start_seq": 2,
                "max_seq": 3,
                "end_seq": 3,
                "highest_seq": 3,
                "start_time": 0,
                "end_time": 0,
                "environment": "demo",
                "detail_info": [
                    {
                        "seq": [1,2],
                        "start_time": 0,
                        "end_time": 0,
                        "click": [
                            {"seq":1, "time": 0, "is_right": True},
                            {"seq":2, "time": 0, "is_right": True},
                        ],
                        "is_finish": 1,
                    },
                    {
                        "seq": [1,2,3],
                        "start_time": 0,
                        "end_time": 0,
                        "click": [
                            {"seq":1, "time": 0, "is_right": True},
                            {"seq":3, "time": 0, "is_right": False},
                        ],
                        "is_finish": 0,
                    },
                    {
                        "seq": [2,3],
                        "start_time": 0,
                        "end_time": 0,
                        "click": [
                            {"seq":2, "time": 0, "is_right": True},
                            {"seq":3, "time": 0, "is_right": True},
                        ],
                        "is_finish": 1,
                    },
                    {
                        "seq": [2,3,6],
                        "start_time": 0,
                        "end_time": 0,
                        "click": [
                            {"seq":2, "time": 0, "is_right": True},
                            {"seq":3, "time": 0, "is_right": True},
                            {"seq":6, "time": 0, "is_right": True},
                        ],
                        "is_finish": 1,
                    },
                ],
                "is_finish": 1,
            }

            environment = answer.get("environment", "demo")
            if environment == "demo":
                answer_json[value.question_id] = {"answer": answer, "score": 0.0, "memory_score": 0.0, "reaction_score": 0.0}
                continue

            score = 0.0
            memory_score = 0.0 # 记忆力
            reaction_score = 0.0 # 反应时间

            max_seq = answer.get("max_seq", 1)
            highest_seq = answer.get("highest_seq", 0)
            memory_score = highest_seq / max_seq * 100

            answer_detail_info = answer.get("detail_info", [])
            all_reaction_times = []
            for detail_info in answer_detail_info:
                click_list = detail_info.get("click", [])
                if not click_list:
                    continue
                last_time = detail_info["start_time"]
                for click_info in click_list:
                    if not click_info.get("is_right", False):
                        break
                    reaction_time = click_info["time"] - last_time
                    last_time = click_info["time"]
                    all_reaction_times.append(reaction_time)
            
            np_all_reaction_times = np.array(all_reaction_times)
            reaction_times_mean = np_all_reaction_times.mean()
            if reaction_times_mean < 1500:
                reaction_score = 100
            elif 1500 <= reaction_times_mean < 2500:
                reaction_score = 80
            elif 2500 <= reaction_times_mean < 3500:
                reaction_score = 60
            elif 3500 <= reaction_times_mean < 4500:
                reaction_score = 40
            else:
                reaction_score = 20

            total_score.append(score)
            answer_json[value.question_id] = {"answer": answer, "score": score, "memory_score": memory_score, "reaction_score": reaction_score}
        
        return answer_json

    async def handle_cognitive_survey_answer(self, cognitiv_survey_answer_json, qs_result_id, user_id):
        answer_service = QuickScreeningAnswerService()
        for key, value in cognitiv_survey_answer_json.items():
            new_answer = QuickScreeningAnswer(
                question_id=key,
                user_id=user_id,
                qs_result_id=qs_result_id,
                answer_json=value,
                extra_json={}
            )
            new_answer_info = await answer_service.new_quick_screening_answer(new_answer)

    def gen_cognitive_survey_report_data(self, cognitive_survey_result):
        return {}
