from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.quick_screening_questions import QuickScreeningQuestion

class QuickScreeningQuestionService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_question(self, new_question):
        try:
            async with self.db_session() as session:
                session.add(new_question)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_question: {str(e)}")
            raise

        return new_question

    async def get_list_by_part(self, part):
        try:
            async with self.db_session() as session:
                stmt = select(QuickScreeningQuestion).where(QuickScreeningQuestion.part == part)
                result = await session.execute(stmt)
                question_list = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_list_by_part: {str(e)}")
            raise

        return question_list

    async def get_list_by_question_ids(self, question_ids):
        try:
            async with self.db_session() as session:
                conditions = [
                    QuickScreeningQuestion.question_id.in_(question_ids),
                ]
                stmt = select(QuickScreeningQuestion).where(and_(*conditions))
                result = await session.execute(stmt)
                question_list = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_list_by_question_ids: {str(e)}")
            raise

        return question_list