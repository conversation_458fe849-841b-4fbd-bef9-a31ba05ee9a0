import aioredis
from aioredis import Redis
from typing import Any, Optional, Callable
import asyncio
from contextlib import asynccontextmanager
import os
from functools import partial

class AsyncRedisClient:
    def __init__(self):
        REDIS_URL = os.getenv("REDIS_URL")
        self.redis_url = REDIS_URL
        self.password = None
        self.pool_size = 10
        self._redis_pool: Optional[Redis] = None
        self._wrapped_methods: dict = {}
    
    async def init_pool(self) -> None:
        """初始化 Redis 连接池"""
        if self._redis_pool is None:
            self._redis_pool = await aioredis.from_url(
                self.redis_url,
                password=self.password,
                max_connections=self.pool_size,
                encoding="utf-8",
                decode_responses=True
            )
    
    async def close(self) -> None:
        """关闭 Redis 连接池"""
        if self._redis_pool:
            await self._redis_pool.close()
            self._redis_pool = None
    
    @asynccontextmanager
    async def get_connection(self) -> Redis:
        """获取 Redis 连接的上下文管理器"""
        if not self._redis_pool:
            await self.init_pool()
        try:
            yield self._redis_pool
        except Exception as e:
            print(f"Redis operation error: {str(e)}")
            raise
    
    async def command(self, command: str, *args, **kwargs):
        async with self.get_connection() as redis:
            try:
                response = await redis.execute_command(command, *args, **kwargs)
            except Exception as e:
                print(f"operation failed: {str(e)}")
                raise
        return response

    # async def __getattr__(self, name: str) -> Callable:
    #     """
    #     动态代理所有Redis命令
    #     通过魔术方法__getattr__实现方法调用的动态转发
    #     """
    #     if name not in self._wrapped_methods:
    #         async def wrapper(*args, **kwargs):
    #             if not self._redis_pool:
    #                 await self.init_pool()
    #             try:
    #                 redis_method = getattr(self._redis_pool, name)
    #                 if not asyncio.iscoroutinefunction(redis_method):
    #                     # 如果不是异步方法，转换为异步执行
    #                     return await asyncio.to_thread(partial(redis_method, *args, **kwargs))
    #                 return await redis_method(*args, **kwargs)
    #             except Exception as e:
    #                 print(f"Redis operation '{name}' failed: {str(e)}")
    #                 raise
    #         self._wrapped_methods[name] = wrapper
    #     return self._wrapped_methods[name]
