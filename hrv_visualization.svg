<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; fill: #666; }
      .label { font-family: Arial, sans-serif; font-size: 12px; fill: #333; }
      .axis { stroke: #ccc; stroke-width: 1; }
      .grid { stroke: #f0f0f0; stroke-width: 0.5; }
      .ppg-signal { stroke: #2196F3; stroke-width: 2; fill: none; }
      .peaks { fill: #FF5722; }
      .rr-bars { fill: #4CAF50; }
      .box { fill: #f8f9fa; stroke: #dee2e6; stroke-width: 1; }
      .arrow { stroke: #666; stroke-width: 2; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" class="title">HRV算法实现流程与数据可视化</text>
  
  <!-- Data Collection Section -->
  <g transform="translate(50, 60)">
    <rect class="box" width="280" height="120" rx="5"/>
    <text x="140" y="20" text-anchor="middle" class="subtitle">1. 数据采集</text>
    <text x="20" y="40" class="label">• RGB视频帧采集</text>
    <text x="20" y="55" class="label">• POS算法提取PPG信号</text>
    <text x="20" y="70" class="label">• 带通滤波 (0.8-2.5Hz)</text>
    <text x="20" y="85" class="label">• 重采样至30Hz</text>
    <text x="20" y="100" class="label">• 数据格式: [timestamp, R, G, B]</text>
  </g>
  
  <!-- Arrow 1 -->
  <line x1="350" y1="120" x2="420" y1="120" class="arrow" />
  
  <!-- Signal Processing Section -->
  <g transform="translate(440, 60)">
    <rect class="box" width="280" height="120" rx="5"/>
    <text x="140" y="20" text-anchor="middle" class="subtitle">2. 信号处理</text>
    <text x="20" y="40" class="label">• PPG峰值检测 (neurokit2)</text>
    <text x="20" y="55" class="label">• RR间期计算</text>
    <text x="20" y="70" class="label">• 异常值过滤 (300-2000ms)</text>
    <text x="20" y="85" class="label">• 时域/频域HRV指标</text>
    <text x="20" y="100" class="label">• SDNN, RMSSD, LF, HF等</text>
  </g>
  
  <!-- Arrow 2 -->
  <line x1="740" y1="120" x2="810" y1="120" class="arrow" />
  
  <!-- Health Assessment Section -->
  <g transform="translate(830, 60)">
    <rect class="box" width="280" height="120" rx="5"/>
    <text x="140" y="20" text-anchor="middle" class="subtitle">3. 健康评估</text>
    <text x="20" y="40" class="label">• 自主神经平衡 (LF/HF)</text>
    <text x="20" y="55" class="label">• 身体压力指数</text>
    <text x="20" y="70" class="label">• 心理压力指数</text>
    <text x="20" y="85" class="label">• 抗压能力评估</text>
    <text x="20" y="100" class="label">• 综合健康评分</text>
  </g>
  
  <!-- PPG Signal Visualization -->
  <g transform="translate(50, 220)">
    <text x="300" y="20" text-anchor="middle" class="subtitle">PPG信号与峰值检测</text>
    
    <!-- Axes -->
    <line x1="50" y1="150" x2="550" y2="150" class="axis"/>
    <line x1="50" y1="30" x2="50" y2="150" class="axis"/>
    
    <!-- Grid lines -->
    <line x1="150" y1="30" x2="150" y2="150" class="grid"/>
    <line x1="250" y1="30" x2="250" y2="150" class="grid"/>
    <line x1="350" y1="30" x2="350" y2="150" class="grid"/>
    <line x1="450" y1="30" x2="450" y2="150" class="grid"/>
    
    <!-- PPG Signal curve -->
    <path d="M 50,100 Q 100,70 150,90 Q 200,60 250,80 Q 300,50 350,70 Q 400,40 450,60 Q 500,30 550,50" 
          class="ppg-signal"/>
    
    <!-- Peak markers -->
    <circle cx="150" cy="90" r="4" class="peaks"/>
    <circle cx="250" cy="80" r="4" class="peaks"/>
    <circle cx="350" cy="70" r="4" class="peaks"/>
    <circle cx="450" cy="60" r="4" class="peaks"/>
    
    <!-- Labels -->
    <text x="300" y="170" text-anchor="middle" class="label">时间 (秒)</text>
    <text x="30" y="90" text-anchor="middle" class="label" transform="rotate(-90, 30, 90)">振幅</text>
    
    <!-- RR Intervals -->
    <line x1="150" y1="180" x2="250" y2="180" stroke="#4CAF50" stroke-width="2"/>
    <line x1="250" y1="180" x2="350" y2="180" stroke="#4CAF50" stroke-width="2"/>
    <line x1="350" y1="180" x2="450" y2="180" stroke="#4CAF50" stroke-width="2"/>
    <text x="200" y="195" text-anchor="middle" class="label">RR1</text>
    <text x="300" y="195" text-anchor="middle" class="label">RR2</text>
    <text x="400" y="195" text-anchor="middle" class="label">RR3</text>
  </g>
  
  <!-- HRV Metrics Visualization -->
  <g transform="translate(650, 220)">
    <text x="250" y="20" text-anchor="middle" class="subtitle">HRV指标分布</text>
    
    <!-- SDNN Bar -->
    <rect x="50" y="40" width="80" height="15" fill="#2196F3"/>
    <text x="140" y="52" class="label">SDNN: 45.2ms</text>
    
    <!-- RMSSD Bar -->
    <rect x="50" y="65" width="120" height="15" fill="#4CAF50"/>
    <text x="180" y="77" class="label">RMSSD: 32.8ms</text>
    
    <!-- LF Bar -->
    <rect x="50" y="90" width="95" height="15" fill="#FF9800"/>
    <text x="155" y="102" class="label">LF: 1200ms²</text>
    
    <!-- HF Bar -->
    <rect x="50" y="115" width="110" height="15" fill="#9C27B0"/>
    <text x="170" y="127" class="label">HF: 800ms²</text>
    
    <!-- LF/HF Ratio -->
    <rect x="50" y="140" width="60" height="15" fill="#FF5722"/>
    <text x="120" y="152" class="label">LF/HF: 1.5</text>
    
    <!-- Health Scores -->
    <g transform="translate(0, 180)">
      <text x="125" y="10" text-anchor="middle" class="label">健康评估结果</text>
      
      <!-- Stress Resistance Circle -->
      <circle cx="75" cy="40" r="25" fill="none" stroke="#4CAF50" stroke-width="4"/>
      <circle cx="75" cy="40" r="25" fill="none" stroke="#e0e0e0" stroke-width="4" 
              stroke-dasharray="110 47" transform="rotate(-90, 75, 40)"/>
      <text x="75" y="45" text-anchor="middle" class="label">抗压 75%</text>
      
      <!-- Mental Pressure Circle -->
      <circle cx="175" cy="40" r="25" fill="none" stroke="#FF9800" stroke-width="4"/>
      <circle cx="175" cy="40" r="25" fill="none" stroke="#e0e0e0" stroke-width="4" 
              stroke-dasharray="78 79" transform="rotate(-90, 175, 40)"/>
      <text x="175" y="45" text-anchor="middle" class="label">心理 50%</text>
      
      <!-- Physical Pressure Circle -->
      <circle cx="275" cy="40" r="25" fill="none" stroke="#2196F3" stroke-width="4"/>
      <circle cx="275" cy="40" r="25" fill="none" stroke="#e0e0e0" stroke-width="4" 
              stroke-dasharray="47 110" transform="rotate(-90, 275, 40)"/>
      <text x="275" y="45" text-anchor="middle" class="label">身体 30%</text>
    </g>
  </g>
  
  <!-- Algorithm Flow -->
  <g transform="translate(50, 520)">
    <text x="550" y="20" text-anchor="middle" class="subtitle">核心算法流程</text>
    
    <!-- Step boxes -->
    <rect class="box" x="0" y="30" width="150" height="80" rx="5"/>
    <text x="75" y="50" text-anchor="middle" class="label">RGB采集</text>
    <text x="75" y="65" text-anchor="middle" class="label">POS算法</text>
    <text x="75" y="80" text-anchor="middle" class="label">信号提取</text>
    
    <rect class="box" x="200" y="30" width="150" height="80" rx="5"/>
    <text x="275" y="50" text-anchor="middle" class="label">带通滤波</text>
    <text x="275" y="65" text-anchor="middle" class="label">0.8-2.5Hz</text>
    <text x="275" y="80" text-anchor="middle" class="label">去噪处理</text>
    
    <rect class="box" x="400" y="30" width="150" height="80" rx="5"/>
    <text x="475" y="50" text-anchor="middle" class="label">峰值检测</text>
    <text x="475" y="65" text-anchor="middle" class="label">RR间期</text>
    <text x="475" y="80" text-anchor="middle" class="label">异常过滤</text>
    
    <rect class="box" x="600" y="30" width="150" height="80" rx="5"/>
    <text x="675" y="50" text-anchor="middle" class="label">HRV分析</text>
    <text x="675" y="65" text-anchor="middle" class="label">时频域</text>
    <text x="675" y="80" text-anchor="middle" class="label">指标计算</text>
    
    <rect class="box" x="800" y="30" width="150" height="80" rx="5"/>
    <text x="875" y="50" text-anchor="middle" class="label">健康评估</text>
    <text x="875" y="65" text-anchor="middle" class="label">压力分析</text>
    <text x="875" y="80" text-anchor="middle" class="label">综合评分</text>
    
    <!-- Arrows between steps -->
    <line x1="160" y1="70" x2="190" y1="70" class="arrow" />
    <line x1="360" y1="70" x2="390" y1="70" class="arrow" />
    <line x1="560" y1="70" x2="590" y1="70" class="arrow" />
    <line x1="760" y1="70" x2="790" y1="70" class="arrow" />
  </g>
  
  <!-- Technical Details -->
  <g transform="translate(50, 650)">
    <text x="550" y="20" text-anchor="middle" class="subtitle">技术实现细节</text>
    
    <text x="50" y="40" class="label">• 采样频率: 30Hz (重采样处理)</text>
    <text x="50" y="55" class="label">• 滤波器: 2阶巴特沃斯带通滤波器</text>
    <text x="50" y="70" class="label">• 峰值检测: neurokit2库PPG峰值算法</text>
    <text x="50" y="85" class="label">• RR间期范围: 300-2000ms (过滤异常值)</text>
    
    <text x="600" y="40" class="label">• 时域指标: SDNN, RMSSD, pNN50等</text>
    <text x="600" y="55" class="label">• 频域指标: LF, HF, LF/HF比值</text>
    <text x="600" y="70" class="label">• 评估算法: 多指标加权计算</text>
    <text x="600" y="85" class="label">• 一致性处理: 随机扰动保持稳定性</text>
  </g>
  
  <!-- Footer -->
  <text x="600" y="780" text-anchor="middle" class="label" style="font-style: italic;">
    基于rPPG技术的非接触式心率变异性分析系统
  </text>
</svg>