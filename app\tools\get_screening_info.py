import asyncio
import json
import csv
from datetime import datetime, timedelta
import pandas as pd
import os
import dotenv

from app.services.speech_service import SpeechService
from app.services.hrv_service import HrvService
from app.core.database import get_db
from app.utils.blob import save_file_to_blob, generate_blob_sas_url
from app.services.upload_record_video_service import UploadRecordVideoService
from app.utils.crypt import Crypt

dotenv.load_dotenv()
class GetScreeningInfo:

    def __init__(self) -> None:
        pass

    async def get_screening_info(self, id):
        speech_service = SpeechService(get_db())
        screening_info = await speech_service.get_speech_by_id(id)
        audio_url_blob_name = screening_info["origin_data"]
        audio_url = generate_blob_sas_url(audio_url_blob_name)
        print(audio_url)
        print(screening_info["process_result"])

    async def get_hrv_info(self):

        hrv_service = HrvService(get_db())
        
        hrv_report_list = await hrv_service.list_hrv_report_by_range(0, 1000)

        list_info = []
        for hrv_report in hrv_report_list:
            process_result = hrv_report.process_result
            if type(process_result) != str:
                continue
            raw_data = process_result
            raw_data = raw_data.replace('\\n', '')
            raw_data = raw_data.replace('\\"', '"')
            raw_data = raw_data.strip().strip('"')
            raw_data_dict = json.loads(raw_data)

            pressureInfo = raw_data_dict["pressureInfo"]
            nerveInfo = raw_data_dict["nerveInfo"]

            info = {
                # "device_id": hrv_report.device_id,
                # "assessment_time":  hrv_report.created.strftime('%Y-%m-%d %H:%M:%S'),
                "resilience": pressureInfo["stressResistance"]["Value"],
                "psychological_stress": pressureInfo["mentalPressure"]["Value"],
                "physical_stress": pressureInfo["physicalPressure"]["Value"],
                "autonomic_balance": nerveInfo["balance"]["Value"],
                "autonomic_activity": nerveInfo["activity"]["Value"],
            }

            if info["physical_stress"] == 40:
                continue
            info["resilience"] = 100 - info["resilience"]
            info["autonomic_balance"] = (50 - abs(info["autonomic_balance"] - 50)) * 2
            list_info.append(info)

        csv_file_path = 'output.csv'
        fieldnames = list_info[0].keys()
        with open(csv_file_path, mode='w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            
            # 写入表头
            writer.writeheader()
            
            # 写入数据行
            writer.writerows(list_info)

        print(f"数据已成功导出到 {csv_file_path}")

        return list_info


    async def get_hrv_info_by_device(self, device_id, start_time, end_time):

        hrv_service = HrvService()
        upload_video_service = UploadRecordVideoService()

        print(device_id)
        hrv_report_list = await hrv_service.list_hrv_report_by_device_id([device_id], start_time, end_time, 0, 1000)
        print(hrv_report_list)

        list_info = []
        for hrv_report in hrv_report_list:
            process_result = hrv_report.process_result
            if type(process_result) != str:
                continue
            raw_data = process_result
            raw_data = raw_data.replace('\\n', '')
            raw_data = raw_data.replace('\\"', '"')
            raw_data = raw_data.strip().strip('"')
            raw_data_dict = json.loads(raw_data)

            pressureInfo = raw_data_dict["pressureInfo"]
            nerveInfo = raw_data_dict["nerveInfo"]

            video_url = ""
            video_info = await upload_video_service.get_info_by_type_id(hrv_report.idx, 'hrv')
            if video_info:
                video_url = video_info["extra_json"].get("blob_url")
                video_url = generate_blob_sas_url(video_url)

            crypt_key = os.getenv("CRYPT_KEY")
            en_screening_id = Crypt.encrypt(str(hrv_report.idx), crypt_key)
            info = {
                "device_id": hrv_report.device_id,
                "assessment_time":  (hrv_report.created + timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S'),
                "origin_data": hrv_report.origin_data,
                "video_url": video_url,
                "en_screening_idx": "https://llm.ybbywb.com/hrv_report?en_screening_id={}".format(en_screening_id)
            }

            list_info.append(info)


        csv_file_path = 'output.xlsx'
        df = pd.DataFrame(list_info)
        df.to_excel(csv_file_path, index=False)

        print(f"数据已成功导出到 {csv_file_path}")

        return list_info


    def get_blob_url(self, target):
        return generate_blob_sas_url(target)

async def main():
    tools = GetScreeningInfo()
    # id = 237
    # await tools.get_screening_info(id)

    # target = "upload_video/20250213/380.webm"
    # url = tools.get_blob_url(target)
    # print(url)

    # hrv_info = await tools.get_hrv_info()
    # print(hrv_info)

    device_id = "37101fa3a8c1db5a"
    time_format = "%Y-%m-%d %H:%M:%S"
    start_time = "2025-02-19 00:00:00"
    start_time = datetime.strptime(start_time, time_format)
    end_time = "2025-02-20 00:00:00"
    end_time = datetime.strptime(end_time, time_format)
    await tools.get_hrv_info_by_device(device_id, start_time, end_time)

if __name__ == "__main__":
    asyncio.run(main())
