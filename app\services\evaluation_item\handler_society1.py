
# 社会认知评估项目1处理
class HandlerSociety1:
    def __init__(self) -> None:
        pass

    # 通过收集评测的数据，计算需要的报告等数据
    def cal_result_info(self, collect_data):
        collect_data_sample = {
            "start_time": "",
            "run_data": [ # 每轮数据
                {
                    "cost_time": "",
                    "question_desc": "", # 情景描述
                    "is_right": 1, # 情绪判断是否正确
                },
            ],
            "collect_type": 0, # 操作方式：0点击
            "total_score": 5,
            "cost_time": "",
        }

    # 根据语音，分析共情、社交理解能力
    def analyze(self, resource_info):
        pass