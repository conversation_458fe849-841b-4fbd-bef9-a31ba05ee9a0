import asyncio
import os
import time

from app.services.upload_record_audio_service import UploadRecordAudioService

class CleanUploadTmpFileTools:
    
    def __init__(self) -> None:
        pass

    # 清理7天以上上传的临时音频文件
    def clean_upload_audio(self):
        upload_audio_service = UploadRecordAudioService()
        self.clean_old_files(upload_audio_service.tmp_dir)

    def clean_old_files(self, directory, days=7):
        # 计算当前时间戳和7天前的时间戳
        now = time.time()
        cutoff = now - (days * 24 * 60 * 60)

        # 自下而上遍历目录树（先处理子目录）
        for root, dirs, files in os.walk(directory, topdown=False):
            # 处理文件
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    if os.path.isfile(file_path):
                        mtime = os.stat(file_path).st_mtime
                        if mtime < cutoff:
                            os.remove(file_path)
                            print(f"已删除文件：{file_path}")
                except Exception as e:
                    print(f"处理文件 {file_path} 时出错：{str(e)}")

            # 处理目录
            # try:
            #     current_dir = root
            #     # 检查目录是否为空（需要重新扫描防止遍历期间状态变化）
            #     if not os.listdir(current_dir):
            #         # 可选：增加时间条件检查（os.stat(current_dir).st_mtime < cutoff）
            #         os.rmdir(current_dir)
            #         print(f"已删除空目录：{current_dir}")
            # except Exception as e:
            #     print(f"处理目录 {current_dir} 时出错：{str(e)}")

async def main():
    tools = CleanUploadTmpFileTools()
    tools.clean_upload_audio()

if __name__ == "__main__":
    asyncio.run(main())