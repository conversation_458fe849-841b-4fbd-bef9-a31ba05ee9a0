from sqlalchemy import select, and_, or_, func, desc, update
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.screening_detail import ScreeningDetail

class ScreeningDetailService:
    def __init__(self) -> None:
        self.db_session = get_db

    async def new_screening_detail(self, new_screening_detail):
        try:
            async with self.db_session() as session:
                session.add(new_screening_detail)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_screening_detail: {str(e)}")
            raise

        return new_screening_detail


    async def get_screening_detail_by_id(self, id):
        try:
            async with self.db_session() as session:
                stmt = select(ScreeningDetail).where(ScreeningDetail.id == id)
                result = await session.execute(stmt)
                info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_screening_detail_by_id: {str(e)}")
            raise

        return info
    
    async def get_screening_detail_by_screening_info_id(self, screening_info_id):
        try:
            async with self.db_session() as session:
                stmt = select(ScreeningDetail).where(ScreeningDetail.screening_id == id)
                result = await session.execute(stmt)
                info = result.scalars().all()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_screening_detail_by_screening_info_id: {str(e)}")
            raise

        return info
    
    async def update_screening_detail_by_id(self, id, update_data):
        try:
            async with self.db_session() as session:
                stmt = select(ScreeningDetail).where(ScreeningDetail.id == id)
                result = await session.execute(stmt)
                info = result.scalars().first()

                if not info:
                    return False

                await info.update(update_data, session)

        except Exception as e:
            main_logger.error(f"Unexpected error in update_screening_detail_by_id: {str(e)}")
            raise

        return True