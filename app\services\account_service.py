import os
import jwt
from datetime import datetime, timedelta
from sqlalchemy import select, and_, or_, func, desc, update

from app.core.database import get_db
from app.core.logger import main_logger
from app.models.account import Account
from app.services.account_key_service import AccountKeyService
from app.services.account_device_service import AccountDeviceService

class AccountService:

    def __init__(self):
        self.secret = os.getenv("ACCOUNT_SECRET")
        self.db_session = get_db

    def login(self, account_name, password):
        allow_account = {
            "huilongguan_hospital": {"password": "^hlg$", "account_id": 1},
            "copilot_ikang": {"password": "ikang#001", "account_id": 2},
        }
        password_info = allow_account.get(account_name)
        if not password_info:
            return None
        if password_info["password"] != password:
            return None
        
        payload = {
            'account_id': password_info["account_id"],
            'account_name': account_name,
            'exp': datetime.utcnow() + timedelta(hours=8),  # 过期时间
            'iat': datetime.utcnow()  # 发行时间
        }
        token = jwt.encode(payload, self.secret, algorithm='HS256')

        return token

    def check_token(self, token):
        try:
            decoded_payload = jwt.decode(token, self.secret, algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            print("Token has expired")
            return False
        except jwt.PyJWTError:
            print("Invalid token")
            return False
        
        if not decoded_payload:
            return False

        return decoded_payload
    
    async def check_app_key(self, app_key):

        account_key_servcie = AccountKeyService()
        account_key_info = await account_key_servcie.get_account_key_by_app_key(app_key)
        if not account_key_info:
            raise Exception({"code": "10011", "message": "APPKEY状态失效，请检查有效性"})
            return False

        account_info = {"account_id": account_key_info.account_id}

        return account_info
    
    async def new_account(self, new_account):
        try:
            async with self.db_session() as session:
                session.add(new_account)
                await session.commit()
        except Exception as e:
            main_logger.error(f"Unexpected error in new_account: {str(e)}")
            raise

        return new_account
    
    async def get_account_by_account_id(self, account_id):
        try:
            async with self.db_session() as session:
                conditions = [
                    Account.account_id == account_id,
                    Account.status.in_([1])
                ]
                stmt = select(Account).where(and_(*conditions))
                result = await session.execute(stmt)
                result_info = result.scalars().first()
        except Exception as e:
            main_logger.error(f"Unexpected error in get_account_by_account_id: {str(e)}")
            raise

        return result_info

    async def get_acount_info_by_device_id(self, device_id):
        if not device_id:
            return None
        
        account_device_service = AccountDeviceService()
        account_device_info = await account_device_service.get_info_by_device_id(device_id)
        if not account_device_info:
            return None
        
        account_id = account_device_info.account_id
        if not account_id:
            return None
        
        account_info = await self.get_account_by_account_id(account_id)
        if not account_info:
            return None
        
        extra_json = account_info.extra_json

        # config = {
        #     "is_hidden_report": extra_json.get("is_hidden_report", 0)
        # }
        config = extra_json

        return_account_info = {
            "account_id": account_id,
            "name": account_info.name,
            "config": config,
        }

        return return_account_info
                