import os
import logging
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
import dotenv

dotenv.load_dotenv()

class LogConfig:
    def __init__(self):
        """
        初始化日志配置
        """
        log_dir = os.getenv("LOG_DIR")
        self.log_dir = log_dir
        self.ensure_log_dir()
        
    def ensure_log_dir(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
            os.chmod(self.log_dir, 0o777)
            
    def get_logger(self, module_name, level=logging.DEBUG):
        """
        获取指定模块的logger
        :param module_name: 模块名称
        :param level: 日志级别
        :return: logger实例
        """
        # 创建logger
        logger = logging.getLogger(module_name)
        logger.setLevel(level)
        
        # 避免重复添加handler
        if logger.handlers:
            return logger
        
        # 创建日志文件路径
        log_file = os.path.join(self.log_dir, f"{module_name}.log")
        if not os.path.exists(log_file):
            with open(log_file, 'wb') as file_info:
                file_info.write("")
            os.chmod(log_file, 0o777)
        
        # 创建TimedRotatingFileHandler
        file_handler = TimedRotatingFileHandler(
            filename=log_file,
            when='midnight',  # 每天午夜切割
            interval=1,       # 间隔天数
            backupCount=30,   # 保留30天的日志
            encoding='utf-8'
        )
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        # 创建控制台Handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        # 添加handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger

main_logger = LogConfig().get_logger("main")