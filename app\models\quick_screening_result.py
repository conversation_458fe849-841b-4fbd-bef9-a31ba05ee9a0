from sqlalchemy import Column, Integer, String, JSON, DateTime, Text
from app.models.base import Base, TimestampMixin, ModelUpdateMixin


class QuickScreeningResult(Base, TimestampMixin, ModelUpdateMixin):
    __tablename__ = 'quick_screening_results'

    qs_result_id = Column(Integer, primary_key=True)
    user_id = Column(Integer, default=0)
    result_json = Column(JSON, default=dict)
    report_json = Column(JSON, default=dict)
    status = Column(Integer, default=1)
    extra_json = Column(JSON, default=dict)

    def __repr__(self):
        return f"<QuickScreeningResult(qs_result_id={self.qs_result_id}, user_id={self.user_id})>"
