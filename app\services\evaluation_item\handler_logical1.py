
# 逻辑思维评估项目1处理
class HandlerLogical1:
    def __init__(self) -> None:
        pass

    # 通过收集评测的数据，计算需要的报告等数据
    def cal_result_info(self, collect_data):
        collect_data_sample = {
            "start_time": "",
            "run_data": [ # 每轮数据
                {
                    "cost_time": "",
                    "is_right": 1, # 答题是否正确
                    "select_time": "", # 从题目出现到选择答案的时间，阅读题目和反映时间
                    "click_hit_time": ["", ""],
                    "question_type": "", # 问题类型，用来分析哪种类型题目的影响
                },
            ],
            "collect_type": 0, # 操作方式：0点击 1眼动
            "total_score": 5,
            "cost_time": "",
        }