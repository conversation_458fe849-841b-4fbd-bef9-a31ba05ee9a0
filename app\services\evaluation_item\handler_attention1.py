from datetime import datetime
import numpy as np

# 注意力评估项目1处理
class HandlerAttention1:
    def __init__(self) -> None:
        pass

    # 通过收集评测的数据，计算需要的报告等数据
    def cal_result_info(self, collect_data):
        collect_data_sample = {
            "start_time": "",
            "run_data": [ # 每轮数据
                {
                    "cost_time": "",
                    "hit_num": 1, # 击中数量
                    "unhit_num": 2,
                    "click_hit_time": ["", ""],
                    # "click_error_times": 5,
                    # "click_total_times": 7,
                    "distractor_speed": 3, # 干扰项运动速度
                    "distractor_num": 3,
                },
            ],
            "collect_type": 0, # 操作方式：0点击 1眼动
            "total_score": 5,
            "cost_time": "",
        }

        collect_data_sample = {
            "start_time": 1728649921000,
            "end_time": 1728649921000,
            "collect_type": "eyemovement",
            "detail_info": [
                {
                    "start_time": 1728649921000,
                    "end_time": 1728649951000,
                    "gaze_point": [ # obj_type 1目标 2干扰物
                        {"obj_name": "蓝色圆形", "obj_type": 1, "start_time": 1728649930000, "end_time": 1728649930500},
                        {"obj_name": "干扰物", "obj_type": 2, "start_time": 1728649940000, "end_time": 1728649940500},
                        {"obj_name": "红色三角形", "obj_type": 1, "start_time": 1728649950000, "end_time": 1728649950500},
                    ],
                    "is_timeout": 0,
                    "target_obj_num": 2,
                    "distractor_obj_num": 8,
                },
                {
                    "start_time": 1728649952000,
                    "end_time": 1728649977000,
                    "gaze_point": [ # obj_type 1目标 2干扰物
                        {"obj_name": "蓝色圆形", "obj_type": 1, "start_time": 1728649964000, "end_time": 1728649964500},
                        {"obj_name": "干扰物", "obj_type": 2, "start_time": 1728649975000, "end_time": 1728649975500},
                    ],
                    "is_timeout": 1,
                    "target_obj_num": 4,
                    "distractor_obj_num": 8,
                },
            ],
        }

        # collect_data = collect_data_sample

        gaze_point_time_unit = 500 # 500ms
        total_target_gaze_point_time = 0
        total_target_gaze_point_num = 0
        total_gaze_point_num = 0
        total_target_switch_num = 0
        total_switch_num = 0
        reaction_time_list = []
        total_time = collect_data["end_time"] - collect_data["start_time"]

        detail_info_list = collect_data["detail_info"]
        for detail_info in detail_info_list:
            gaze_point_list = detail_info["gaze_point"]
            tmp_switch_num = 0
            tmp_target_switch_num = 0
            reaction_start_time = detail_info["start_time"]
            
            for gaze_point in gaze_point_list:
                if gaze_point["obj_type"] == 1:
                    total_target_gaze_point_time += gaze_point_time_unit
                    total_target_gaze_point_num += 1
                    tmp_target_switch_num += 1
                total_gaze_point_num += 1
                tmp_switch_num += 1
                gaze_point_time = gaze_point["start_time"]
                reaction_diff_time = gaze_point_time - reaction_start_time
                reaction_start_time = gaze_point["end_time"]
                reaction_time_list.append(reaction_diff_time)

            total_target_switch_num += tmp_target_switch_num
            total_switch_num += tmp_switch_num

        sustained_attention = total_target_gaze_point_time / total_time * 100
        choose_attention = total_target_gaze_point_num / total_gaze_point_num * 100
        allocate_attention = total_target_switch_num / total_switch_num * 100
        np_reaction_time_list = np.array(reaction_time_list)
        reaction_speed = np.mean(np_reaction_time_list)

        return_info = {
            "sustained_attention": sustained_attention,
            "choose_attention": choose_attention,
            "allocate_attention": allocate_attention,
            "reaction_speed": reaction_speed,
            "create_time": int(datetime.utcnow().timestamp() * 1000)
        }

        return return_info