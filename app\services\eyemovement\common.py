def is_valid_pos(x, y):
    return x != 0 and y != 0


def calc_distance(x1, y1, x2, y2):
    return ((x1 - x2) ** 2 + (y1 - y2) ** 2) ** 0.5


def calc_center(a, s, e):
    min_x, min_y, max_x, max_y = 0, 0, 0, 0
    for i in range(s, e):
        min_x = min(min_x, a[i]['ax'])
        min_y = min(min_y, a[i]['ay'])
        max_x = max(max_x, a[i]['ax'])
        max_y = max(max_y, a[i]['ay'])
    return (min_x + max_x) / 2, (min_y + max_y) / 2
