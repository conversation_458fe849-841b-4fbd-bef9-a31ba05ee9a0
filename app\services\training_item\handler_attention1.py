from datetime import datetime
import numpy as np

# 注意力评估项目1处理
class HandlerAttention1:
    def __init__(self) -> None:
        pass

    # 通过收集训练的数据，计算需要的报告等数据
    def cal_result_info(self, collect_data):

        collect_data_sample = {
            "start_time": 1728649921000,
            "end_time": 1728649921000,
            "collect_type": "eyemovement",
            "level": 1, #1,2,3 简单、中等、困难
            "gaze_point": [ # obj_type 1目标 2干扰物 area left\right duraiton 凝视时长(ms)
                {"obj_name": "蓝色圆形", "obj_type": 1, "start_time": 1728649930000, "area": "left", "end_time": 1728649932000},
                {"obj_name": "圆形干扰物", "obj_type": 2, "start_time": 1728649940000, "area": "right", "end_time": 1728649942000},
                {"obj_name": "红色三角形", "obj_type": 1, "start_time": 1728649950000, "area": "left", "end_time": 1728649952000},
                {"obj_name": "五角星干扰物", "obj_type": 2, "start_time": 1728649950000, "area": "left", "end_time": 1728649952000},
            ],
            "left_area": {
                "target_obj_num": 1,
                "distractor_obj_num": 2,
            },
            "right_area": {
                "target_obj_num": 1,
                "distractor_obj_num": 4,
            },
        }


        target_obj_num = collect_data["left_area"]["target_obj_num"] + collect_data["right_area"]["target_obj_num"]
        distractor_obj_num = collect_data["left_area"]["distractor_obj_num"] + collect_data["right_area"]["distractor_obj_num"]

        # 计算持续性注意力分数
        high_duration_unit = 5000
        low_duration_unit = 1000
        high_duration = high_duration_unit * target_obj_num
        low_duration = low_duration_unit * target_obj_num
        score_unit = 100000 / (high_duration - low_duration)
        total_gaze_time = 0
        for gaze_info in collect_data["gaze_point"]:
            if gaze_info["obj_type"] == 1:
                total_gaze_time += gaze_info["end_time"] - gaze_info["start_time"]
        if total_gaze_time >= high_duration:
            score = 100
        elif total_gaze_time <= low_duration:
            score = 0
        else:
            score = ((total_gaze_time - low_duration) * score_unit) / 1000
        sustained_attention_score = score

        # 计算选择性注意力分数
        positive_score_unit = 100 / target_obj_num
        negative_score_unit = 100 / distractor_obj_num
        gaze_target_obj_num = 0
        gaze_distractor_obj_num = 0
        for gaze_info in collect_data["gaze_point"]:
            if gaze_info["obj_type"] == 1:
                gaze_target_obj_num += 1
            if gaze_info["obj_type"] == 2:
                gaze_distractor_obj_num += 1
        score = gaze_target_obj_num * positive_score_unit - gaze_distractor_obj_num * negative_score_unit
        choose_attention_score = score

        # 计算分配性注意力分数
        high_duration = 4000
        low_duration = 1000
        score_unit = 100000 / (high_duration - low_duration)
        move_duration = 0
        start_time = collect_data["start_time"]
        for gaze_info in collect_data["gaze_point"]:
            if gaze_info["obj_type"] == 1:
                move_duration += gaze_info["start_time"] - start_time
                start_time = gaze_info["end_time"]
        if move_duration <= low_duration:
            score = 100
        elif move_duration >= high_duration:
            score = 0
        else:
            score = (move_duration - low_duration)  * score_unit / 1000
        allocate_attention_score = score

        # 计算总分
        score = sustained_attention_score * 0.33 + choose_attention_score * 0.33 + allocate_attention_score * 0.34


        level = collect_data["level"]
        total_time = collect_data["end_time"] - collect_data["start_time"]
        score_info = {
            "sustained_attention_score": sustained_attention_score,
            "choose_attention_score": choose_attention_score,
            "allocate_attention_score": allocate_attention_score,
            "score": score,
        }

        return_info = {
            "level": level,
            "total_time": total_time,
            "score": score,
            "score_info": score_info,
            "create_time": int(datetime.utcnow().timestamp() * 1000)
        }
        return return_info
