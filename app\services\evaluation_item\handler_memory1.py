
# 记忆力评估项目1处理
class HandlerMemory1:
    def __init__(self) -> None:
        pass

    # 通过收集评测的数据，计算需要的报告等数据
    def cal_result_info(self, collect_data):
        collect_data_sample = {
            "start_time": "",
            "run_data": [
                {
                    "cost_time": "",
                    "right_num": 1, # 匹配正确数量
                    "wrong_num": 2, # 匹配错误数量
                },
            ],
            "collect_type": 0, # 操作方式：0点击 1眼动
            "total_score": 5,
            "cost_time": "",
        }