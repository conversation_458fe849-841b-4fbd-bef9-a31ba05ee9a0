from fastapi import APIRouter, HTTPException, Request
from fastapi import Depends
from pydantic import BaseModel, Field
import traceback
import json
from typing import Any, Dict

from app.core.decorators import require_user_token
from app.schemas.base import APIResponse
from app.services.user_service import UserService
from app.core.logger import main_logger
from app.services.quick_screening_service import QuickScreeningService


router = APIRouter()

# 获取快筛题目列表
@router.post("/get_question_list")
@require_user_token()
async def get_question_list(request: Request):

    current_user_info = request.state.user
    current_account_info = request.state.account

    quick_screening_service = QuickScreeningService()

    try:
        list = await quick_screening_service.get_list()
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        list = {}

    return APIResponse(
        code=0, message=_("获取成功"), data=list
    )

# 重新快筛
@router.post("/restart")
@require_user_token()
async def restart(request: Request):

    current_user_info = request.state.user
    current_account_info = request.state.account

    quick_screening_service = QuickScreeningService()
    user_service = UserService()

    try:
        user_update_data = {
            "extra_json": {
                "qs": 0
            }
        }
        await user_service.update_user_info_json(current_user_info["user_id"], user_update_data)

    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)

    return APIResponse(
        code=0, message=_("提交成功"), data={}
    )

# 用户提交的回答数据处理
class AnswerRequest(BaseModel):
    collect_answer: Dict[str, Any]
@router.post("/answer")
@require_user_token()
async def answer(request: Request, answer_request: AnswerRequest):
    current_user_info = request.state.user
    current_account_info = request.state.account

    user_service = UserService()
    user_info = await user_service.get_user_by_user_id(current_user_info["user_id"])
    user_extra_json = user_info["extra_json"]
    qs_result_id = user_extra_json.get("qs", 0)
    if qs_result_id:
        return APIResponse(code=10086, message=_("已经完成快筛"))

    quick_screening_service = QuickScreeningService()
    collect_answer = answer_request.collect_answer

    try:
        result = await quick_screening_service.answer_question(collect_answer, current_user_info["user_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10087, message=_("快筛提交出错"))

    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    user_update_data = {
        "extra_json": {
            "qs": data["qs_result_id"],
        }
    }
    await user_service.update_user_info_json(current_user_info["user_id"], user_update_data)

    return APIResponse(
        code=0, message=_("提交成功"), data=data
    )

# 获取报告数据
class GetReportRequest(BaseModel):
    qs_result_id: int
@router.post("/get_report")
@require_user_token()
async def get_report(request: Request, get_report_request: GetReportRequest):
    
    current_user_info = request.state.user
    current_account_info = request.state.account

    qs_result_id = get_report_request.qs_result_id
    quick_screening_service = QuickScreeningService()

    try:
        result = await quick_screening_service.get_report(qs_result_id, current_user_info["user_id"])
    except Exception as e:
        error_stack = traceback.format_exc()
        main_logger.error(error_stack)
        result = None

    if not result:
        return APIResponse(code=10087, message=_("获取报告失败"))

    code = result.get("code", 0)
    msg = result.get("msg", "")
    data = result.get("data", {})
    if code > 0:
        return APIResponse(code=code, message=msg)

    return APIResponse(
        code=0, message=_("提交成功"), data=data
    )
